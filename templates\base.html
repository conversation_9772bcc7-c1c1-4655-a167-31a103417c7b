<!DOCTYPE html>
<html lang="ta">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ஆவண மேலாண்மை அமைப்பு{% endblock %}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }

        .navbar {
            {% if current_user.is_authenticated and current_user.is_admin() %}
                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            {% else %}
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            {% endif %}
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: white;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-badge {
            background-color: rgba(255,255,255,0.2);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .flash-messages {
            max-width: 1200px;
            margin: 1rem auto;
            padding: 0 1rem;
        }

        .flash-message {
            padding: 12px 20px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .flash-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .flash-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .flash-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .flash-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .main-content {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                flex-direction: column;
                gap: 1rem;
                width: 100%;
                text-align: center;
            }

            .user-info {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        {% block extra_css %}{% endblock %}
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    {% if current_user.is_authenticated %}
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="nav-brand">
                {% if current_user.is_admin() %}
                    🔧 {{ t.admin }} ஆவண மேலாண்மை
                {% else %}
                    📄 ஆவண மேலாண்மை
                {% endif %}
            </a>
            <div class="nav-menu">
                {% if current_user.is_admin() %}
                    <a href="{{ url_for('admin_dashboard') }}" class="nav-link">
                        🏠 {{ t.dashboard }}
                    </a>
                    <a href="{{ url_for('view_documents') }}" class="nav-link">
                        📄 அனைத்து ஆவணங்கள்
                    </a>
                {% else %}
                    <a href="{{ url_for('user_dashboard') }}" class="nav-link">
                        🏠 {{ t.dashboard }}
                    </a>
                {% endif %}
                
                <div class="user-info">
                    <span class="user-badge">
                        {% if current_user.is_admin() %}
                            👑 {{ t.admin }}
                        {% else %}
                            👤 {{ t.user }}
                        {% endif %}
                    </span>
                    <span>{{ current_user.full_name }}</span>
                    <a href="{{ url_for('logout') }}" class="nav-link">
                        🚪 {{ t.logout }}
                    </a>
                </div>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message flash-{{ category }}">
                        {% if category == 'success' %}
                            ✅
                        {% elif category == 'error' %}
                            ❌
                        {% elif category == 'warning' %}
                            ⚠️
                        {% elif category == 'info' %}
                            ℹ️
                        {% endif %}
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <div class="main-content">
        {% block content %}{% endblock %}
    </div>

    {% block extra_js %}{% endblock %}
</body>
</html>
