from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, send_from_directory
from flask_sqlalchemy import SQLAlchemy
import secrets
import os
import re
from werkzeug.utils import secure_filename
from datetime import datetime
import subprocess
import platform
from database import db, User, Document, init_database, get_user_documents, get_documents_by_type, search_users, search_documents

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)  # For flash messages

# Database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///user_documents.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Configure upload settings
UPLOAD_FOLDER = 'uploads'
SCANNED_FOLDER = 'scanned'
ALLOWED_EXTENSIONS = {'txt', 'csv', 'json', 'xml', 'png', 'jpg', 'jpeg', 'pdf'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['SCANNED_FOLDER'] = SCANNED_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize database
db.init_app(app)

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(SCANNED_FOLDER, exist_ok=True)

# Initialize database on first run
with app.app_context():
    db.create_all()
    print("Database initialized successfully!")

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def check_scanner_connection():
    """Check if scanner/printer is connected to the system"""
    try:
        if platform.system() == "Windows":
            # Check for WIA (Windows Image Acquisition) devices
            result = subprocess.run(['powershell', '-Command', 
                                   'Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like "*scan*" -or $_.Name -like "*imaging*"}'], 
                                  capture_output=True, text=True, timeout=10)
            scanner_found = "scan" in result.stdout.lower() or "imaging" in result.stdout.lower()
            if scanner_found:
                print("Real scanner found and connected")
                return {'connected': True, 'type': 'real', 'message': '✅ Real scanner detected and ready to scan'}
            else:
                print("No real scanner found")
                return {'connected': False, 'type': 'none', 'message': '❌ No scanner detected. Please use file upload instead.'}
        else:
            # For Linux/Mac, check for SANE devices
            result = subprocess.run(['scanimage', '-L'], capture_output=True, text=True, timeout=10)
            scanner_found = "device" in result.stdout.lower()
            if scanner_found:
                print("Real scanner found and connected")
                return {'connected': True, 'type': 'real', 'message': '✅ Real scanner detected and ready to scan'}
            else:
                print("No real scanner found")
                return {'connected': False, 'type': 'none', 'message': '❌ No scanner detected. Please use file upload instead.'}
    except Exception as e:
        print(f"Scanner check failed: {e}")
        return {'connected': False, 'type': 'none', 'message': '❌ Scanner check failed. Please use file upload instead.'}

def scan_document():
    """Scan a document using the connected scanner"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"scanned_{timestamp}.png"
        filepath = os.path.join(app.config['SCANNED_FOLDER'], filename)
        
        if platform.system() == "Windows":
            try:
                # Use Windows TWAIN interface
                powershell_script = f'''
                Add-Type -AssemblyName System.Windows.Forms
                Add-Type -AssemblyName System.Drawing
                
                $scanner = New-Object -ComObject WIA.DeviceManager
                $device = $scanner.DeviceInfos.Item(1).Connect()
                $item = $device.Items.Item(1)
                
                $image = $item.Transfer()
                $image.SaveFile("{filepath}")
                '''
                result = subprocess.run(['powershell', '-Command', powershell_script], 
                                      capture_output=True, text=True, timeout=30)
                
                if os.path.exists(filepath):
                    return filename
            except:
                # Fallback to mock scanner for testing
                print("Real scanner not available, using mock scanner")
                return create_mock_scan()
        else:
            try:
                # Use SANE for Linux/Mac
                result = subprocess.run(['scanimage', '--format=png', f'--output-file={filepath}'], 
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0 and os.path.exists(filepath):
                    return filename
            except:
                # Fallback to mock scanner for testing
                print("Real scanner not available, using mock scanner")
                return create_mock_scan()
                
        return None
    except Exception as e:
        print(f"Scanning error: {e}")
        return None

def create_mock_scan():
    """Create a mock scanned document for testing when no scanner is available"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple image that looks like a scanned document
        width, height = 800, 1000
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        try:
            # Try to use a default font
            font = ImageFont.truetype("arial.ttf", 40)
            small_font = ImageFont.truetype("arial.ttf", 20)
        except:
            # Fallback to default font if arial is not available
            font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        
        # Draw document content
        draw.text((50, 100), "MOCK SCANNED DOCUMENT", fill='black', font=font)
        draw.text((50, 200), "This is a simulated scan for testing", fill='black', font=small_font)
        draw.text((50, 250), "Date: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'), fill='black', font=small_font)
        
        # Add some lines to make it look more like a document
        for i in range(10):
            y = 350 + i * 50
            draw.line([(50, y), (750, y)], fill='lightgray', width=1)
        
        # Save the mock scan
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"scanned_{timestamp}.png"
        filepath = os.path.join(app.config['SCANNED_FOLDER'], filename)
        
        image.save(filepath)
        return filename
    except ImportError:
        print("PIL not available for mock scanning")
        return None
    except Exception as e:
        print(f"Mock scanning error: {e}")
        return None

def extract_user_data_from_file(file_path):
    """Extract name and city data from uploaded file"""
    users_data = []
    
    # Get file extension to determine if it's a text-based file
    _, ext = os.path.splitext(file_path)
    ext = ext.lower()
    
    # Only try to extract user data from text-based files
    if ext not in ['.txt', '.csv', '.json', '.xml']:
        print(f"Skipping user data extraction for non-text file: {ext}")
        return users_data
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        # Try different patterns to extract name and city
        patterns = [
            # JSON-like pattern: "name": "John", "city": "NYC"
            r'"name"\s*:\s*"([^"]+)"\s*,?\s*"city"\s*:\s*"([^"]+)"',
            # XML pattern: <n>John</n><city>NYC</city>
            r'<n>([^<]+)</n>\s*<city>([^<]+)</city>',
            # Simple text pattern: Name: John City: NYC
            r'[Nn]ame\s*:\s*([A-Za-z\s]+)\s+[Cc]ity\s*:\s*([A-Za-z\s]+)',
            # Tab separated: John	NYC
            r'([A-Za-z\s]+)\t+([A-Za-z\s]+)',
            # Pipe separated: John | NYC
            r'([A-Za-z\s]+)\s*\|\s*([A-Za-z\s]+)',
        ]
        
        # First try CSV format line by line
        lines = content.strip().split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Try comma separation
            parts = [part.strip() for part in line.split(',')]
            if len(parts) == 2 and all(len(part) > 0 and len(part) < 50 for part in parts):
                # Validate that parts contain valid names/cities (letters and spaces only)
                if all(part.replace(' ', '').isalpha() for part in parts):
                    users_data.append({'name': parts[0], 'city': parts[1]})
        
        # If CSV didn't work, try other patterns
        if not users_data:
            for pattern in patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.IGNORECASE)
                if matches:
                    for match in matches:
                        name = match[0].strip()
                        city = match[1].strip()
                        if len(name) > 0 and len(city) > 0 and len(name) < 50 and len(city) < 50:
                            users_data.append({'name': name, 'city': city})
                    break
    
    except Exception as e:
        print(f"Error reading file: {e}")
    
    return users_data

@app.route('/check_scanner')
def check_scanner():
    """API endpoint to check scanner connection"""
    scanner_info = check_scanner_connection()
    return jsonify(scanner_info)

@app.route('/scan_document', methods=['POST'])
def scan_document_route():
    """API endpoint to scan a document"""
    scanner_status = check_scanner_connection()
    if not scanner_status['connected']:
        return jsonify({'success': False, 'message': 'No scanner connected'})
    
    filename = scan_document()
    if filename:
        return jsonify({'success': True, 'filename': filename, 'message': 'Document scanned successfully'})
    else:
        return jsonify({'success': False, 'message': 'Scanning failed'})

@app.route('/process_scanned/<filename>', methods=['POST'])
def process_scanned_document(filename):
    """Process scanned document with user data"""
    name = request.form.get('name', '').strip()
    city = request.form.get('city', '').strip()
    
    if not name or not city:
        flash('Please provide both name and city for the scanned document.', 'error')
        return redirect(url_for('index'))
    
    try:
        # Create or get user
        user = User.query.filter_by(name=name, city=city).first()
        if not user:
            user = User(name=name, city=city, source='scanned')
            db.session.add(user)
            db.session.flush()  # Get the user ID
        
        # Create document record
        file_path = os.path.join(app.config['SCANNED_FOLDER'], filename)
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        
        document = Document(
            filename=filename,
            original_filename=filename,
            file_path=file_path,
            file_type='.png',
            file_size=file_size,
            document_type='scanned',
            user_id=user.id
        )
        
        db.session.add(document)
        db.session.commit()
        
        flash(f'User "{name}" from {city} added with scanned document!', 'success')
        return redirect(url_for('index', success='true'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'Error processing document: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        # Handle the new scanner-first workflow form submission
        name = request.form.get('name', '').strip()
        city = request.form.get('city', '').strip()
        document_path = request.form.get('document_path', '').strip()
        document_type = request.form.get('document_type', '').strip()
        
        if not name or not city:
            flash('Please fill in both name and city fields.', 'error')
            return redirect(url_for('index'))
        
        if not document_path or not document_type:
            flash('Document information is missing. Please scan or upload a document first.', 'error')
            return redirect(url_for('index'))
        
        try:
            # Create or get user
            existing_user = User.query.filter_by(name=name, city=city).first()
            if not existing_user:
                user = User(name=name, city=city, source=document_type)
                db.session.add(user)
                db.session.flush()  # Get user ID
                user_created = True
            else:
                user = existing_user
                user_created = False
            
            # Update the document record to associate it with the user
            if document_type == 'scanned':
                # Find the scanned document by filename
                file_path = os.path.join(app.config['SCANNED_FOLDER'], document_path)
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                
                document = Document(
                    filename=document_path,
                    original_filename=document_path,
                    file_path=os.path.abspath(file_path),
                    file_type='.png',
                    file_size=file_size,
                    document_type='scanned',
                    user_id=user.id
                )
            else:  # uploaded
                # Find the uploaded document by filename
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], document_path)
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                _, ext = os.path.splitext(document_path)
                
                document = Document(
                    filename=document_path,
                    original_filename=document_path,
                    file_path=os.path.abspath(file_path),
                    file_type=ext.lower(),
                    file_size=file_size,
                    document_type='uploaded',
                    user_id=user.id
                )
            
            db.session.add(document)
            db.session.commit()
            
            if user_created:
                flash(f'User "{name}" from {city} added successfully with {document_type} document!', 'success')
            else:
                flash(f'{document_type.title()} document added for existing user "{name}" from {city}!', 'success')
            
            return redirect(url_for('index', success='true'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error saving user and document: {str(e)}', 'error')
    
    # Get all users for display
    users = User.query.order_by(User.added_at.desc()).all()
    return render_template('index.html', users=users)

@app.route('/upload_document', methods=['POST'])
def upload_document():
    """Handle file upload for the scanner-first workflow"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'No file provided'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'})
    
    if not allowed_file(file.filename):
        return jsonify({'success': False, 'message': 'Invalid file type'})
    
    try:
        # Create unique filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        original_filename = secure_filename(file.filename)
        name_part, ext_part = os.path.splitext(original_filename)
        unique_filename = f"{name_part}_{timestamp}{ext_part}"
        
        # Save file
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)
        
        return jsonify({
            'success': True, 
            'filename': unique_filename,
            'message': 'File uploaded successfully'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/documents')
def view_documents():
    """View all stored documents with user information"""
    scanned_docs = Document.query.filter_by(document_type='scanned').all()
    uploaded_docs = Document.query.filter_by(document_type='uploaded').all()
    
    # Convert to dictionaries for template
    all_documents = {
        'scanned': [doc.to_dict() for doc in scanned_docs],
        'uploaded': [doc.to_dict() for doc in uploaded_docs]
    }
    
    return render_template('documents.html', documents=all_documents)

@app.route('/api/search')
def search_api():
    """API endpoint for searching users and documents"""
    query = request.args.get('q', '').strip().lower()
    if not query:
        return jsonify({'users': [], 'documents': []})
    
    # Search users
    matching_users = [user.to_dict() for user in search_users(query)]
    
    # Search documents
    matching_documents = [doc.to_dict() for doc in search_documents(query)]
    
    return jsonify({
        'users': matching_users,
        'documents': matching_documents,
        'total': len(matching_users) + len(matching_documents)
    })

@app.route('/search')
def search_data():
    """Search endpoint for users and documents"""
    query = request.args.get('q', '').strip().lower()
    if not query:
        return jsonify({'users': [], 'documents': []})
    
    # Search users
    matching_users = [user.to_dict() for user in search_users(query)]
    
    # Search documents
    matching_documents = [doc.to_dict() for doc in search_documents(query)]
    
    return jsonify({
        'users': matching_users,
        'documents': matching_documents
    })

@app.route('/clear')
def clear_users():
    """Clear all users and documents"""
    try:
        User.query.delete()
        Document.query.delete()
        db.session.commit()
        flash('All users and documents cleared!', 'info')
    except Exception as e:
        db.session.rollback()
        flash(f'Error clearing data: {str(e)}', 'error')
    return redirect(url_for('index'))

@app.route('/static/scanned/<filename>')
def serve_scanned_file(filename):
    """Serve scanned images from the scanned directory"""
    return send_from_directory(app.config['SCANNED_FOLDER'], filename)

@app.route('/static/uploads/<filename>')
def serve_uploaded_file(filename):
    """Serve uploaded files from the uploads directory"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/user/<int:user_id>/documents')
def view_user_documents(user_id):
    """View all documents for a specific user"""
    user = User.query.get_or_404(user_id)
    user_documents = get_user_documents(user_id)
    
    # Convert to dictionaries for template
    documents = [doc.to_dict() for doc in user_documents]
    
    return render_template('user_documents.html', user=user.to_dict(), documents=documents)

@app.route('/api/users')
def get_users():
    """API endpoint to get all users"""
    users = User.query.order_by(User.added_at.desc()).all()
    return jsonify([user.to_dict() for user in users])

@app.route('/open_directory', methods=['POST'])
def open_directory():
    """Open the scanned documents directory in the default file manager"""
    try:
        scanned_path = os.path.abspath(app.config['SCANNED_FOLDER'])
        
        if platform.system() == "Windows":
            # Use Windows Explorer
            subprocess.run(['explorer', scanned_path], check=True)
        elif platform.system() == "Darwin":  # macOS
            # Use Finder
            subprocess.run(['open', scanned_path], check=True)
        else:  # Linux and other Unix-like systems
            # Try various file managers
            try:
                subprocess.run(['xdg-open', scanned_path], check=True)
            except:
                # Fallback for different Linux file managers
                for cmd in ['nautilus', 'dolphin', 'thunar', 'pcmanfm']:
                    try:
                        subprocess.run([cmd, scanned_path], check=True)
                        break
                    except FileNotFoundError:
                        continue
                else:
                    raise Exception("No suitable file manager found")
        
        return jsonify({'success': True, 'message': 'Directory opened successfully'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/delete_user/<int:user_id>', methods=['POST'])
def delete_user(user_id):
    """Delete a user and all their documents"""
    try:
        user = User.query.get_or_404(user_id)
        
        # Get all documents for this user to delete physical files
        user_documents = Document.query.filter_by(user_id=user_id).all()
        
        # Delete physical files
        for doc in user_documents:
            try:
                if os.path.exists(doc.file_path):
                    os.remove(doc.file_path)
            except Exception as e:
                print(f"Error deleting file {doc.file_path}: {e}")
        
        # Delete database records
        Document.query.filter_by(user_id=user_id).delete()
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({'success': True, 'message': f'User "{user.name}" and all documents deleted successfully'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/edit_user/<int:user_id>', methods=['POST'])
def edit_user(user_id):
    """Edit user details"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        city = data.get('city', '').strip()
        
        if not name or not city:
            return jsonify({'success': False, 'message': 'Name and city are required.'})
        
        user = User.query.get_or_404(user_id)
        user.name = name
        user.city = city
        db.session.commit()
        
        return jsonify({'success': True, 'message': f'User updated to "{name}" from {city}'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
