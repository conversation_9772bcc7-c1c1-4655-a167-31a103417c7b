#!/usr/bin/env python3
"""
Accurate Tamil Jathagam Generator using Swiss Ephemeris
Based on traditional Tamil Nadu astrology with proper astronomical calculations
"""
import json
import math
from datetime import datetime, date, time
from typing import Dict, List, Tuple, Optional

try:
    import swisseph as swe
    from geopy.geocoders import Nominatim
    from timezonefinder import TimezoneFinder
    import pytz
    LIBRARIES_AVAILABLE = True
except ImportError as e:
    print(f"Required libraries not available: {e}")
    LIBRARIES_AVAILABLE = False

# Tamil Zodiac Signs (Raasi) - 12 signs
TAMIL_RAASI = [
    'மேஷம்', 'ரிஷபம்', 'மிதுனம்', 'கடகம்', 'சிம்மம்', 'கன்னி',
    'துலாம்', 'விருச்சிகம்', 'தனுசு', 'மகரம்', 'கும்பம்', 'மீனம்'
]

# Tamil Nakshatras (Stars) - 27 stars
TAMIL_NAKSHATRAS = [
    'அசுவினி', 'பரணி', 'கார்த்திகை', 'ரோகிணி', 'மிருகசீரிடம்', 'திருவாதிரை',
    'புனர்பூசம்', 'பூசம்', 'ஆயில்யம்', 'மகம்', 'பூரம்', 'உத்திரம்',
    'அஸ்தம்', 'சித்திரை', 'சுவாதி', 'விசாகம்', 'அனுஷம்', 'கேட்டை',
    'மூலம்', 'பூராடம்', 'உத்திராடம்', 'திருவோணம்', 'அவிட்டம்', 'சதயம்',
    'பூரட்டாதி', 'உத்திரட்டாதி', 'ரேவதி'
]

# Planets in Tamil
TAMIL_PLANETS = {
    'Sun': 'சூரியன்',
    'Moon': 'சந்திரன்',
    'Mars': 'செவ்வாய்',
    'Mercury': 'புதன்',
    'Jupiter': 'குரு',
    'Venus': 'சுக்கிரன்',
    'Saturn': 'சனி',
    'Rahu': 'ராகு',
    'Ketu': 'கேது'
}

# Houses in Tamil
TAMIL_HOUSES = [
    'லக்னம்', 'தன', 'சகோதர', 'மாதா', 'புத்திர', 'ரிபு',
    'கலத்திர', 'ஆயுள்', 'பாக்கிய', 'கர்ம', 'லாப', 'வ்யய'
]

# Swiss Ephemeris planet constants
PLANET_CONSTANTS = {
    'Sun': swe.SUN,
    'Moon': swe.MOON,
    'Mars': swe.MARS,
    'Mercury': swe.MERCURY,
    'Jupiter': swe.JUPITER,
    'Venus': swe.VENUS,
    'Saturn': swe.SATURN,
    'Rahu': swe.MEAN_NODE,  # Mean North Node
    'Ketu': swe.MEAN_NODE   # Ketu is 180° opposite to Rahu
}

class LocationService:
    """Handle location-based services for accurate coordinates and timezone"""
    
    def __init__(self):
        self.geolocator = Nominatim(user_agent="tamil_jathagam_generator")
        self.tf = TimezoneFinder()
    
    def get_coordinates(self, place_name: str) -> Tuple[float, float]:
        """Get latitude and longitude for a place"""
        try:
            # Add Tamil Nadu, India for better accuracy
            search_query = f"{place_name}, Tamil Nadu, India"
            location = self.geolocator.geocode(search_query)
            
            if location:
                return location.latitude, location.longitude
            else:
                # Fallback to Chennai coordinates
                return 13.0827, 80.2707
        except Exception as e:
            print(f"Location error: {e}")
            # Default to Chennai
            return 13.0827, 80.2707
    
    def get_timezone(self, latitude: float, longitude: float) -> str:
        """Get timezone for coordinates"""
        try:
            timezone_str = self.tf.timezone_at(lat=latitude, lng=longitude)
            return timezone_str or 'Asia/Kolkata'
        except Exception as e:
            print(f"Timezone error: {e}")
            return 'Asia/Kolkata'

class AccurateJathagamGenerator:
    """Generate accurate Tamil Jathagam using Swiss Ephemeris"""
    
    def __init__(self):
        if not LIBRARIES_AVAILABLE:
            raise ImportError("Required libraries (pyswisseph, geopy, timezonefinder) not available")
        
        self.location_service = LocationService()
        # Set Lahiri Ayanamsa (most commonly used in Indian astrology)
        swe.set_sid_mode(swe.SIDM_LAHIRI)
    
    def calculate_julian_day(self, birth_date: date, birth_time: time, timezone_str: str) -> float:
        """Calculate Julian Day Number with timezone correction"""
        try:
            # Create datetime object
            dt = datetime.combine(birth_date, birth_time)
            
            # Apply timezone
            tz = pytz.timezone(timezone_str)
            local_dt = tz.localize(dt)
            utc_dt = local_dt.utctimetuple()
            
            # Calculate Julian Day
            jd = swe.julday(
                utc_dt.tm_year,
                utc_dt.tm_mon,
                utc_dt.tm_mday,
                utc_dt.tm_hour + utc_dt.tm_min/60.0 + utc_dt.tm_sec/3600.0
            )
            
            return jd
        except Exception as e:
            print(f"Julian day calculation error: {e}")
            # Fallback calculation
            return swe.julday(birth_date.year, birth_date.month, birth_date.day, 
                            birth_time.hour + birth_time.minute/60.0)
    
    def calculate_planetary_positions(self, julian_day: float) -> Dict[str, Dict]:
        """Calculate accurate planetary positions using Swiss Ephemeris"""
        positions = {}
        
        for planet_name, planet_const in PLANET_CONSTANTS.items():
            try:
                if planet_name == 'Ketu':
                    # Ketu is 180° opposite to Rahu
                    rahu_pos = swe.calc_ut(julian_day, swe.MEAN_NODE, swe.FLG_SIDEREAL)[0][0]
                    longitude = (rahu_pos + 180) % 360
                else:
                    # Calculate sidereal position
                    result = swe.calc_ut(julian_day, planet_const, swe.FLG_SIDEREAL)
                    longitude = result[0][0]
                
                # Calculate Raasi (zodiac sign)
                raasi_index = int(longitude / 30)
                raasi = TAMIL_RAASI[raasi_index]
                
                # Calculate Nakshatra
                nakshatra_index = int(longitude * 27 / 360)
                nakshatra = TAMIL_NAKSHATRAS[nakshatra_index % 27]
                
                # Calculate degrees within sign
                degrees_in_sign = longitude % 30
                
                # Calculate Nakshatra pada (quarter)
                nakshatra_position = (longitude * 27 / 360) % 1
                pada = int(nakshatra_position * 4) + 1
                
                positions[planet_name] = {
                    'longitude': round(longitude, 4),
                    'raasi': raasi,
                    'nakshatra': nakshatra,
                    'pada': pada,
                    'degrees_in_sign': round(degrees_in_sign, 2),
                    'tamil_name': TAMIL_PLANETS[planet_name]
                }
                
            except Exception as e:
                print(f"Error calculating {planet_name}: {e}")
                # Fallback position
                positions[planet_name] = {
                    'longitude': 0,
                    'raasi': 'மேஷம்',
                    'nakshatra': 'அசுவினி',
                    'pada': 1,
                    'degrees_in_sign': 0,
                    'tamil_name': TAMIL_PLANETS[planet_name]
                }
        
        return positions
    
    def calculate_ascendant(self, julian_day: float, latitude: float, longitude: float) -> Dict:
        """Calculate accurate Ascendant (Lagna) using Swiss Ephemeris"""
        try:
            # Calculate houses using Placidus system
            houses = swe.houses(julian_day, latitude, longitude, b'P')
            ascendant_longitude = houses[1][0]  # Ascendant is the first house cusp
            
            # Convert to sidereal
            ayanamsa = swe.get_ayanamsa(julian_day)
            sidereal_ascendant = (ascendant_longitude - ayanamsa) % 360
            
            # Calculate Raasi and Nakshatra for Ascendant
            raasi_index = int(sidereal_ascendant / 30)
            raasi = TAMIL_RAASI[raasi_index]
            
            nakshatra_index = int(sidereal_ascendant * 27 / 360)
            nakshatra = TAMIL_NAKSHATRAS[nakshatra_index % 27]
            
            degrees_in_sign = sidereal_ascendant % 30
            
            return {
                'longitude': round(sidereal_ascendant, 4),
                'raasi': raasi,
                'nakshatra': nakshatra,
                'degrees_in_sign': round(degrees_in_sign, 2)
            }
            
        except Exception as e:
            print(f"Ascendant calculation error: {e}")
            # Fallback
            return {
                'longitude': 0,
                'raasi': 'மேஷம்',
                'nakshatra': 'அசுவினி',
                'degrees_in_sign': 0
            }
    
    def generate_house_chart(self, ascendant_longitude: float, planetary_positions: Dict) -> Dict:
        """Generate house positions for Rasi chart"""
        houses = {}
        
        # Initialize all houses
        for i in range(12):
            house_name = TAMIL_HOUSES[i]
            houses[house_name] = {
                'number': i + 1,
                'raasi': TAMIL_RAASI[(int(ascendant_longitude / 30) + i) % 12],
                'planets': []
            }
        
        # Place planets in houses
        for planet, data in planetary_positions.items():
            planet_longitude = data['longitude']
            # Calculate which house the planet is in
            house_number = int((planet_longitude - ascendant_longitude) / 30) % 12
            house_name = TAMIL_HOUSES[house_number]
            houses[house_name]['planets'].append(data['tamil_name'])
        
        return houses

    def calculate_navamsa_chart(self, planetary_positions: Dict) -> Dict:
        """Generate Navamsa (D9) chart"""
        navamsa_houses = {}

        # Initialize Navamsa houses
        for i in range(12):
            house_name = TAMIL_HOUSES[i]
            navamsa_houses[house_name] = {
                'number': i + 1,
                'planets': []
            }

        # Calculate Navamsa positions
        for planet, data in planetary_positions.items():
            longitude = data['longitude']

            # Navamsa calculation: Each sign is divided into 9 parts
            sign_number = int(longitude / 30)
            degrees_in_sign = longitude % 30
            navamsa_within_sign = int(degrees_in_sign / (30/9))

            # Calculate Navamsa sign
            if sign_number % 4 == 0:  # Movable signs
                navamsa_sign = (sign_number + navamsa_within_sign) % 12
            elif sign_number % 4 == 1:  # Fixed signs
                navamsa_sign = (sign_number + 8 + navamsa_within_sign) % 12
            else:  # Dual signs
                navamsa_sign = (sign_number + 4 + navamsa_within_sign) % 12

            house_name = TAMIL_HOUSES[navamsa_sign]
            navamsa_houses[house_name]['planets'].append(data['tamil_name'])

        return navamsa_houses

    def generate_accurate_jathagam(self, name: str, birth_date: date, birth_time: time,
                                 birth_place: str, gender: str = "Male") -> Optional[Dict]:
        """Generate complete accurate Jathagam"""
        try:
            # Get coordinates and timezone
            latitude, longitude = self.location_service.get_coordinates(birth_place)
            timezone_str = self.location_service.get_timezone(latitude, longitude)

            # Calculate Julian Day
            julian_day = self.calculate_julian_day(birth_date, birth_time, timezone_str)

            # Calculate planetary positions
            planetary_positions = self.calculate_planetary_positions(julian_day)

            # Calculate Ascendant
            ascendant = self.calculate_ascendant(julian_day, latitude, longitude)

            # Generate charts
            rasi_chart = self.generate_house_chart(ascendant['longitude'], planetary_positions)
            navamsa_chart = self.calculate_navamsa_chart(planetary_positions)

            # Get Moon's position for Rasi and Nakshatra
            moon_data = planetary_positions.get('Moon', {})

            # Create comprehensive Jathagam data
            jathagam = {
                'personal_details': {
                    'name': name,
                    'gender': gender,
                    'birth_date': birth_date.strftime('%Y-%m-%d'),
                    'birth_time': birth_time.strftime('%H:%M'),
                    'birth_place': birth_place,
                    'latitude': round(latitude, 6),
                    'longitude': round(longitude, 6),
                    'timezone': timezone_str,
                    'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                'astrological_details': {
                    'moon_raasi': moon_data.get('raasi', 'தெரியவில்லை'),
                    'moon_nakshatra': moon_data.get('nakshatra', 'தெரியவில்லை'),
                    'moon_pada': moon_data.get('pada', 1),
                    'lagna_raasi': ascendant['raasi'],
                    'lagna_nakshatra': ascendant['nakshatra'],
                    'lagna_degrees': ascendant['degrees_in_sign']
                },
                'planetary_positions': planetary_positions,
                'ascendant': ascendant,
                'rasi_chart': rasi_chart,
                'navamsa_chart': navamsa_chart,
                'calculation_method': 'Swiss Ephemeris with Lahiri Ayanamsa'
            }

            return jathagam

        except Exception as e:
            print(f"Jathagam generation error: {e}")
            return None

def format_jathagam_for_display(jathagam_data: Dict) -> str:
    """Format Jathagam data for beautiful Tamil display"""
    if not jathagam_data:
        return "ஜாதக தகவல் கிடைக்கவில்லை"

    try:
        personal = jathagam_data.get('personal_details', {})
        astro = jathagam_data.get('astrological_details', {})
        planets = jathagam_data.get('planetary_positions', {})

        formatted = f"""
🌟 ஜாதக விவரங்கள் 🌟

📋 அடிப்படை தகவல்கள்:
   பெயர்: {personal.get('name', 'தெரியவில்லை')}
   பாலினம்: {personal.get('gender', 'தெரியவில்லை')}
   பிறந்த தேதி: {personal.get('birth_date', 'தெரியவில்லை')}
   பிறந்த நேரம்: {personal.get('birth_time', 'தெரியவில்லை')}
   பிறந்த இடம்: {personal.get('birth_place', 'தெரியவில்லை')}

🌙 முக்கிய ஜோதிட தகவல்கள்:
   சந்திர ராசி: {astro.get('moon_raasi', 'தெரியவில்லை')}
   நட்சத்திரம்: {astro.get('moon_nakshatra', 'தெரியவில்லை')}
   பாதம்: {astro.get('moon_pada', 'தெரியவில்லை')}
   லக்ன ராசி: {astro.get('lagna_raasi', 'தெரியவில்லை')}
   லக்ன நட்சத்திரம்: {astro.get('lagna_nakshatra', 'தெரியவில்லை')}

🪐 கிரக நிலைகள்:
"""

        for planet, data in planets.items():
            tamil_name = data.get('tamil_name', planet)
            raasi = data.get('raasi', 'தெரியவில்லை')
            nakshatra = data.get('nakshatra', 'தெரியவில்லை')
            degrees = data.get('degrees_in_sign', 0)
            formatted += f"   {tamil_name}: {raasi} - {nakshatra} ({degrees:.1f}°)\n"

        formatted += f"\n📊 கணக்கீட்டு முறை: {jathagam_data.get('calculation_method', 'Swiss Ephemeris')}"

        return formatted

    except Exception as e:
        return f"ஜாதக காட்சி பிழை: {str(e)}"

# Test function
if __name__ == "__main__":
    try:
        generator = AccurateJathagamGenerator()

        # Test with sample data
        test_date = date(1990, 8, 15)
        test_time = time(10, 30)
        test_jathagam = generator.generate_accurate_jathagam(
            "சோதனை", test_date, test_time, "சென்னை", "Male"
        )

        if test_jathagam:
            print("✅ Accurate Jathagam generated successfully!")
            print(format_jathagam_for_display(test_jathagam))
        else:
            print("❌ Failed to generate Jathagam")

    except Exception as e:
        print(f"❌ Test error: {e}")
        print("Note: Ensure pyswisseph, geopy, and timezonefinder are installed")
