#!/usr/bin/env python3
"""
Add Jathagam and birth detail fields to Document table
"""
import os
import sys
import sqlite3

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def migrate_database():
    """Add new columns to the database"""
    db_path = os.path.join(os.getcwd(), 'instance', 'user_documents.db')
    
    print(f"Adding Jathagam fields to database at: {db_path}")
    
    try:
        # Connect to SQLite database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check existing columns
        cursor.execute("PRAGMA table_info(document)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"Existing columns: {columns}")
        
        # Add missing columns
        new_columns = [
            ('birth_date', 'DATE'),
            ('birth_time', 'TIME'),
            ('birth_place', 'VARCHAR(100)'),
            ('jathagam_data', 'TEXT'),
            ('has_jathagam', 'BOOLEAN DEFAULT 0')
        ]
        
        for column_name, column_type in new_columns:
            if column_name not in columns:
                print(f"Adding {column_name} column...")
                cursor.execute(f"ALTER TABLE document ADD COLUMN {column_name} {column_type}")
            else:
                print(f"{column_name} column already exists")
        
        # Commit changes
        conn.commit()
        
        # Verify columns were added
        cursor.execute("PRAGMA table_info(document)")
        new_columns_list = [row[1] for row in cursor.fetchall()]
        print(f"Updated columns: {new_columns_list}")
        
        conn.close()
        print("✅ Database migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Error migrating database: {e}")
        return False
    
    return True

if __name__ == "__main__":
    migrate_database()
