#!/usr/bin/env python3
"""
Test the separated Jathagam functionality
"""
import os
import sys
import requests
import json

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_file_upload_without_jathagam():
    """Test that file upload no longer includes Jathagam generation"""
    print("=== Testing File Upload (No Jathagam) ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Create a test file
        test_file_content = "Test document without Jathagam"
        
        # Prepare upload data (no birth details or Jathagam generation)
        files = {
            'file': ('test_no_jathagam.txt', test_file_content, 'text/plain')
        }
        
        data = {
            'name': 'சோதனை பயனர்',
            'city': 'சென்னை',
            'natchathiram': 'ரோகிணி',
            'raasi': 'ரிஷபம்',
            'vayathu': '25'
        }
        
        response = session.post('http://localhost:5000/admin_upload', files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ File upload successful (without Jathagam)")
                # Verify no Jathagam was generated
                if 'jathagam_generated' not in result:
                    print("✅ No Jathagam generation in upload process")
                    return True
                else:
                    print("❌ Jathagam generation still present in upload")
                    return False
            else:
                print(f"❌ Upload failed: {result.get('message')}")
                return False
        else:
            print(f"❌ Upload request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Upload test error: {e}")
        return False

def test_standalone_jathagam_generation():
    """Test standalone Jathagam generation"""
    print("\n=== Testing Standalone Jathagam Generation ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Generate standalone Jathagam
        data = {
            'name': 'ஜாதக சோதனை',
            'birth_date': '1990-08-15',
            'birth_time': '10:30',
            'birth_place': 'சென்னை'
        }
        
        response = session.post('http://localhost:5000/generate_standalone_jathagam', data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Standalone Jathagam generation successful!")
                jathagam = result.get('jathagam')
                if jathagam:
                    print(f"   Name: {jathagam.get('name')}")
                    print(f"   Moon Raasi: {jathagam.get('moon_raasi')}")
                    print(f"   Moon Nakshatra: {jathagam.get('moon_nakshatra')}")
                    print(f"   Lagna Raasi: {jathagam.get('lagna_raasi')}")
                    return True
                else:
                    print("❌ No Jathagam data in response")
                    return False
            else:
                print(f"❌ Jathagam generation failed: {result.get('message')}")
                return False
        else:
            print(f"❌ Jathagam generation request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Jathagam generation test error: {e}")
        return False

def test_jathagam_list():
    """Test Jathagam list functionality"""
    print("\n=== Testing Jathagam List ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Get Jathagam list
        response = session.get('http://localhost:5000/get_jathagam_list')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                jathagams = result.get('jathagams', [])
                print(f"✅ Jathagam list retrieved: {len(jathagams)} Jathagams found")
                
                for jathagam in jathagams[:3]:  # Show first 3
                    print(f"   - {jathagam.get('user_name', 'Unknown')} ({jathagam.get('birth_date', 'No date')})")
                
                return True
            else:
                print(f"❌ Jathagam list failed: {result.get('message')}")
                return False
        else:
            print(f"❌ Jathagam list request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Jathagam list test error: {e}")
        return False

def test_admin_dashboard_actions():
    """Test that admin dashboard has separate Jathagam actions"""
    print("\n=== Testing Admin Dashboard Actions ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Get admin dashboard
        response = session.get('http://localhost:5000/admin_dashboard')
        
        if response.status_code == 200:
            content = response.text
            
            # Check for separate Jathagam actions
            if 'ஜாதகம் உருவாக்கு' in content and 'ஜாதக பட்டியல்' in content:
                print("✅ Admin dashboard has separate Jathagam actions")
                
                # Check that upload doesn't mention Jathagam
                if 'கோப்பு பதிவேற்று' in content:
                    print("✅ File upload action present")
                    return True
                else:
                    print("❌ File upload action missing")
                    return False
            else:
                print("❌ Jathagam actions missing from dashboard")
                return False
        else:
            print(f"❌ Admin dashboard request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Separated Jathagam Functionality...")
    
    tests = [
        test_file_upload_without_jathagam,
        test_standalone_jathagam_generation,
        test_jathagam_list,
        test_admin_dashboard_actions
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All separation tests passed!")
        print("\n✅ Successfully Separated Features:")
        print("  📤 File Upload: Clean upload without Jathagam generation")
        print("  🌟 Standalone Jathagam: Independent horoscope generation")
        print("  📋 Jathagam Management: List and view generated Jathagams")
        print("  🎛️ Admin Dashboard: Separate action cards for each feature")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
