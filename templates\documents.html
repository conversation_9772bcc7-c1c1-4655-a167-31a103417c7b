<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Archive - User Data Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .nav-buttons {
            margin-top: 20px;
        }

        .nav-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            margin: 0 10px;
            display: inline-block;
            transition: all 0.3s ease;
        }        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .view-btn {
            background: linear-gradient(45deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(72, 187, 120, 0.3);
        }

        .view-btn:hover {
            background: linear-gradient(45deg, #38a169, #2f855a);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
        }

        .directory-btn {
            background: linear-gradient(45deg, #ed8936, #dd6b20);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(237, 137, 54, 0.3);
        }

        .directory-btn:hover {
            background: linear-gradient(45deg, #dd6b20, #c05621);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(237, 137, 54, 0.4);
        }        .document-actions {
            padding: 15px;
            text-align: center;
            border-top: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 0 0 15px 15px;
            margin-top: 10px;
        }

        .section-header {
            grid-column: 1 / -1;
            margin: 20px 0 10px 0;
        }

        .section-header h2 {
            color: #333;
            font-size: 1.5em;
            margin: 0;
            padding: 15px 0;
            border-bottom: 2px solid #667eea;
            background: white;
            border-radius: 10px;
            padding-left: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .document-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            border: 2px solid #e1e5e9;
        }

        .file-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }

        .file-type {
            font-size: 14px;
            font-weight: 600;
            color: #666;
        }

        .document-stats {
            font-size: 12px;
            color: #888;
            margin: 5px 0;
        }

        .document-stats div {
            margin: 2px 0;
        }

        .document-type {
            background: #e8f4fd;
            color: #1976d2;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-top: 8px;
            display: inline-block;
        }

        .scanned-doc .document-type {
            background: #e8f5e8;
            color: #2e7d2e;
        }

        .uploaded-doc .document-type {
            background: #fff3e0;
            color: #f57c00;
        }

        .search-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .search-box {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }        .document-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .document-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .document-card.hidden {
            display: none;
        }        .document-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 15px;
            border: 2px solid #e1e5e9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .document-image:hover {
            transform: scale(1.02);
            border-color: #667eea;
        }

        .document-info {
            margin-bottom: 15px;
        }

        .user-name {
            font-size: 18px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .user-city {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .document-date {
            color: #999;
            font-size: 12px;
        }

        .document-filename {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            margin-top: 10px;
            display: inline-block;
        }

        .stats {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .empty-state {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .empty-state h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .modal-content {
            margin: auto;
            display: block;
            max-width: 90%;
            max-height: 90%;
            margin-top: 50px;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #bbb;
        }

        .search-results-info {
            background: #e3f2fd;
            color: #1976d2;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header, .search-section, .stats {
                padding: 20px;
            }
            
            .documents-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .nav-btn {
                display: block;
                margin: 5px 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">        <div class="header">
            <h1>📄 Document Archive</h1>
            <p>View and search all scanned and uploaded documents with user information</p>            <div class="nav-buttons">
                <a href="/" class="nav-btn">🏠 Home</a>
                <a href="#" class="nav-btn" onclick="refreshDocuments()">🔄 Refresh</a>
                <a href="#" class="directory-btn" onclick="openScannedDirectory()">📁 Open Directory</a>
            </div>
        </div><div class="stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number" id="totalDocs">{{ (documents.scanned|length) + (documents.uploaded|length) }}</span>
                    <span class="stat-label">Total Documents</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="scannedDocs">{{ documents.scanned|length }}</span>
                    <span class="stat-label">Scanned Documents</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="uploadedDocs">{{ documents.uploaded|length }}</span>
                    <span class="stat-label">Uploaded Documents</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="uniqueUsers">0</span>
                    <span class="stat-label">Unique Users</span>
                </div>
            </div>
        </div>        <div class="search-section">
            <input type="text" class="search-box" id="searchInput" placeholder="🔍 Search by name, city, filename, or document type..." onkeyup="searchDocuments(this.value)">
            <div class="search-results-info" id="searchInfo"></div>
        </div><div class="documents-grid" id="documentsGrid">
            <!-- Scanned Documents Section -->
            {% if documents.scanned %}
                <div class="section-header">
                    <h2>📷 Scanned Documents</h2>
                </div>
                {% for doc in documents.scanned %}
                    <div class="document-card scanned-doc" 
                         data-name="{{ doc.user_name.lower() }}" 
                         data-city="{{ doc.user_city.lower() }}" 
                         data-filename="{{ doc.filename.lower() }}"
                         data-type="scanned">
                        <img src="/static/scanned/{{ doc.filename }}" 
                             alt="Scanned Document" 
                             class="document-image"
                             onerror="this.style.display='none'"
                             onclick="openModal('/static/scanned/{{ doc.filename }}')">                        <div class="document-info">
                            <div class="user-name">👤 {{ doc.user_name }}</div>
                            <div class="user-city">📍 {{ doc.user_city }}</div>
                            <div class="document-date">📅 Scanned: {{ doc.created_at }}</div>
                            <div class="document-type">📷 Scanned Document</div>
                        </div>
                        <div class="document-filename">📄 {{ doc.filename }}</div>
                        <div class="document-actions">
                            <a href="/static/scanned/{{ doc.filename }}" target="_blank" class="view-btn">👁️ View Document</a>
                            <a href="#" onclick="openModal('/static/scanned/{{ doc.filename }}')" class="view-btn">🔍 Quick View</a>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Uploaded Documents Section -->
            {% if documents.uploaded %}
                <div class="section-header">
                    <h2>📁 Uploaded Documents</h2>
                </div>
                {% for doc in documents.uploaded %}
                    <div class="document-card uploaded-doc" 
                         data-filename="{{ doc.filename.lower() }}"
                         data-original="{{ doc.original_filename.lower() }}"
                         data-type="uploaded">
                        
                        {% if doc.file_type in ['.png', '.jpg', '.jpeg'] %}
                            <img src="/static/uploads/{{ doc.filename }}" 
                                 alt="Uploaded Document" 
                                 class="document-image"
                                 onerror="this.style.display='none'"
                                 onclick="openModal('/static/uploads/{{ doc.filename }}')">
                        {% else %}
                            <div class="document-placeholder">
                                <div class="file-icon">
                                    {% if doc.file_type == '.pdf' %}📄
                                    {% elif doc.file_type in ['.txt', '.csv'] %}📝
                                    {% elif doc.file_type == '.json' %}⚙️
                                    {% elif doc.file_type == '.xml' %}🗂️
                                    {% else %}📋{% endif %}
                                </div>
                                <div class="file-type">{{ doc.file_type.upper() }} File</div>
                            </div>
                        {% endif %}
                          <div class="document-info">
                            <div class="user-name">📄 {{ doc.original_filename }}</div>
                            <div class="document-date">📅 Uploaded: {{ doc.created_at }}</div>
                            <div class="document-stats">
                                <div>📏 {{ "%.1f"|format(doc.file_size/1024) }} KB</div>
                            </div>
                            <div class="document-type">📁 Uploaded Document</div>
                        </div>
                        <div class="document-filename">📄 {{ doc.filename }}</div>
                        <div class="document-actions">
                            <a href="/static/uploads/{{ doc.filename }}" target="_blank" class="view-btn">👁️ View Document</a>
                            <a href="/static/uploads/{{ doc.filename }}" download="{{ doc.original_filename }}" class="view-btn">⬇️ Download</a>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Empty State -->
            {% if not documents.scanned and not documents.uploaded %}
                <div class="empty-state">
                    <h3>No Documents Found</h3>
                    <p>No documents have been processed yet.</p>
                    <p>Go to the <a href="/" style="color: #667eea;">home page</a> to scan or upload documents.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Modal for viewing full-size images -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    <script>        // Calculate statistics
        function calculateStats() {
            const scannedDocs = document.querySelectorAll('.document-card.scanned-doc');
            const uploadedDocs = document.querySelectorAll('.document-card.uploaded-doc');
            const uniqueUsers = new Set();

            // Count unique users from scanned documents
            scannedDocs.forEach(doc => {
                const userName = doc.querySelector('.user-name');
                if (userName) {
                    const name = userName.textContent.replace('👤 ', '').trim();
                    if (name) uniqueUsers.add(name);
                }
            });

            document.getElementById('uniqueUsers').textContent = uniqueUsers.size;
        }function searchDocuments(searchTerm) {
            const cards = document.querySelectorAll('.document-card');
            const searchInfo = document.getElementById('searchInfo');
            const term = searchTerm.toLowerCase();
            let visibleCount = 0;

            cards.forEach(card => {
                const name = card.getAttribute('data-name') || '';
                const city = card.getAttribute('data-city') || '';
                const filename = card.getAttribute('data-filename') || '';
                const original = card.getAttribute('data-original') || '';
                const type = card.getAttribute('data-type') || '';

                if (!term || 
                    name.includes(term) || 
                    city.includes(term) || 
                    filename.includes(term) || 
                    original.includes(term) ||
                    type.includes(term)) {
                    card.classList.remove('hidden');
                    visibleCount++;
                } else {
                    card.classList.add('hidden');
                }
            });

            if (term) {
                searchInfo.style.display = 'block';
                searchInfo.textContent = `Found ${visibleCount} document(s) matching "${searchTerm}"`;
            } else {
                searchInfo.style.display = 'none';
            }
        }

        function openModal(imageSrc) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.style.display = 'block';
            modalImg.src = imageSrc;
        }

        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }        function refreshDocuments() {
            window.location.reload();
        }

        function openScannedDirectory() {
            // Try to open the directory in the default file manager
            fetch('/open_directory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Directory opened successfully!', 'success');
                } else {
                    showToast('Could not open directory: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error opening directory:', error);
                showToast('Error opening directory. Please navigate to the scanned folder manually.', 'error');
            });
        }

        function showToast(message, type = 'info') {
            // Create toast notification
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#4299e1'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1001;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            
            document.body.appendChild(toast);
            
            // Animate in
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);
            
            // Remove after 3 seconds
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target == modal) {
                closeModal();
            }
        }

        // Initialize statistics on page load
        document.addEventListener('DOMContentLoaded', function() {
            calculateStats();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
            if (event.ctrlKey && event.key === 'f') {
                event.preventDefault();
                document.getElementById('searchInput').focus();
            }
        });
    </script>
</body>
</html>