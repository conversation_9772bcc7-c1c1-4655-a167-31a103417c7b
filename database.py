from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os

db = SQLAlchemy()

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    city = db.Column(db.String(100), nullable=False)
    added_at = db.Column(db.DateTime, default=datetime.utcnow)
    source = db.Column(db.String(20), nullable=False)  # 'manual', 'file', 'scanned'
    
    # Relationships
    documents = db.relationship('Document', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<User {self.name} from {self.city}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'city': self.city,
            'added_at': self.added_at.strftime('%Y-%m-%d %H:%M:%S'),
            'source': self.source,
            'document_count': len(self.documents)
        }

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=True)
    file_path = db.Column(db.String(500), nullable=False)
    file_type = db.Column(db.String(10), nullable=False)
    file_size = db.Column(db.Integer, nullable=True)
    document_type = db.Column(db.String(20), nullable=False)  # 'scanned', 'uploaded'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Foreign key
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    
    def __repr__(self):
        return f'<Document {self.filename}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_path': self.file_path,
            'file_type': self.file_type,
            'file_size': self.file_size,
            'document_type': self.document_type,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'user_id': self.user_id,
            'user_name': self.user.name if self.user else None,
            'user_city': self.user.city if self.user else None
        }

def init_database(app):
    """Initialize the database"""
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")

def get_user_documents(user_id):
    """Get all documents for a specific user"""
    return Document.query.filter_by(user_id=user_id).all()

def get_documents_by_type(document_type):
    """Get all documents of a specific type"""
    return Document.query.filter_by(document_type=document_type).all()

def search_users(query):
    """Search users by name or city"""
    return User.query.filter(
        db.or_(
            User.name.ilike(f'%{query}%'),
            User.city.ilike(f'%{query}%')
        )
    ).all()

def search_documents(query):
    """Search documents by filename or user info"""
    return Document.query.join(User).filter(
        db.or_(
            Document.filename.ilike(f'%{query}%'),
            Document.original_filename.ilike(f'%{query}%'),
            User.name.ilike(f'%{query}%'),
            User.city.ilike(f'%{query}%')
        )
    ).all()
