from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
import os

db = SQLAlchemy()

class AuthUser(UserMixin, db.Model):
    """Authentication user model for login/signup"""
    __tablename__ = 'auth_users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='user')  # 'user' or 'admin'
    full_name = db.Column(db.String(100), nullable=False)
    city = db.Column(db.String(100), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Bo<PERSON>, default=True)

    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        """Check if user is admin"""
        return self.role == 'admin'

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'full_name': self.full_name,
            'city': self.city,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'is_active': self.is_active
        }

    def __repr__(self):
        return f'<AuthUser {self.username}>'

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    city = db.Column(db.String(100), nullable=False)
    added_at = db.Column(db.DateTime, default=datetime.utcnow)
    source = db.Column(db.String(20), nullable=False)  # 'manual', 'file', 'scanned'
    
    # Relationships
    documents = db.relationship('Document', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<User {self.name} from {self.city}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'city': self.city,
            'added_at': self.added_at.strftime('%Y-%m-%d %H:%M:%S'),
            'source': self.source,
            'document_count': len(self.documents)
        }

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=True)
    file_path = db.Column(db.String(500), nullable=False)
    file_type = db.Column(db.String(10), nullable=False)
    file_size = db.Column(db.Integer, nullable=True)
    document_type = db.Column(db.String(20), nullable=False)  # 'scanned', 'uploaded'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Additional fields for scanned documents
    natchathiram = db.Column(db.String(50), nullable=True)  # நட்சத்திரம்
    raasi = db.Column(db.String(50), nullable=True)  # ராசி
    vayathu = db.Column(db.Integer, nullable=True)  # வயது

    # Foreign key
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    
    def __repr__(self):
        return f'<Document {self.filename}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_path': self.file_path,
            'file_type': self.file_type,
            'file_size': self.file_size,
            'document_type': self.document_type,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'natchathiram': self.natchathiram,
            'raasi': self.raasi,
            'vayathu': self.vayathu,
            'user_id': self.user_id,
            'user_name': self.user.name if self.user else None,
            'user_city': self.user.city if self.user else None
        }

def init_database(app):
    """Initialize the database"""
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")

def get_user_documents(user_id):
    """Get all documents for a specific user"""
    return Document.query.filter_by(user_id=user_id).all()

def get_documents_by_type(document_type):
    """Get all documents of a specific type"""
    return Document.query.filter_by(document_type=document_type).all()

def search_users(query):
    """Search users by name or city"""
    return User.query.filter(
        db.or_(
            User.name.ilike(f'%{query}%'),
            User.city.ilike(f'%{query}%')
        )
    ).all()

def search_documents(query):
    """Search documents by filename or user info"""
    return Document.query.join(User).filter(
        db.or_(
            Document.filename.ilike(f'%{query}%'),
            Document.original_filename.ilike(f'%{query}%'),
            User.name.ilike(f'%{query}%'),
            User.city.ilike(f'%{query}%'),
            Document.natchathiram.ilike(f'%{query}%'),
            Document.raasi.ilike(f'%{query}%')
        )
    ).all()

def advanced_search_documents(filters):
    """Advanced search with multiple filters"""
    query = Document.query.join(User, isouter=True)

    if filters.get('name'):
        query = query.filter(User.name.ilike(f"%{filters['name']}%"))

    if filters.get('city'):
        query = query.filter(User.city.ilike(f"%{filters['city']}%"))

    if filters.get('natchathiram'):
        query = query.filter(Document.natchathiram.ilike(f"%{filters['natchathiram']}%"))

    if filters.get('raasi'):
        query = query.filter(Document.raasi.ilike(f"%{filters['raasi']}%"))

    if filters.get('vayathu_min'):
        query = query.filter(Document.vayathu >= int(filters['vayathu_min']))

    if filters.get('vayathu_max'):
        query = query.filter(Document.vayathu <= int(filters['vayathu_max']))

    if filters.get('document_type'):
        query = query.filter(Document.document_type == filters['document_type'])

    if filters.get('date_from'):
        query = query.filter(Document.created_at >= filters['date_from'])

    if filters.get('date_to'):
        query = query.filter(Document.created_at <= filters['date_to'])

    return query.all()

def get_search_suggestions():
    """Get suggestions for search autocomplete"""
    natchathiram_list = db.session.query(Document.natchathiram).filter(Document.natchathiram.isnot(None)).distinct().all()
    raasi_list = db.session.query(Document.raasi).filter(Document.raasi.isnot(None)).distinct().all()

    return {
        'natchathiram': [n[0] for n in natchathiram_list if n[0]],
        'raasi': [r[0] for r in raasi_list if r[0]]
    }
