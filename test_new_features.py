#!/usr/bin/env python3
"""
Test the new search and astrological features
"""
import os
import sys
import requests
import json

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_admin_login_and_dashboard():
    """Test admin login and dashboard access"""
    print("=== Testing Admin Login and Dashboard ===")
    
    session = requests.Session()
    
    # Test login
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post('http://localhost:5000/login', data=login_data)
    print(f"Login status: {response.status_code}")
    
    if response.status_code == 200:
        # Test admin dashboard
        dashboard_response = session.get('http://localhost:5000/admin_dashboard')
        print(f"Admin dashboard status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            print("✅ Admin login and dashboard working!")
            return True
        else:
            print("❌ Admin dashboard failed")
            return False
    else:
        print("❌ Admin login failed")
        return False

def test_user_login_and_dashboard():
    """Test user login and dashboard access"""
    print("\n=== Testing User Login and Dashboard ===")
    
    session = requests.Session()
    
    # Test login
    login_data = {
        'username': 'user',
        'password': 'user123'
    }
    
    response = session.post('http://localhost:5000/login', data=login_data)
    print(f"Login status: {response.status_code}")
    
    if response.status_code == 200:
        # Test user dashboard
        dashboard_response = session.get('http://localhost:5000/user_dashboard')
        print(f"User dashboard status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            print("✅ User login and dashboard working!")
            return True
        else:
            print("❌ User dashboard failed")
            return False
    else:
        print("❌ User login failed")
        return False

def test_search_functionality():
    """Test search functionality"""
    print("\n=== Testing Search Functionality ===")
    
    session = requests.Session()
    
    # Login as admin
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    session.post('http://localhost:5000/login', data=login_data)
    
    # Test advanced search
    search_params = {
        'name': 'test',
        'natchathiram': 'அசுவினி',
        'raasi': 'மேஷம்'
    }
    
    response = session.get('http://localhost:5000/advanced_search', params=search_params)
    print(f"Search status: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"Search results: {data.get('count', 0)} documents found")
            print("✅ Search functionality working!")
            return True
        except:
            print("❌ Search response not JSON")
            return False
    else:
        print("❌ Search failed")
        return False

def test_database_schema():
    """Test database schema"""
    print("\n=== Testing Database Schema ===")
    
    try:
        from app import app
        from database import db, Document
        
        with app.app_context():
            # Try to query with new fields
            docs = Document.query.all()
            print(f"Documents found: {len(docs)}")
            
            # Check if we can access new fields
            for doc in docs:
                print(f"Document: {doc.filename}")
                print(f"  நட்சத்திரம்: {doc.natchathiram}")
                print(f"  ராசி: {doc.raasi}")
                print(f"  வயது: {doc.vayathu}")
                break  # Just check first document
            
            print("✅ Database schema working!")
            return True
            
    except Exception as e:
        print(f"❌ Database schema error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing New Features...")
    
    tests = [
        test_database_schema,
        test_admin_login_and_dashboard,
        test_user_login_and_dashboard,
        test_search_functionality
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All tests passed! The application is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
