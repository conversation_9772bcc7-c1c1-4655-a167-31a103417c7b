#!/usr/bin/env python3
"""
Test the precise chart generation logic following the exact specifications
"""
import os
import sys
import requests
import json
from datetime import date, time

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_precise_chart_generator():
    """Test the precise chart generator with exact logical architecture"""
    print("=== Testing Precise Chart Generator ===")
    
    try:
        from precise_chart_generator import PreciseChartGenerator
        
        # Test data
        test_name = "துல்லிய சோதனை"
        test_date = date(1990, 8, 15)
        test_time = time(10, 30)
        test_place = "சென்னை"
        test_gender = "Male"
        
        # Generate precise charts
        generator = PreciseChartGenerator()
        chart_data = generator.generate_precise_charts(
            test_name, test_date, test_time, test_place, test_gender
        )
        
        if chart_data:
            print("✅ Precise chart generation successful!")
            
            # Test STEP 1: Input parameters
            personal = chart_data.get('personal_details', {})
            print(f"   Name: {personal.get('name')}")
            print(f"   Coordinates: {personal.get('latitude')}, {personal.get('longitude')}")
            print(f"   Timezone: {personal.get('timezone')}")
            
            # Test STEP 2: Ephemeris calculations
            planets = chart_data.get('planetary_positions', {})
            print(f"   Planets calculated: {len(planets)}")
            
            # Test STEP 3: Rasi chart logic
            rasi_chart = chart_data.get('rasi_chart', {})
            print(f"   Rasi chart houses: {len(rasi_chart)}")
            
            # Verify each house has proper structure
            for house_key, house_data in rasi_chart.items():
                if 'rasi_name' in house_data and 'planets' in house_data:
                    continue
                else:
                    print(f"❌ House {house_key} missing required fields")
                    return False
            
            # Test STEP 4: Navamsa chart logic
            navamsa_chart = chart_data.get('navamsa_chart', {})
            print(f"   Navamsa chart houses: {len(navamsa_chart)}")
            
            # Test ascendant calculation
            ascendant = chart_data.get('ascendant', {})
            print(f"   Lagna Raasi: {ascendant.get('raasi')}")
            print(f"   Lagna Degrees: {ascendant.get('degrees_in_sign')}")
            
            return True
        else:
            print("❌ Precise chart generation failed")
            return False
            
    except ImportError as e:
        print(f"❌ Required libraries not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Precise chart test error: {e}")
        return False

def test_navamsa_calculation_logic():
    """Test the specific Navamsa calculation logic"""
    print("\n=== Testing Navamsa Calculation Logic ===")
    
    try:
        from precise_chart_generator import PreciseChartGenerator
        
        generator = PreciseChartGenerator()
        
        # Test different Rasi positions for Navamsa calculation
        test_cases = [
            (15.0, "Aries - Fire sign forward"),      # 15° in Aries
            (45.0, "Taurus - Earth sign forward"),    # 15° in Taurus  
            (75.0, "Gemini - Air sign backward"),     # 15° in Gemini
            (105.0, "Cancer - Water sign backward"),  # 15° in Cancer
            (135.0, "Leo - Fire sign forward"),       # 15° in Leo
            (165.0, "Virgo - Earth sign forward"),    # 15° in Virgo
        ]
        
        for longitude, description in test_cases:
            navamsa_number, navamsa_name = generator.calculate_navamsa_rasi(longitude)
            print(f"   {description}: {longitude}° → {navamsa_name} (#{navamsa_number})")
        
        print("✅ Navamsa calculation logic working!")
        return True
        
    except Exception as e:
        print(f"❌ Navamsa calculation test error: {e}")
        return False

def test_precise_chart_visualizer():
    """Test the precise chart visualizer"""
    print("\n=== Testing Precise Chart Visualizer ===")
    
    try:
        from precise_chart_visualizer import PreciseChartVisualizer
        
        # Sample chart data
        sample_chart_data = {
            'personal_details': {
                'name': 'விஷுவலைசர் சோதனை',
                'birth_date': '1990-08-15',
                'birth_time': '10:30',
                'birth_place': 'சென்னை'
            },
            'astrological_details': {
                'moon_raasi': 'ரிஷபம்',
                'moon_nakshatra': 'ரோகிணி',
                'lagna_raasi': 'துலாம்'
            },
            'rasi_chart': {
                'House_1': {'rasi_name': 'துலாம்', 'planets': ['சூரியன்']},
                'House_2': {'rasi_name': 'விருச்சிகம்', 'planets': ['சந்திரன்']},
                'House_3': {'rasi_name': 'தனுசு', 'planets': []},
                'House_4': {'rasi_name': 'மகரம்', 'planets': ['குரு']},
                'House_5': {'rasi_name': 'கும்பம்', 'planets': []},
                'House_6': {'rasi_name': 'மீனம்', 'planets': ['செவ்வாய்']},
                'House_7': {'rasi_name': 'மேஷம்', 'planets': ['சுக்கிரன்']},
                'House_8': {'rasi_name': 'ரிஷபம்', 'planets': []},
                'House_9': {'rasi_name': 'மிதுனம்', 'planets': ['சனி']},
                'House_10': {'rasi_name': 'கடகம்', 'planets': []},
                'House_11': {'rasi_name': 'சிம்மம்', 'planets': ['ராகு']},
                'House_12': {'rasi_name': 'கன்னி', 'planets': ['கேது']}
            },
            'navamsa_chart': {
                'House_1': {'rasi_name': 'மேஷம்', 'planets': ['சூரியன்']},
                'House_2': {'rasi_name': 'ரிஷபம்', 'planets': []},
                'House_3': {'rasi_name': 'மிதுனம்', 'planets': ['சந்திரன்']},
                'House_4': {'rasi_name': 'கடகம்', 'planets': []},
                'House_5': {'rasi_name': 'சிம்மம்', 'planets': ['குரு']},
                'House_6': {'rasi_name': 'கன்னி', 'planets': []},
                'House_7': {'rasi_name': 'துலாம்', 'planets': ['சுக்கிரன்']},
                'House_8': {'rasi_name': 'விருச்சிகம்', 'planets': []},
                'House_9': {'rasi_name': 'தனுசு', 'planets': ['செவ்வாய்']},
                'House_10': {'rasi_name': 'மகரம்', 'planets': ['சனி']},
                'House_11': {'rasi_name': 'கும்பம்', 'planets': []},
                'House_12': {'rasi_name': 'மீனம்', 'planets': ['ராகு', 'கேது']}
            }
        }
        
        visualizer = PreciseChartVisualizer()
        
        # Test Rasi chart HTML generation
        rasi_html = visualizer.generate_precise_chart_html(sample_chart_data, "rasi")
        if rasi_html and 'precise-south-indian-chart' in rasi_html:
            print("✅ Precise Rasi chart HTML generated!")
        else:
            print("❌ Rasi chart HTML generation failed")
            return False
        
        # Test Navamsa chart HTML generation
        navamsa_html = visualizer.generate_precise_chart_html(sample_chart_data, "navamsa")
        if navamsa_html and 'நவாம்ச சக்கரம்' in navamsa_html:
            print("✅ Precise Navamsa chart HTML generated!")
        else:
            print("❌ Navamsa chart HTML generation failed")
            return False
        
        # Test complete chart HTML
        complete_html = visualizer.generate_complete_precise_charts_html(sample_chart_data)
        if complete_html and len(complete_html) > 1000:
            print("✅ Complete precise charts HTML generated!")
            return True
        else:
            print("❌ Complete charts HTML generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Chart visualizer test error: {e}")
        return False

def test_web_precise_chart_generation():
    """Test web-based precise chart generation"""
    print("\n=== Testing Web Precise Chart Generation ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Generate precise Jathagam via web
        data = {
            'name': 'வெப் துல்லிய சோதனை',
            'gender': 'Female',
            'birth_date': '1985-12-10',
            'birth_time': '16:45',
            'birth_place': 'கோவை'
        }
        
        response = session.post('http://localhost:5000/generate_standalone_jathagam', data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Web precise chart generation successful!")
                
                jathagam = result.get('jathagam', {})
                
                # Check if using precise calculation method
                calc_method = jathagam.get('calculation_method', '')
                if 'Precise Logic' in calc_method:
                    print("✅ Using precise logical architecture!")
                else:
                    print("⚠️ May not be using precise logic")
                
                # Check chart structure
                rasi_chart = jathagam.get('rasi_chart', {})
                navamsa_chart = jathagam.get('navamsa_chart', {})
                
                if len(rasi_chart) == 12 and len(navamsa_chart) == 12:
                    print("✅ Both charts have correct 12 houses!")
                else:
                    print(f"⚠️ Chart structure: Rasi={len(rasi_chart)}, Navamsa={len(navamsa_chart)}")
                
                # Check chart HTML
                chart_html = result.get('chart_html', '')
                if 'precise-south-indian-chart' in chart_html:
                    print("✅ Using precise chart visualization!")
                    return True
                else:
                    print("⚠️ Chart HTML may not be using precise visualization")
                    return True  # Still pass as generation worked
            else:
                print(f"❌ Web precise chart failed: {result.get('message')}")
                return False
        else:
            print(f"❌ Web request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web precise chart test error: {e}")
        return False

def main():
    """Run all precise chart logic tests"""
    print("🧪 Testing Precise Chart Generation Logic...")
    print("Following exact logical architecture specifications\n")
    
    tests = [
        test_precise_chart_generator,
        test_navamsa_calculation_logic,
        test_precise_chart_visualizer,
        test_web_precise_chart_generation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All precise chart logic tests passed!")
        print("\n✅ Precise Logical Architecture Implemented:")
        print("  📌 STEP 1: Input parameters with coordinates & timezone")
        print("  📌 STEP 2: Swiss Ephemeris calculations with Lahiri Ayanamsa")
        print("  📌 STEP 3: Rasi chart using 30° segments")
        print("  📌 STEP 4: Navamsa chart using 3°20' divisions")
        print("  📌 STEP 5: South Indian visualization with proper positioning")
        print("\n🎯 Key Features:")
        print("  🔢 Exact longitude calculations")
        print("  🏠 Proper house positioning from Lagna")
        print("  🔄 Correct Navamsa mapping rules")
        print("  🎨 Enhanced visual design")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
