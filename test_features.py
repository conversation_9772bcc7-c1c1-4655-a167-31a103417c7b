"""
Test script to verify all features of the User Data Manager application
"""
from app import app, db, User, Document, extract_user_data_from_file
import os
import requests

def test_database_functionality():
    """Test database operations"""
    print("=== Testing Database Functionality ===")
    
    with app.app_context():
        # Test user creation
        user_count_before = User.query.count()
        doc_count_before = Document.query.count()
        
        print(f"Users before: {user_count_before}")
        print(f"Documents before: {doc_count_before}")
        
        # Test user creation
        test_user = User(name="Test API User", city="API City", source="manual")
        db.session.add(test_user)
        db.session.commit()
        
        user_count_after = User.query.count()
        print(f"Users after adding: {user_count_after}")
        
        # Test document association
        test_doc = Document(
            filename="test_api_doc.txt",
            original_filename="test_api_doc.txt",
            file_path="uploads/test_api_doc.txt",
            file_type=".txt",
            file_size=500,
            document_type="uploaded",
            user_id=test_user.id
        )
        db.session.add(test_doc)
        db.session.commit()
        
        doc_count_after = Document.query.count()
        print(f"Documents after adding: {doc_count_after}")
        
        # Test relationships
        user_with_docs = User.query.filter_by(name="Test API User").first()
        print(f"User documents count: {len(user_with_docs.documents)}")
        
        print("✅ Database functionality working!")

def test_file_extraction():
    """Test file data extraction"""
    print("\n=== Testing File Extraction ===")
    
    # Test CSV extraction
    csv_data = extract_user_data_from_file('test_users.csv')
    print(f"Extracted {len(csv_data)} users from CSV")
    for user in csv_data[:2]:
        print(f"  - {user['name']} from {user['city']}")
    
    print("✅ File extraction working!")

def test_api_endpoints():
    """Test API endpoints"""
    print("\n=== Testing API Endpoints ===")
    
    base_url = "http://127.0.0.1:5000"
    
    # Test endpoints
    endpoints = {
        "/api/users": "Users API",
        "/api/search?q=test": "Search API",
        "/check_scanner": "Scanner Status API",
        "/documents": "Documents Page"
    }
    
    for endpoint, description in endpoints.items():
        try:
            response = requests.get(base_url + endpoint, timeout=5)
            if response.status_code == 200:
                print(f"✅ {description}: Working (200)")
            else:
                print(f"❌ {description}: Error ({response.status_code})")
        except Exception as e:
            print(f"❌ {description}: Failed - {e}")

def test_scanner_functionality():
    """Test scanner functionality"""
    print("\n=== Testing Scanner Functionality ===")
    
    try:
        from mock_scanner import create_mock_scan
        filename = create_mock_scan()
        print(f"✅ Mock scanner created: {filename}")
        
        # Check if file exists
        if os.path.exists(os.path.join('scanned', filename)):
            print("✅ Scanned file saved correctly")
        else:
            print("❌ Scanned file not found")
            
    except Exception as e:
        print(f"❌ Scanner functionality failed: {e}")

def generate_feature_summary():
    """Generate a summary of all features"""
    print("\n" + "="*50)
    print("USER DATA MANAGER - FEATURE SUMMARY")
    print("="*50)
    
    with app.app_context():
        total_users = User.query.count()
        total_docs = Document.query.count()
        scanned_docs = Document.query.filter_by(document_type='scanned').count()
        uploaded_docs = Document.query.filter_by(document_type='uploaded').count()
        
        print(f"📊 Database Statistics:")
        print(f"   • Total Users: {total_users}")
        print(f"   • Total Documents: {total_docs}")
        print(f"   • Scanned Documents: {scanned_docs}")
        print(f"   • Uploaded Documents: {uploaded_docs}")
        
        print(f"\n🎯 Available Features:")
        print(f"   ✅ Manual user entry")
        print(f"   ✅ File upload and data extraction")
        print(f"   ✅ Document scanning (with mock scanner)")
        print(f"   ✅ Document storage and viewing")
        print(f"   ✅ User-document association")
        print(f"   ✅ Search functionality")
        print(f"   ✅ Document archive with filtering")
        print(f"   ✅ Individual user document views")
        print(f"   ✅ Database persistence")
        print(f"   ✅ RESTful API endpoints")
        
        print(f"\n🌐 Web Interface:")
        print(f"   • Home page: http://127.0.0.1:5000/")
        print(f"   • Documents archive: http://127.0.0.1:5000/documents")
        print(f"   • User documents: http://127.0.0.1:5000/user/[id]/documents")
        print(f"   • API users: http://127.0.0.1:5000/api/users")
        print(f"   • API search: http://127.0.0.1:5000/api/search?q=query")

if __name__ == "__main__":
    os.chdir('c:/Users/<USER>/OneDrive/Desktop/project')
    
    test_database_functionality()
    test_file_extraction()
    test_scanner_functionality()
    test_api_endpoints()
    generate_feature_summary()
    
    print(f"\n🎉 All tests completed!")
