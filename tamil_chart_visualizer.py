#!/usr/bin/env python3
"""
Tamil Chart Visualizer for South Indian Style Charts
Following traditional Tamil Nadu Jathagam layout used in Kalaimagal, Kala Chakra
"""
from typing import Dict, List
import json

class TamilChartVisualizer:
    """
    Visualize Tamil Jathagam charts in traditional South Indian style
    Fixed box layout with Ra<PERSON> in clockwise order
    """
    
    def __init__(self):
        # South Indian chart layout (4x4 grid)
        # Fixed positions for each house number
        self.south_indian_layout = {
            1: (1, 3),   # லக்னம் - Right middle
            2: (0, 3),   # தன - Top right
            3: (0, 2),   # சகோதர - Top middle-right
            4: (0, 1),   # மாதா - Top middle-left
            5: (0, 0),   # புத்திர - Top left
            6: (1, 0),   # ரிபு - Left middle
            7: (2, 0),   # கலத்திர - Bottom left
            8: (3, 0),   # ஆயுள் - Bottom middle-left
            9: (3, 1),   # பாக்கிய - Bottom middle-right
            10: (3, 2),  # கர்ம - Bottom right
            11: (2, 3),  # லாப - Right bottom
            12: (1, 2)   # வ்யய - Right top
        }
        
        # Tamil house names
        self.tamil_house_names = [
            'லக்னம்', 'தன', 'சகோதர', 'மாதா', 'புத்திர', 'ரிபு',
            'கலத்திர', 'ஆயுள்', 'பாக்கிய', 'கர்ம', 'லாப', 'வ்யய'
        ]
        
        # Planet abbreviations for chart display
        self.planet_abbreviations = {
            'சூரியன்': 'சூ',
            'சந்திரன்': 'ச',
            'செவ்வாய்': 'செ',
            'புதன்': 'பு',
            'குரு': 'கு',
            'சுக்கிரன்': 'சு',
            'சனி': 'சனி',
            'ராகு': 'ரா',
            'கேது': 'கே'
        }
    
    def generate_chart_html(self, chart_data: Dict, chart_type: str = "rasi") -> str:
        """
        Generate HTML for Tamil chart in South Indian style
        """
        if chart_type == "rasi":
            chart = chart_data.get('rasi_chart', {})
            title = "ராசி சக்கரம் (D1)"
            chart_class = "tamil-rasi-chart"
        else:
            chart = chart_data.get('navamsa_chart', {})
            title = "நவாம்ச சக்கரம் (D9)"
            chart_class = "tamil-navamsa-chart"
        
        chart_html = f"""
        <div class="tamil-chart-container">
            <h3 class="tamil-chart-title">{title}</h3>
            <div class="{chart_class} tamil-south-indian-chart">
        """
        
        # Create 4x4 grid
        for row in range(4):
            chart_html += '<div class="tamil-chart-row">'
            for col in range(4):
                # Find which house is at this position
                house_at_position = None
                for house_num in range(1, 13):
                    if self.south_indian_layout[house_num] == (row, col):
                        house_at_position = house_num
                        break
                
                if house_at_position:
                    # Generate house cell
                    house_key = f"House_{house_at_position}"
                    house_data = chart.get(house_key, {})
                    
                    house_name = self.tamil_house_names[house_at_position - 1]
                    rasi_name = house_data.get('rasi_name', '')
                    planets = house_data.get('planets', [])
                    
                    # Abbreviate planet names for display
                    planet_abbrevs = []
                    for planet in planets:
                        abbrev = self.planet_abbreviations.get(planet, planet[:2])
                        planet_abbrevs.append(abbrev)
                    
                    planets_text = ' '.join(planet_abbrevs) if planet_abbrevs else ''
                    
                    chart_html += f"""
                    <div class="tamil-chart-cell tamil-house-cell house-{house_at_position}" data-house="{house_at_position}">
                        <div class="tamil-house-number">{house_at_position}</div>
                        <div class="tamil-house-name">{house_name}</div>
                        <div class="tamil-rasi-name">{rasi_name}</div>
                        <div class="tamil-planets">{planets_text}</div>
                    </div>
                    """
                else:
                    # Empty center cells
                    chart_html += '<div class="tamil-chart-cell tamil-center-cell"></div>'
            
            chart_html += '</div>'
        
        chart_html += """
            </div>
        </div>
        """
        
        return chart_html
    
    def generate_chart_css(self) -> str:
        """
        Generate CSS for Tamil charts following traditional style
        """
        return """
        <style>
        .tamil-chart-container {
            margin: 1rem 0;
            font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', 'Suratha', sans-serif;
            text-align: center;
        }
        
        .tamil-chart-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #2c3e50;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.8rem;
            border-radius: 8px;
        }
        
        .tamil-south-indian-chart {
            display: grid;
            grid-template-rows: repeat(4, 1fr);
            width: 480px;
            height: 480px;
            margin: 0 auto;
            border: 3px solid #2c3e50;
            background: #ffffff;
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .tamil-chart-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
        }
        
        .tamil-chart-cell {
            border: 1px solid #34495e;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0.5rem;
            font-size: 0.85rem;
            position: relative;
            min-height: 115px;
            transition: all 0.3s ease;
        }
        
        .tamil-house-cell {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            cursor: pointer;
        }
        
        .tamil-house-cell:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: scale(1.02);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .tamil-center-cell {
            background: linear-gradient(135deg, #f1f3f4 0%, #dadce0 100%);
            border: none;
        }
        
        .tamil-house-number {
            font-size: 0.7rem;
            color: #6c757d;
            position: absolute;
            top: 3px;
            left: 3px;
            font-weight: bold;
            background: rgba(255,255,255,0.9);
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #dee2e6;
        }
        
        .tamil-house-name {
            font-size: 0.75rem;
            color: #495057;
            margin-bottom: 0.4rem;
            font-weight: 600;
            text-align: center;
            line-height: 1.2;
        }
        
        .tamil-rasi-name {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1rem;
            margin-bottom: 0.5rem;
            text-align: center;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
            background: rgba(255,255,255,0.8);
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .tamil-planets {
            color: #dc3545;
            font-weight: bold;
            font-size: 0.85rem;
            line-height: 1.4;
            text-align: center;
            background: rgba(220, 53, 69, 0.1);
            padding: 3px 6px;
            border-radius: 5px;
            min-height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }
        
        /* House-specific styling */
        .house-1 { border-left: 4px solid #ff6b6b; } /* லக்னம் */
        .house-2 { border-left: 4px solid #4ecdc4; } /* தன */
        .house-3 { border-left: 4px solid #45b7d1; } /* சகோதர */
        .house-4 { border-left: 4px solid #96ceb4; } /* மாதா */
        .house-5 { border-left: 4px solid #ffeaa7; } /* புத்திர */
        .house-6 { border-left: 4px solid #dda0dd; } /* ரிபு */
        .house-7 { border-left: 4px solid #98d8c8; } /* கலத்திர */
        .house-8 { border-left: 4px solid #f7dc6f; } /* ஆயுள் */
        .house-9 { border-left: 4px solid #bb8fce; } /* பாக்கிய */
        .house-10 { border-left: 4px solid #85c1e9; } /* கர்ம */
        .house-11 { border-left: 4px solid #82e0aa; } /* லாப */
        .house-12 { border-left: 4px solid #f8c471; } /* வ்யய */
        
        /* Rasi chart specific styling */
        .tamil-rasi-chart {
            border-color: #2c3e50;
        }
        
        .tamil-rasi-chart .tamil-chart-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* Navamsa chart specific styling */
        .tamil-navamsa-chart {
            border-color: #8e44ad;
        }
        
        .tamil-navamsa-chart .tamil-chart-title {
            background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .tamil-south-indian-chart {
                width: 380px;
                height: 380px;
            }
            
            .tamil-chart-cell {
                font-size: 0.75rem;
                min-height: 90px;
                padding: 0.3rem;
            }
            
            .tamil-house-name {
                font-size: 0.65rem;
            }
            
            .tamil-rasi-name {
                font-size: 0.9rem;
            }
            
            .tamil-planets {
                font-size: 0.75rem;
            }
        }
        
        @media (max-width: 480px) {
            .tamil-south-indian-chart {
                width: 320px;
                height: 320px;
            }
            
            .tamil-chart-cell {
                font-size: 0.65rem;
                min-height: 75px;
                padding: 0.2rem;
            }
            
            .tamil-house-name {
                font-size: 0.55rem;
            }
            
            .tamil-rasi-name {
                font-size: 0.8rem;
            }
            
            .tamil-planets {
                font-size: 0.65rem;
            }
        }
        </style>
        """
    
    def generate_complete_charts_html(self, jathagam_data: Dict) -> str:
        """
        Generate complete HTML with both Rasi and Navamsa charts
        """
        if not jathagam_data:
            return "<p>தமிழ் ஜாதக தகவல் கிடைக்கவில்லை</p>"
        
        personal = jathagam_data.get('personal_details', {})
        astro = jathagam_data.get('astrological_details', {})
        
        html = self.generate_chart_css()
        
        html += f"""
        <div class="tamil-jathagam-container">
            <div class="tamil-jathagam-header">
                <h2 style="text-align: center; color: #2c3e50; margin-bottom: 1.5rem; font-size: 1.6rem;">
                    🌟 {personal.get('name', 'தெரியவில்லை')} அவர்களின் தமிழ் ஜாதகம் 🌟
                </h2>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 1.5rem; border-radius: 10px; color: white;">
                        <h4 style="margin-bottom: 1rem; color: white;">📋 பிறப்பு விவரங்கள்</h4>
                        <div style="display: grid; gap: 0.5rem; font-size: 0.95rem;">
                            <div><strong>பிறந்த தேதி:</strong> {personal.get('birth_date', 'தெரியவில்லை')}</div>
                            <div><strong>பிறந்த நேரம்:</strong> {personal.get('birth_time', 'தெரியவில்லை')}</div>
                            <div><strong>பிறந்த இடம்:</strong> {personal.get('birth_place', 'தெரியவில்லை')}</div>
                            <div><strong>அயனாம்சம்:</strong> {personal.get('ayanamsa', 'Lahiri')}</div>
                        </div>
                    </div>
                    
                    <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); padding: 1.5rem; border-radius: 10px; color: white;">
                        <h4 style="margin-bottom: 1rem; color: white;">🌙 முக்கிய ஜோதிட தகவல்கள்</h4>
                        <div style="display: grid; gap: 0.5rem; font-size: 0.95rem;">
                            <div><strong>லக்ன ராசி:</strong> {astro.get('lagna_rasi', 'தெரியவில்லை')}</div>
                            <div><strong>சந்திர ராசி:</strong> {astro.get('moon_raasi', 'தெரியவில்லை')}</div>
                            <div><strong>சந்திர நட்சத்திரம்:</strong> {astro.get('moon_nakshatra', 'தெரியவில்லை')}</div>
                            <div><strong>சந்திர பாதம்:</strong> {astro.get('moon_pada', 'தெரியவில்லை')}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; margin-bottom: 2rem;">
                <div class="chart-section">
                    {self.generate_chart_html(jathagam_data, "rasi")}
                </div>
                <div class="chart-section">
                    {self.generate_chart_html(jathagam_data, "navamsa")}
                </div>
            </div>
            
            <div style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); padding: 1rem; border-radius: 8px; text-align: center; font-size: 0.9rem; color: #2c3e50;">
                <strong>கணக்கீட்டு முறை:</strong> {jathagam_data.get('calculation_method', 'Swiss Ephemeris with Lahiri Ayanamsa')}
            </div>
        </div>
        """
        
        return html

# Test function
if __name__ == "__main__":
    print("🎨 Testing Tamil Chart Visualizer...")
    
    # Sample chart data
    sample_jathagam = {
        'personal_details': {
            'name': 'தமிழ் சார்ட் சோதனை',
            'birth_date': '1990-08-15',
            'birth_time': '10:30',
            'birth_place': 'சென்னை',
            'ayanamsa': 'Lahiri'
        },
        'astrological_details': {
            'lagna_rasi': 'துலாம்',
            'moon_raasi': 'ரிஷபம்',
            'moon_nakshatra': 'ரோகிணி',
            'moon_pada': 2
        },
        'rasi_chart': {
            'House_1': {'rasi_name': 'துலாம்', 'planets': ['சூரியன்']},
            'House_2': {'rasi_name': 'விருச்சிகம்', 'planets': ['சந்திரன்']},
            'House_3': {'rasi_name': 'தனுசு', 'planets': []},
            'House_4': {'rasi_name': 'மகரம்', 'planets': ['குரு']},
            'House_5': {'rasi_name': 'கும்பம்', 'planets': []},
            'House_6': {'rasi_name': 'மீனம்', 'planets': ['செவ்வாய்']},
            'House_7': {'rasi_name': 'மேஷம்', 'planets': ['சுக்கிரன்']},
            'House_8': {'rasi_name': 'ரிஷபம்', 'planets': []},
            'House_9': {'rasi_name': 'மிதுனம்', 'planets': ['சனி']},
            'House_10': {'rasi_name': 'கடகம்', 'planets': []},
            'House_11': {'rasi_name': 'சிம்மம்', 'planets': ['ராகு']},
            'House_12': {'rasi_name': 'கன்னி', 'planets': ['கேது']}
        },
        'navamsa_chart': {
            'House_1': {'rasi_name': 'மேஷம்', 'planets': ['சூரியன்']},
            'House_2': {'rasi_name': 'ரிஷபம்', 'planets': []},
            'House_3': {'rasi_name': 'மிதுனம்', 'planets': ['சந்திரன்']},
            'House_4': {'rasi_name': 'கடகம்', 'planets': []},
            'House_5': {'rasi_name': 'சிம்மம்', 'planets': ['குரு']},
            'House_6': {'rasi_name': 'கன்னி', 'planets': []},
            'House_7': {'rasi_name': 'துலாம்', 'planets': ['சுக்கிரன்']},
            'House_8': {'rasi_name': 'விருச்சிகம்', 'planets': []},
            'House_9': {'rasi_name': 'தனுசு', 'planets': ['செவ்வாய்']},
            'House_10': {'rasi_name': 'மகரம்', 'planets': ['சனி']},
            'House_11': {'rasi_name': 'கும்பம்', 'planets': []},
            'House_12': {'rasi_name': 'மீனம்', 'planets': ['ராகு', 'கேது']}
        }
    }
    
    visualizer = TamilChartVisualizer()
    
    # Test chart generation
    rasi_html = visualizer.generate_chart_html(sample_jathagam, "rasi")
    navamsa_html = visualizer.generate_chart_html(sample_jathagam, "navamsa")
    complete_html = visualizer.generate_complete_charts_html(sample_jathagam)
    
    print("✅ Tamil chart HTML generation successful!")
    print(f"   Rasi chart HTML: {len(rasi_html)} characters")
    print(f"   Navamsa chart HTML: {len(navamsa_html)} characters")
    print(f"   Complete charts HTML: {len(complete_html)} characters")
    print("\n🎉 Tamil Chart Visualizer test completed!")
