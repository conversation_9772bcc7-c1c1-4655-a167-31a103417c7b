#!/usr/bin/env python3
"""
Test the client-side only printer detection system
Verify that it only detects user's local printers, not server printers
"""
import os
import sys
import requests
import json
from datetime import date, time

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_client_side_printer_detection():
    """Test that printer detection is client-side only"""
    print("=== Testing Client-Side Printer Detection ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Access admin dashboard to check printer system
        dashboard_response = session.get('http://localhost:5000/admin_dashboard')
        
        if dashboard_response.status_code == 200:
            dashboard_html = dashboard_response.text
            
            # Check if enhanced print system is loaded
            if 'enhanced_print_system.js' in dashboard_html:
                print("✅ Enhanced print system loaded in dashboard")
                
                # Check for client-side indicators
                if 'CLIENT-SIDE' in dashboard_html or 'client-side' in dashboard_html:
                    print("✅ Client-side printer detection indicators found")
                else:
                    print("⚠️ Client-side indicators not found in HTML")
                
                # Check for server-side API calls (should not be present)
                if '/api/detect_printers' in dashboard_html:
                    print("⚠️ Server-side printer API still referenced")
                else:
                    print("✅ No server-side printer API references found")
                
                return True
            else:
                print("❌ Enhanced print system not loaded")
                return False
        else:
            print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Client-side printer detection test error: {e}")
        return False

def test_enhanced_print_system_js():
    """Test the enhanced print system JavaScript file"""
    print("\n=== Testing Enhanced Print System JS ===")
    
    try:
        # Read the enhanced print system file
        with open('static/js/enhanced_print_system.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for client-side only indicators
        client_side_indicators = [
            'CLIENT-SIDE ONLY',
            'detectClientSidePrintersOnly',
            'No server communication',
            'client-side only',
            'உங்கள் உள்ளூர் அச்சுப்பொறிகள்',
            'சர்வர் அச்சுப்பொறிகள் அல்ல'
        ]
        
        found_indicators = []
        for indicator in client_side_indicators:
            if indicator in js_content:
                found_indicators.append(indicator)
        
        print(f"✅ Client-side indicators found: {len(found_indicators)}/{len(client_side_indicators)}")
        for indicator in found_indicators:
            print(f"   ✓ {indicator}")
        
        # Check for server-side API calls (should be minimal or removed)
        server_api_calls = [
            '/api/detect_printers',
            '/api/check_printer_connectivity',
            'detectPrintersViaServer'
        ]
        
        found_server_calls = []
        for api_call in server_api_calls:
            if api_call in js_content:
                found_server_calls.append(api_call)
        
        if found_server_calls:
            print(f"⚠️ Server API calls still present: {len(found_server_calls)}")
            for call in found_server_calls:
                print(f"   ⚠️ {call}")
        else:
            print("✅ No server API calls found - fully client-side!")
        
        # Check for browser-based detection methods
        browser_methods = [
            'navigator.printing',
            'window.matchMedia',
            'window.print',
            'detectPrintersViaBrowserAPI',
            'detectPrintCapabilitiesViaMediaQuery'
        ]
        
        found_browser_methods = []
        for method in browser_methods:
            if method in js_content:
                found_browser_methods.append(method)
        
        print(f"✅ Browser-based detection methods: {len(found_browser_methods)}/{len(browser_methods)}")
        for method in found_browser_methods:
            print(f"   ✓ {method}")
        
        # Overall assessment
        if len(found_indicators) >= 3 and len(found_browser_methods) >= 3:
            print("✅ Enhanced print system is properly client-side!")
            return True
        else:
            print("⚠️ Enhanced print system may not be fully client-side")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced print system JS test error: {e}")
        return False

def test_printer_dialog_content():
    """Test that printer dialog shows client-side content"""
    print("\n=== Testing Printer Dialog Content ===")
    
    try:
        # Read the enhanced print system file to check dialog content
        with open('static/js/enhanced_print_system.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for client-side dialog indicators
        dialog_indicators = [
            'உங்கள் கணினியில் கண்டறியப்பட்ட அச்சுப்பொறிகள்',
            'சர்வர் அச்சுப்பொறிகள் அல்ல',
            'உள்ளூர் அச்சுப்பொறிகள் மட்டும்',
            'மூலம்:',
            'உள்ளூர் கணினி',
            'சர்வர் அல்ல'
        ]
        
        found_dialog_indicators = []
        for indicator in dialog_indicators:
            if indicator in js_content:
                found_dialog_indicators.append(indicator)
        
        print(f"✅ Dialog client-side indicators: {len(found_dialog_indicators)}/{len(dialog_indicators)}")
        for indicator in found_dialog_indicators:
            print(f"   ✓ {indicator}")
        
        # Check for printer source information
        if 'printer.source' in js_content:
            print("✅ Printer source information included")
        else:
            print("⚠️ Printer source information missing")
        
        # Check for local printer emphasis
        if '🖥️' in js_content and 'உள்ளூர்' in js_content:
            print("✅ Local printer emphasis present")
        else:
            print("⚠️ Local printer emphasis missing")
        
        return len(found_dialog_indicators) >= 4
        
    except Exception as e:
        print(f"❌ Printer dialog content test error: {e}")
        return False

def test_print_formatting_client_side():
    """Test that print formatting is client-side only"""
    print("\n=== Testing Print Formatting (Client-Side) ===")
    
    try:
        # Read the enhanced print system file
        with open('static/js/enhanced_print_system.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for client-side formatting indicators
        formatting_indicators = [
            'formatJathagamClientSide',
            'CLIENT-SIDE ONLY',
            'No server communication',
            'all formatting done in browser',
            'உங்கள் உள்ளூர் கணினியில் இருந்து அச்சிடப்பட்டது',
            'சர்வர் அல்ல'
        ]
        
        found_formatting_indicators = []
        for indicator in formatting_indicators:
            if indicator in js_content:
                found_formatting_indicators.append(indicator)
        
        print(f"✅ Client-side formatting indicators: {len(found_formatting_indicators)}/{len(formatting_indicators)}")
        for indicator in found_formatting_indicators:
            print(f"   ✓ {indicator}")
        
        # Check for enhanced formatting features
        formatting_features = [
            'formatPlanetaryPositions',
            'printer-info',
            'உள்ளூர் அச்சுப்பொறி',
            'Swiss Ephemeris கணக்கீடு',
            'STEP 1-5 Implementation'
        ]
        
        found_features = []
        for feature in formatting_features:
            if feature in js_content:
                found_features.append(feature)
        
        print(f"✅ Enhanced formatting features: {len(found_features)}/{len(formatting_features)}")
        for feature in found_features:
            print(f"   ✓ {feature}")
        
        return len(found_formatting_indicators) >= 4 and len(found_features) >= 3
        
    except Exception as e:
        print(f"❌ Print formatting test error: {e}")
        return False

def test_web_interface_client_side_integration():
    """Test web interface integration with client-side printer system"""
    print("\n=== Testing Web Interface Integration ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Generate a Jathagam to test print integration
        data = {
            'name': 'அச்சு சோதனை',
            'gender': 'Male',
            'birth_date': '1990-08-15',
            'birth_time': '10:30',
            'birth_place': 'சென்னை'
        }
        
        response = session.post('http://localhost:5000/generate_standalone_jathagam', data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                chart_html = result.get('chart_html', '')
                
                # Check for enhanced print system integration
                if 'enhancedPrintSystem.enhancedPrintJathagam()' in chart_html:
                    print("✅ Enhanced print system integrated in chart HTML")
                else:
                    print("⚠️ Enhanced print system not found in chart HTML")
                
                # Check for client-side print indicators
                if 'மேம்பட்ட அச்சு' in chart_html:
                    print("✅ Enhanced print button present")
                else:
                    print("⚠️ Enhanced print button missing")
                
                return True
            else:
                print(f"❌ Jathagam generation failed: {result.get('message')}")
                return False
        else:
            print(f"❌ Web request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web interface integration test error: {e}")
        return False

def main():
    """Run all client-side printer system tests"""
    print("🖨️ Testing Client-Side Only Printer Detection System...")
    print("Verifying that only user's local printers are detected, not server printers\n")
    
    tests = [
        test_client_side_printer_detection,
        test_enhanced_print_system_js,
        test_printer_dialog_content,
        test_print_formatting_client_side,
        test_web_interface_client_side_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print(f"\n📊 Client-Side Printer System Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All client-side printer system tests passed!")
        print("\n✅ Client-Side Printer System Features:")
        print("  🖥️ Local Detection: Only user's computer printers detected")
        print("  🚫 No Server Communication: Zero server-side printer API calls")
        print("  🌐 Browser-Based: Uses Web Print API and media queries")
        print("  📱 Client-Side Formatting: All print formatting done in browser")
        print("  🔒 Privacy: No printer information sent to server")
        print("  ⚡ Fast: No network delays for printer detection")
        print("  🎯 User-Specific: Shows only printers available to current user")
        print("  📊 Complete Integration: Works with corrected Tamil Jathagam system")
    else:
        print("⚠️ Some client-side printer tests failed. Please check the issues above.")
        print("\nNote: The system should only detect user's local printers, not server printers.")

if __name__ == "__main__":
    main()
