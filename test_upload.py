#!/usr/bin/env python3
"""
Test the upload functionality specifically
"""
import os
import sys
from werkzeug.datastructures import FileStorage
from io import BytesIO

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import db, User, Document

def test_upload_functionality():
    """Test the file upload functionality"""
    with app.app_context():
        print("=== Testing Upload Functionality ===")
        
        # Count existing records
        users_before = User.query.count()
        docs_before = Document.query.count()
        print(f"Users before upload: {users_before}")
        print(f"Documents before upload: {docs_before}")
        
        # Clear uploads folder first
        uploads_folder = app.config['UPLOAD_FOLDER']
        for file in os.listdir(uploads_folder):
            if os.path.isfile(os.path.join(uploads_folder, file)):
                os.remove(os.path.join(uploads_folder, file))
        
        # Test file content
        test_content = "<PERSON>,Chicago\nBob <PERSON>,Miami\nCarol Davis,Seattle"
        
        # Create a file-like object
        file_data = BytesIO(test_content.encode('utf-8'))
        file_storage = FileStorage(
            stream=file_data,
            filename='test_upload.csv',
            content_type='text/csv'
        )
        
        # Test the upload route
        with app.test_client() as client:
            response = client.post('/upload', 
                                 data={'file': file_storage},
                                 content_type='multipart/form-data')
            
            print(f"Upload response status: {response.status_code}")
            
        # Check results
        users_after = User.query.count()
        docs_after = Document.query.count()
        
        print(f"Users after upload: {users_after}")
        print(f"Documents after upload: {docs_after}")
        print(f"New users added: {users_after - users_before}")
        print(f"New documents added: {docs_after - docs_before}")
        
        # Check if files exist in uploads folder
        uploaded_files = os.listdir(uploads_folder)
        print(f"Files in uploads folder: {len(uploaded_files)}")
        for file in uploaded_files:
            file_path = os.path.join(uploads_folder, file)
            print(f"  - {file} (size: {os.path.getsize(file_path)} bytes)")
        
        # Check database records
        new_docs = Document.query.filter_by(document_type='uploaded').all()
        print(f"\\nUploaded documents in database:")
        for doc in new_docs:
            exists = os.path.exists(doc.file_path)
            print(f"  - {doc.filename} (User: {doc.user.name if doc.user else 'None'}, File exists: {exists})")
        
        print("✅ Upload functionality test completed!")

if __name__ == "__main__":
    test_upload_functionality()
