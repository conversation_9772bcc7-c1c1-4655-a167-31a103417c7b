#!/usr/bin/env python3
"""
Advanced Jathagam Print Formatter
Optimizes Jathagam data formatting for different printer types and ensures complete data coverage
"""
from typing import Dict, List, Optional
import json

class JathagamPrintFormatter:
    """Advanced formatter for Jathagam printing with printer-specific optimizations"""
    
    def __init__(self):
        self.printer_optimizations = {
            'laser': {
                'font_size': '12pt',
                'line_height': '1.4',
                'chart_size': '400px',
                'use_borders': True,
                'color_mode': 'color'
            },
            'inkjet': {
                'font_size': '11pt',
                'line_height': '1.3',
                'chart_size': '380px',
                'use_borders': True,
                'color_mode': 'color'
            },
            'dot_matrix': {
                'font_size': '10pt',
                'line_height': '1.2',
                'chart_size': '300px',
                'use_borders': False,
                'color_mode': 'monochrome'
            },
            'thermal': {
                'font_size': '9pt',
                'line_height': '1.1',
                'chart_size': '250px',
                'use_borders': False,
                'color_mode': 'monochrome'
            },
            'generic': {
                'font_size': '11pt',
                'line_height': '1.3',
                'chart_size': '350px',
                'use_borders': True,
                'color_mode': 'color'
            }
        }
    
    def format_for_printer(self, jathagam_data: Dict, printer_info: Dict) -> str:
        """Format Jathagam data optimized for specific printer type"""
        
        if not jathagam_data:
            return self._generate_error_page("ஜாதக தகவல் கிடைக்கவில்லை")
        
        printer_type = printer_info.get('type', 'generic').lower()
        optimization = self._get_printer_optimization(printer_type)
        
        # Generate complete formatted HTML
        formatted_html = self._generate_print_html(jathagam_data, optimization, printer_info)
        
        return formatted_html
    
    def _get_printer_optimization(self, printer_type: str) -> Dict:
        """Get optimization settings for printer type"""
        
        # Map various printer type names to our optimization categories
        type_mapping = {
            'hp laser': 'laser',
            'laser': 'laser',
            'canon inkjet': 'inkjet',
            'epson inkjet': 'inkjet',
            'inkjet': 'inkjet',
            'dot matrix': 'dot_matrix',
            'thermal': 'thermal',
            'usb printer': 'generic',
            'network printer': 'generic',
            'virtual': 'generic'
        }
        
        mapped_type = type_mapping.get(printer_type, 'generic')
        return self.printer_optimizations[mapped_type]
    
    def _generate_print_html(self, jathagam_data: Dict, optimization: Dict, printer_info: Dict) -> str:
        """Generate complete print-optimized HTML"""
        
        personal = jathagam_data.get('personal_details', {})
        astro = jathagam_data.get('astrological_details', {})
        planets = jathagam_data.get('planetary_positions', {})
        rasi_chart = jathagam_data.get('rasi_chart', {})
        navamsa_chart = jathagam_data.get('navamsa_chart', {})
        
        # Generate CSS optimized for printer
        css = self._generate_print_css(optimization, printer_info)
        
        # Generate complete HTML document
        html = f"""
<!DOCTYPE html>
<html lang="ta">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ஜாதகம் - {personal.get('name', 'தெரியவில்லை')}</title>
    {css}
</head>
<body>
    <div class="print-container">
        <!-- Header Section -->
        <div class="print-header">
            <h1 class="main-title">🌟 தமிழ் ஜாதகம் 🌟</h1>
            <h2 class="person-name">{personal.get('name', 'தெரியவில்லை')} அவர்களின் ஜாதகம்</h2>
            <div class="generation-info">
                <span>உருவாக்கப்பட்ட நேரம்: {personal.get('generated_at', 'தெரியவில்லை')}</span>
                <span class="calculation-method">கணக்கீட்டு முறை: Swiss Ephemeris</span>
            </div>
        </div>
        
        <!-- Personal Details Section -->
        <div class="print-section">
            <h3 class="section-title">📋 அடிப்படை தகவல்கள்</h3>
            <div class="details-grid">
                <div class="detail-item">
                    <span class="label">பெயர்:</span>
                    <span class="value">{personal.get('name', 'தெரியவில்லை')}</span>
                </div>
                <div class="detail-item">
                    <span class="label">பாலினம்:</span>
                    <span class="value">{'ஆண்' if personal.get('gender') == 'Male' else 'பெண்'}</span>
                </div>
                <div class="detail-item">
                    <span class="label">பிறந்த தேதி:</span>
                    <span class="value">{personal.get('birth_date', 'தெரியவில்லை')}</span>
                </div>
                <div class="detail-item">
                    <span class="label">பிறந்த நேரம்:</span>
                    <span class="value">{personal.get('birth_time', 'தெரியவில்லை')}</span>
                </div>
                <div class="detail-item">
                    <span class="label">பிறந்த இடம்:</span>
                    <span class="value">{personal.get('birth_place', 'தெரியவில்லை')}</span>
                </div>
                <div class="detail-item">
                    <span class="label">அட்சாம்சம்:</span>
                    <span class="value">{personal.get('latitude', 'தெரியவில்லை')}°</span>
                </div>
                <div class="detail-item">
                    <span class="label">தீர்க்காம்சம்:</span>
                    <span class="value">{personal.get('longitude', 'தெரியவில்லை')}°</span>
                </div>
                <div class="detail-item">
                    <span class="label">நேர மண்டலம்:</span>
                    <span class="value">{personal.get('timezone', 'தெரியவில்லை')}</span>
                </div>
            </div>
        </div>
        
        <!-- Astrological Details Section -->
        <div class="print-section">
            <h3 class="section-title">🌙 முக்கிய ஜோதிட தகவல்கள்</h3>
            <div class="astro-grid">
                <div class="astro-item highlight">
                    <span class="label">சந்திர ராசி:</span>
                    <span class="value">{astro.get('moon_raasi', 'தெரியவில்லை')}</span>
                </div>
                <div class="astro-item highlight">
                    <span class="label">நட்சத்திரம்:</span>
                    <span class="value">{astro.get('moon_nakshatra', 'தெரியவில்லை')}</span>
                </div>
                <div class="astro-item highlight">
                    <span class="label">பாதம்:</span>
                    <span class="value">{astro.get('moon_pada', 'தெரியவில்லை')}</span>
                </div>
                <div class="astro-item highlight">
                    <span class="label">லக்ன ராசி:</span>
                    <span class="value">{astro.get('lagna_raasi', 'தெரியவில்லை')}</span>
                </div>
                <div class="astro-item highlight">
                    <span class="label">லக்ன நட்சத்திரம்:</span>
                    <span class="value">{astro.get('lagna_nakshatra', 'தெரியவில்லை')}</span>
                </div>
                <div class="astro-item highlight">
                    <span class="label">லக்ன டிகிரி:</span>
                    <span class="value">{astro.get('lagna_degrees', 'தெரியவில்லை')}°</span>
                </div>
            </div>
        </div>
        
        <!-- Planetary Positions Section -->
        <div class="print-section">
            <h3 class="section-title">🪐 கிரக நிலைகள்</h3>
            <div class="planets-table">
                <div class="table-header">
                    <span>கிரகம்</span>
                    <span>ராசி</span>
                    <span>நட்சத்திரம்</span>
                    <span>டிகிரி</span>
                    <span>பாதம்</span>
                </div>
                {self._generate_planets_table_rows(planets)}
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="print-section charts-section">
            <h3 class="section-title">📊 ஜாதக சக்கரங்கள்</h3>
            <div class="charts-container">
                <div class="chart-wrapper">
                    <h4 class="chart-title">ராசி சக்கரம் (D1)</h4>
                    {self._generate_print_chart(rasi_chart, optimization, 'rasi')}
                </div>
                <div class="chart-wrapper">
                    <h4 class="chart-title">நவாம்ச சக்கரம் (D9)</h4>
                    {self._generate_print_chart(navamsa_chart, optimization, 'navamsa')}
                </div>
            </div>
        </div>
        
        <!-- Footer Section -->
        <div class="print-footer">
            <div class="footer-info">
                <span>அச்சிடப்பட்ட நேரம்: {self._get_current_timestamp()}</span>
                <span>அச்சுப்பொறி: {printer_info.get('name', 'தெரியவில்லை')}</span>
            </div>
            <div class="footer-note">
                <p>இந்த ஜாதகம் Swiss Ephemeris மூலம் துல்லியமான கணக்கீட்டுடன் உருவாக்கப்பட்டது.</p>
                <p>தமிழ் பாரம்பரிய ஜோதிட முறைப்படி தயாரிக்கப்பட்டது.</p>
            </div>
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def _generate_print_css(self, optimization: Dict, printer_info: Dict) -> str:
        """Generate printer-optimized CSS"""
        
        font_size = optimization['font_size']
        line_height = optimization['line_height']
        chart_size = optimization['chart_size']
        use_borders = optimization['use_borders']
        color_mode = optimization['color_mode']
        
        # Adjust colors based on printer capabilities
        if color_mode == 'monochrome' or not printer_info.get('capabilities', {}).get('color', False):
            primary_color = '#000000'
            secondary_color = '#333333'
            background_color = '#ffffff'
            border_color = '#000000'
        else:
            primary_color = '#2c3e50'
            secondary_color = '#34495e'
            background_color = '#ffffff'
            border_color = '#bdc3c7'
        
        border_style = f'1px solid {border_color}' if use_borders else 'none'
        
        css = f"""
        <style>
        @media print {{
            @page {{
                size: A4;
                margin: 15mm;
            }}
            
            body {{
                margin: 0;
                padding: 0;
                font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', Arial, sans-serif;
                font-size: {font_size};
                line-height: {line_height};
                color: {primary_color};
                background: {background_color};
            }}
            
            .print-container {{
                width: 100%;
                max-width: none;
            }}
            
            .print-header {{
                text-align: center;
                margin-bottom: 20px;
                border-bottom: {border_style};
                padding-bottom: 15px;
            }}
            
            .main-title {{
                font-size: 1.8em;
                font-weight: bold;
                margin: 0 0 10px 0;
                color: {primary_color};
            }}
            
            .person-name {{
                font-size: 1.4em;
                font-weight: bold;
                margin: 0 0 10px 0;
                color: {secondary_color};
            }}
            
            .generation-info {{
                font-size: 0.9em;
                color: {secondary_color};
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
            }}
            
            .print-section {{
                margin-bottom: 25px;
                page-break-inside: avoid;
            }}
            
            .section-title {{
                font-size: 1.2em;
                font-weight: bold;
                margin: 0 0 15px 0;
                color: {primary_color};
                border-bottom: {border_style};
                padding-bottom: 5px;
            }}
            
            .details-grid {{
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
                margin-bottom: 15px;
            }}
            
            .detail-item, .astro-item {{
                display: flex;
                justify-content: space-between;
                padding: 5px;
                border: {border_style};
                border-radius: 3px;
            }}
            
            .astro-grid {{
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 10px;
                margin-bottom: 15px;
            }}
            
            .highlight {{
                background-color: {'#f8f9fa' if color_mode == 'color' else '#ffffff'};
                font-weight: bold;
            }}
            
            .label {{
                font-weight: bold;
                color: {secondary_color};
            }}
            
            .value {{
                color: {primary_color};
            }}
            
            .planets-table {{
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;
            }}
            
            .table-header {{
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                font-weight: bold;
                background-color: {'#e9ecef' if color_mode == 'color' else '#ffffff'};
                border: {border_style};
                padding: 8px;
            }}
            
            .table-row {{
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                border: {border_style};
                padding: 5px 8px;
            }}
            
            .charts-section {{
                page-break-before: auto;
            }}
            
            .charts-container {{
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
            }}
            
            .chart-wrapper {{
                text-align: center;
            }}
            
            .chart-title {{
                font-size: 1.1em;
                font-weight: bold;
                margin: 0 0 10px 0;
                color: {primary_color};
            }}
            
            .print-chart {{
                width: {chart_size};
                height: {chart_size};
                margin: 0 auto;
                border: {border_style};
                display: grid;
                grid-template-rows: repeat(4, 1fr);
            }}
            
            .chart-row {{
                display: grid;
                grid-template-columns: repeat(4, 1fr);
            }}
            
            .chart-cell {{
                border: {border_style};
                padding: 5px;
                font-size: 0.8em;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
                min-height: 60px;
            }}
            
            .house-number {{
                font-size: 0.7em;
                color: {secondary_color};
                position: absolute;
                top: 2px;
                left: 2px;
            }}
            
            .rasi-name {{
                font-weight: bold;
                color: {primary_color};
                margin-bottom: 3px;
            }}
            
            .planets {{
                color: {'#dc3545' if color_mode == 'color' else primary_color};
                font-weight: bold;
                font-size: 0.7em;
            }}
            
            .print-footer {{
                margin-top: 30px;
                border-top: {border_style};
                padding-top: 15px;
                font-size: 0.9em;
                color: {secondary_color};
            }}
            
            .footer-info {{
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
            }}
            
            .footer-note {{
                text-align: center;
                font-size: 0.8em;
                line-height: 1.4;
            }}
            
            .footer-note p {{
                margin: 5px 0;
            }}
        }}
        
        /* Screen styles for preview */
        @media screen {{
            body {{
                font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', Arial, sans-serif;
                font-size: {font_size};
                line-height: {line_height};
                color: {primary_color};
                background: {background_color};
                padding: 20px;
            }}
            
            .print-container {{
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }}
        }}
        </style>
        """
        
        return css

    def _generate_planets_table_rows(self, planets: Dict) -> str:
        """Generate table rows for planetary positions"""
        rows = ""

        for planet, data in planets.items():
            tamil_name = data.get('tamil_name', planet)
            raasi = data.get('raasi', 'தெரியவில்லை')
            nakshatra = data.get('nakshatra', 'தெரியவில்லை')
            degrees = data.get('degrees_in_sign', 0)
            pada = data.get('pada', 1)

            rows += f"""
            <div class="table-row">
                <span>{tamil_name}</span>
                <span>{raasi}</span>
                <span>{nakshatra}</span>
                <span>{degrees:.1f}°</span>
                <span>{pada}</span>
            </div>
            """

        return rows

    def _generate_print_chart(self, chart_data: Dict, optimization: Dict, chart_type: str) -> str:
        """Generate print-optimized chart HTML"""

        chart_html = '<div class="print-chart">'

        # Define house layout for South Indian style (4x4 grid)
        house_layout = [
            [5,  4,  3,  2],   # Top row
            [6,  0,  0,  1],   # Second row (0 = empty)
            [7,  0,  0,  12],  # Third row (0 = empty)
            [8,  9,  10, 11]   # Bottom row
        ]

        for row in house_layout:
            chart_html += '<div class="chart-row">'
            for house_num in row:
                if house_num == 0:
                    # Empty center cells
                    chart_html += '<div class="chart-cell"></div>'
                else:
                    # House cell
                    house_key = f"House_{house_num}"
                    house_data = chart_data.get(house_key, {})

                    rasi_name = house_data.get('rasi_name', '')
                    planets = house_data.get('planets', [])

                    # Abbreviate planet names for print
                    planet_abbrevs = []
                    planet_abbrev_map = {
                        'சூரியன்': 'சூ', 'சந்திரன்': 'ச', 'செவ்வாய்': 'செ',
                        'புதன்': 'பு', 'குரு': 'கு', 'சுக்கிரன்': 'சு',
                        'சனி': 'சனி', 'ராகு': 'ரா', 'கேது': 'கே'
                    }

                    for planet in planets:
                        abbrev = planet_abbrev_map.get(planet, planet[:2])
                        planet_abbrevs.append(abbrev)

                    planets_text = ' '.join(planet_abbrevs) if planet_abbrevs else ''

                    chart_html += f"""
                    <div class="chart-cell" style="position: relative;">
                        <div class="house-number">{house_num}</div>
                        <div class="rasi-name">{rasi_name}</div>
                        <div class="planets">{planets_text}</div>
                    </div>
                    """

            chart_html += '</div>'

        chart_html += '</div>'
        return chart_html

    def _generate_error_page(self, error_message: str) -> str:
        """Generate error page for printing"""
        return f"""
        <!DOCTYPE html>
        <html lang="ta">
        <head>
            <meta charset="UTF-8">
            <title>ஜாதக பிழை</title>
            <style>
                body {{
                    font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', Arial, sans-serif;
                    text-align: center;
                    padding: 50px;
                    color: #dc3545;
                }}
                .error-container {{
                    border: 2px solid #dc3545;
                    padding: 30px;
                    border-radius: 10px;
                    background: #f8f9fa;
                }}
            </style>
        </head>
        <body>
            <div class="error-container">
                <h1>❌ ஜாதக பிழை</h1>
                <p>{error_message}</p>
                <p>தயவுசெய்து மீண்டும் முயற்சிக்கவும்.</p>
            </div>
        </body>
        </html>
        """

    def _get_current_timestamp(self) -> str:
        """Get current timestamp for print footer"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def validate_print_data(self, jathagam_data: Dict) -> Dict:
        """Validate that all required data is present for printing"""
        validation_result = {
            'valid': True,
            'missing_fields': [],
            'warnings': [],
            'completeness_score': 0
        }

        required_fields = {
            'personal_details': ['name', 'birth_date', 'birth_time', 'birth_place'],
            'astrological_details': ['moon_raasi', 'moon_nakshatra', 'lagna_raasi'],
            'planetary_positions': ['Sun', 'Moon', 'Mars', 'Mercury', 'Jupiter', 'Venus', 'Saturn', 'Rahu', 'Ketu'],
            'rasi_chart': [f'House_{i}' for i in range(1, 13)],
            'navamsa_chart': [f'House_{i}' for i in range(1, 13)]
        }

        total_fields = 0
        present_fields = 0

        for section, fields in required_fields.items():
            section_data = jathagam_data.get(section, {})

            for field in fields:
                total_fields += 1
                if field in section_data and section_data[field]:
                    present_fields += 1
                else:
                    validation_result['missing_fields'].append(f"{section}.{field}")

        # Calculate completeness score
        validation_result['completeness_score'] = int((present_fields / total_fields) * 100)

        # Check for warnings
        if validation_result['completeness_score'] < 80:
            validation_result['warnings'].append("ஜாதக தகவல் முழுமையாக இல்லை")

        if validation_result['completeness_score'] < 50:
            validation_result['valid'] = False
            validation_result['warnings'].append("அச்சிடுவதற்கு போதுமான தகவல் இல்லை")

        return validation_result

    def generate_print_preview(self, jathagam_data: Dict, printer_info: Dict) -> str:
        """Generate print preview HTML"""

        # Validate data first
        validation = self.validate_print_data(jathagam_data)

        if not validation['valid']:
            return self._generate_error_page(
                f"அச்சிடுவதற்கு போதுமான தகவல் இல்லை (முழுமை: {validation['completeness_score']}%)"
            )

        # Generate formatted HTML with preview styling
        formatted_html = self.format_for_printer(jathagam_data, printer_info)

        # Add preview-specific styling
        preview_html = formatted_html.replace(
            '<body>',
            '''<body>
            <div class="preview-header" style="background: #007bff; color: white; padding: 10px; text-align: center; margin-bottom: 20px; position: sticky; top: 0; z-index: 1000;">
                <h3 style="margin: 0;">🖨️ அச்சு முன்னோட்டம் (Print Preview)</h3>
                <p style="margin: 5px 0 0 0; font-size: 0.9em;">
                    அச்சுப்பொறி: ''' + printer_info.get('name', 'தெரியவில்லை') + ''' |
                    முழுமை: ''' + str(validation['completeness_score']) + '''% |
                    நிலை: ''' + ('தயார்' if printer_info.get('online', False) else 'ஆஃப்லைன்') + '''
                </p>
            </div>'''
        )

        return preview_html

# Test function
if __name__ == "__main__":
    print("🖨️ Testing Jathagam Print Formatter...")

    # Sample Jathagam data
    sample_jathagam = {
        'personal_details': {
            'name': 'அச்சு சோதனை',
            'gender': 'Male',
            'birth_date': '1990-08-15',
            'birth_time': '10:30',
            'birth_place': 'சென்னை',
            'latitude': 13.0827,
            'longitude': 80.2707,
            'timezone': 'Asia/Kolkata',
            'generated_at': '2024-01-15 14:30:00'
        },
        'astrological_details': {
            'moon_raasi': 'ரிஷபம்',
            'moon_nakshatra': 'ரோகிணி',
            'moon_pada': 2,
            'lagna_raasi': 'துலாம்',
            'lagna_nakshatra': 'சுவாதி',
            'lagna_degrees': 15.5
        },
        'planetary_positions': {
            'Sun': {'tamil_name': 'சூரியன்', 'raasi': 'சிம்மம்', 'nakshatra': 'மகம்', 'degrees_in_sign': 22.5, 'pada': 3},
            'Moon': {'tamil_name': 'சந்திரன்', 'raasi': 'ரிஷபம்', 'nakshatra': 'ரோகிணி', 'degrees_in_sign': 10.2, 'pada': 2},
            'Mars': {'tamil_name': 'செவ்வாய்', 'raasi': 'கன்னி', 'nakshatra': 'உத்திரம்', 'degrees_in_sign': 5.8, 'pada': 1}
        },
        'rasi_chart': {
            'House_1': {'rasi_name': 'துலாம்', 'planets': ['சூரியன்']},
            'House_2': {'rasi_name': 'விருச்சிகம்', 'planets': ['சந்திரன்']},
            'House_3': {'rasi_name': 'தனுசு', 'planets': []},
            'House_4': {'rasi_name': 'மகரம்', 'planets': []},
            'House_5': {'rasi_name': 'கும்பம்', 'planets': []},
            'House_6': {'rasi_name': 'மீனம்', 'planets': ['செவ்வாய்']},
            'House_7': {'rasi_name': 'மேஷம்', 'planets': []},
            'House_8': {'rasi_name': 'ரிஷபம்', 'planets': []},
            'House_9': {'rasi_name': 'மிதுனம்', 'planets': []},
            'House_10': {'rasi_name': 'கடகம்', 'planets': []},
            'House_11': {'rasi_name': 'சிம்மம்', 'planets': []},
            'House_12': {'rasi_name': 'கன்னி', 'planets': []}
        },
        'navamsa_chart': {
            'House_1': {'rasi_name': 'மேஷம்', 'planets': []},
            'House_2': {'rasi_name': 'ரிஷபம்', 'planets': ['சூரியன்']},
            'House_3': {'rasi_name': 'மிதுனம்', 'planets': []},
            'House_4': {'rasi_name': 'கடகம்', 'planets': []},
            'House_5': {'rasi_name': 'சிம்மம்', 'planets': []},
            'House_6': {'rasi_name': 'கன்னி', 'planets': []},
            'House_7': {'rasi_name': 'துலாம்', 'planets': ['சந்திரன்']},
            'House_8': {'rasi_name': 'விருச்சிகம்', 'planets': []},
            'House_9': {'rasi_name': 'தனுசு', 'planets': []},
            'House_10': {'rasi_name': 'மகரம்', 'planets': ['செவ்வாய்']},
            'House_11': {'rasi_name': 'கும்பம்', 'planets': []},
            'House_12': {'rasi_name': 'மீனம்', 'planets': []}
        }
    }

    # Sample printer info
    sample_printer = {
        'name': 'HP LaserJet Pro',
        'type': 'Laser',
        'online': True,
        'capabilities': {'color': True, 'duplex': True}
    }

    formatter = JathagamPrintFormatter()

    # Test validation
    validation = formatter.validate_print_data(sample_jathagam)
    print(f"✅ Data validation: {validation['completeness_score']}% complete")

    # Test formatting
    formatted_html = formatter.format_for_printer(sample_jathagam, sample_printer)
    print(f"✅ Formatted HTML generated: {len(formatted_html)} characters")

    # Test preview
    preview_html = formatter.generate_print_preview(sample_jathagam, sample_printer)
    print(f"✅ Print preview generated: {len(preview_html)} characters")

    print("\n🎉 Jathagam print formatter test completed!")
