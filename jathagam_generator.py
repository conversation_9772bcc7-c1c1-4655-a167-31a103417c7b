#!/usr/bin/env python3
"""
Tamil Jathagam (Horoscope) Generator
Based on birth date, time, and place for Tamil Nadu
"""
import json
from datetime import datetime, date, time
import math

# Tamil Zodiac Signs (Raasi)
TAMIL_RAASI = [
    'மேஷம்', 'ரிஷபம்', 'மிதுனம்', 'கடகம்', 'சிம்மம்', 'கன்னி',
    'துலாம்', 'விருச்சிகம்', 'தனுசு', 'மகரம்', 'கும்பம்', 'மீனம்'
]

# Tamil Nakshatras (Stars)
TAMIL_NAKSHATRAS = [
    'அசுவினி', 'பரணி', 'கார்த்திகை', 'ரோகிணி', 'மிருகசீரிடம்', 'திருவாதிரை',
    'புனர்பூசம்', 'பூசம்', 'ஆயில்யம்', 'மகம்', 'பூரம்', 'உத்திரம்',
    'அஸ்தம்', 'சித்திரை', 'சுவாதி', 'விசாகம்', 'அனுஷம்', 'கேட்டை',
    'மூலம்', 'பூராடம்', 'உத்திராடம்', 'திருவோணம்', 'அவிட்டம்', 'சதயம்',
    'பூரட்டாதி', 'உத்திரட்டாதி', 'ரேவதி'
]

# Planets in Tamil
TAMIL_PLANETS = {
    'sun': 'சூரியன்',
    'moon': 'சந்திரன்',
    'mars': 'செவ்வாய்',
    'mercury': 'புதன்',
    'jupiter': 'குரு',
    'venus': 'சுக்கிரன்',
    'saturn': 'சனி',
    'rahu': 'ராகு',
    'ketu': 'கேது'
}

# Houses in Tamil
TAMIL_HOUSES = [
    'லக்னம்', 'தன', 'சகோதர', 'மாதா', 'புத்திர', 'ரிபு',
    'கலத்திர', 'ஆயுள்', 'பாக்கிய', 'கர்ம', 'லாப', 'வ்யய'
]

def calculate_julian_day(birth_date, birth_time):
    """Calculate Julian Day Number"""
    year = birth_date.year
    month = birth_date.month
    day = birth_date.day
    hour = birth_time.hour
    minute = birth_time.minute
    
    # Convert to decimal day
    decimal_day = day + (hour + minute/60.0) / 24.0
    
    # Julian Day calculation
    if month <= 2:
        year -= 1
        month += 12
    
    a = int(year / 100)
    b = 2 - a + int(a / 4)
    
    jd = int(365.25 * (year + 4716)) + int(30.6001 * (month + 1)) + decimal_day + b - 1524.5
    
    return jd

def calculate_sun_position(julian_day):
    """Calculate Sun's position (simplified)"""
    # Days since J2000.0
    n = julian_day - 2451545.0
    
    # Mean longitude of Sun
    L = (280.460 + 0.9856474 * n) % 360
    
    # Mean anomaly
    g = math.radians((357.528 + 0.9856003 * n) % 360)
    
    # Ecliptic longitude
    lambda_sun = L + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g)
    
    return lambda_sun % 360

def calculate_moon_position(julian_day):
    """Calculate Moon's position (simplified)"""
    # Days since J2000.0
    n = julian_day - 2451545.0
    
    # Mean longitude of Moon
    L = (218.316 + 13.176396 * n) % 360
    
    # Mean anomaly of Moon
    M = math.radians((134.963 + 13.064993 * n) % 360)
    
    # Mean anomaly of Sun
    M_sun = math.radians((357.528 + 0.9856003 * n) % 360)
    
    # Argument of latitude
    F = math.radians((93.272 + 13.229350 * n) % 360)
    
    # Longitude correction
    lambda_moon = L + 6.289 * math.sin(M) - 1.274 * math.sin(M - 2*F) + 0.658 * math.sin(2*F)
    
    return lambda_moon % 360

def get_raasi_from_longitude(longitude):
    """Get Raasi (zodiac sign) from longitude"""
    raasi_index = int(longitude / 30)
    return TAMIL_RAASI[raasi_index % 12]

def get_nakshatra_from_longitude(longitude):
    """Get Nakshatra from longitude"""
    # Each nakshatra is 13.333... degrees
    nakshatra_index = int(longitude / (360/27))
    return TAMIL_NAKSHATRAS[nakshatra_index % 27]

def calculate_ascendant(julian_day, latitude=13.0827, longitude_place=80.2707):
    """Calculate Ascendant (Lagna) - simplified for Chennai coordinates"""
    # This is a simplified calculation
    # In reality, this requires complex astronomical calculations
    
    # Local Sidereal Time calculation (simplified)
    n = julian_day - 2451545.0
    lst = (280.460618 + 360.98564736629 * n + longitude_place) % 360
    
    # Ascendant calculation (very simplified)
    # This would normally require the obliquity of ecliptic and more complex math
    ascendant = (lst + 90) % 360  # Simplified approximation
    
    return ascendant

def generate_jathagam(name, birth_date, birth_time, birth_place="சென்னை"):
    """Generate a Tamil Jathagam"""
    try:
        # Convert string inputs to proper types if needed
        if isinstance(birth_date, str):
            birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
        if isinstance(birth_time, str):
            birth_time = datetime.strptime(birth_time, '%H:%M').time()
        
        # Calculate Julian Day
        jd = calculate_julian_day(birth_date, birth_time)
        
        # Calculate planetary positions
        sun_longitude = calculate_sun_position(jd)
        moon_longitude = calculate_moon_position(jd)
        ascendant = calculate_ascendant(jd)
        
        # Calculate other planets (simplified - using offsets from Sun)
        # In reality, each planet needs its own orbital calculation
        planets = {
            'சூரியன்': sun_longitude,
            'சந்திரன்': moon_longitude,
            'செவ்வாய்': (sun_longitude + 45) % 360,  # Simplified
            'புதன்': (sun_longitude + 15) % 360,   # Simplified
            'குரு': (sun_longitude + 120) % 360,    # Simplified
            'சுக்கிரன்': (sun_longitude + 30) % 360, # Simplified
            'சனி': (sun_longitude + 180) % 360,    # Simplified
            'ராகு': (moon_longitude + 180) % 360,   # Simplified
            'கேது': (moon_longitude) % 360         # Simplified
        }
        
        # Calculate Raasi and Nakshatra
        moon_raasi = get_raasi_from_longitude(moon_longitude)
        moon_nakshatra = get_nakshatra_from_longitude(moon_longitude)
        lagna_raasi = get_raasi_from_longitude(ascendant)
        
        # Create house chart (simplified)
        houses = {}
        for i in range(12):
            house_longitude = (ascendant + i * 30) % 360
            house_raasi = get_raasi_from_longitude(house_longitude)
            houses[TAMIL_HOUSES[i]] = {
                'raasi': house_raasi,
                'planets': []
            }
        
        # Place planets in houses
        for planet, longitude in planets.items():
            house_index = int((longitude - ascendant) / 30) % 12
            house_name = TAMIL_HOUSES[house_index]
            houses[house_name]['planets'].append(planet)
        
        # Generate Jathagam data
        jathagam = {
            'name': name,
            'birth_date': birth_date.strftime('%Y-%m-%d'),
            'birth_time': birth_time.strftime('%H:%M'),
            'birth_place': birth_place,
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'moon_raasi': moon_raasi,
            'moon_nakshatra': moon_nakshatra,
            'lagna_raasi': lagna_raasi,
            'planets': planets,
            'houses': houses,
            'planetary_positions': {
                planet: {
                    'longitude': round(longitude, 2),
                    'raasi': get_raasi_from_longitude(longitude),
                    'nakshatra': get_nakshatra_from_longitude(longitude)
                }
                for planet, longitude in planets.items()
            }
        }
        
        return jathagam
        
    except Exception as e:
        print(f"Error generating Jathagam: {e}")
        return None

def format_jathagam_for_display(jathagam_data):
    """Format Jathagam data for display"""
    if not jathagam_data:
        return "ஜாதக தகவல் கிடைக்கவில்லை"
    
    if isinstance(jathagam_data, str):
        try:
            jathagam_data = json.loads(jathagam_data)
        except:
            return "ஜாதக தகவல் பிழை"
    
    formatted = f"""
    🌟 ஜாதக விவரங்கள் 🌟
    
    பெயர்: {jathagam_data.get('name', 'தெரியவில்லை')}
    பிறந்த தேதி: {jathagam_data.get('birth_date', 'தெரியவில்லை')}
    பிறந்த நேரம்: {jathagam_data.get('birth_time', 'தெரியவில்லை')}
    பிறந்த இடம்: {jathagam_data.get('birth_place', 'தெரியவில்லை')}
    
    🌙 சந்திர ராசி: {jathagam_data.get('moon_raasi', 'தெரியவில்லை')}
    ⭐ நட்சத்திரம்: {jathagam_data.get('moon_nakshatra', 'தெரியவில்லை')}
    🏠 லக்ன ராசி: {jathagam_data.get('lagna_raasi', 'தெரியவில்லை')}
    
    🪐 கிரக நிலைகள்:
    """
    
    planetary_positions = jathagam_data.get('planetary_positions', {})
    for planet, details in planetary_positions.items():
        formatted += f"    {planet}: {details.get('raasi', 'தெரியவில்லை')} ({details.get('nakshatra', 'தெரியவில்லை')})\n"
    
    return formatted

if __name__ == "__main__":
    # Test the Jathagam generator
    test_date = date(1990, 1, 15)
    test_time = time(10, 30)
    test_jathagam = generate_jathagam("சோதனை", test_date, test_time)
    
    if test_jathagam:
        print("✅ Jathagam generated successfully!")
        print(format_jathagam_for_display(test_jathagam))
    else:
        print("❌ Failed to generate Jathagam")
