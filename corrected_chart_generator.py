#!/usr/bin/env python3
"""
Corrected Chart Generator following exact logical architecture
STEP 1-5 implementation as specified
"""
import swisseph as swe
from geopy.geocoders import Nominatim
from timezonefinder import TimezoneFinder
import pytz
from datetime import datetime, date, time
from typing import Dict, List, Tuple, Optional
import math

class CorrectedChartGenerator:
    """
    Implements exact logical architecture for Rasi & Navamsa chart generation
    Following STEP 1-5 process as specified
    """
    
    def __init__(self):
        # Set <PERSON>hir<PERSON> for Tamil Nadu
        swe.set_sid_mode(swe.SIDM_LAHIRI)
        
        # Initialize location services
        self.geolocator = Nominatim(user_agent="corrected_chart_generator")
        self.tf = TimezoneFinder()
        
        # Tamil Rasi names (12 zodiac signs)
        self.tamil_rasi_names = [
            'மேஷம்',      # Aries (0°-30°)
            'ரிஷபம்',     # Tau<PERSON> (30°-60°)
            'மிதுனம்',     # <PERSON> (60°-90°)
            'கடகம்',      # <PERSON> (90°-120°)
            'சிம்மம்',     # <PERSON> (120°-150°)
            'கன்னி',      # <PERSON>ir<PERSON> (150°-180°)
            'துலாம்',     # <PERSON><PERSON> (180°-210°)
            'விருச்சிகம்', # <PERSON><PERSON><PERSON> (210°-240°)
            'தனுசு',      # <PERSON><PERSON><PERSON><PERSON> (240°-270°)
            'மகரம்',      # Capricorn (270°-300°)
            'கும்பம்',     # Aquarius (300°-330°)
            'மீனம்'       # Pisces (330°-360°)
        ]
        
        # Tamil planet names
        self.tamil_planet_names = {
            'Sun': 'சூரியன்',
            'Moon': 'சந்திரன்',
            'Mars': 'செவ்வாய்',
            'Mercury': 'புதன்',
            'Jupiter': 'குரு',
            'Venus': 'சுக்கிரன்',
            'Saturn': 'சனி',
            'Rahu': 'ராகு',
            'Ketu': 'கேது'
        }
        
        # Swiss Ephemeris planet constants
        self.planet_constants = {
            'Sun': swe.SUN,
            'Moon': swe.MOON,
            'Mars': swe.MARS,
            'Mercury': swe.MERCURY,
            'Jupiter': swe.JUPITER,
            'Venus': swe.VENUS,
            'Saturn': swe.SATURN,
            'Rahu': swe.MEAN_NODE,
            'Ketu': swe.MEAN_NODE  # Ketu is 180° opposite to Rahu
        }
        
        # Tamil Nakshatra names (27 stars)
        self.tamil_nakshatra_names = [
            'அசுவினி', 'பரணி', 'கார்த்திகை', 'ரோகிணி', 'மிருகசீர்ஷம்', 'திருவாதிரை',
            'புனர்பூசம்', 'பூசம்', 'ஆயில்யம்', 'மகம்', 'பூரம்', 'உத்திரம்',
            'ஹஸ்தம்', 'சித்திரை', 'சுவாதி', 'விசாகம்', 'அனுஷம்', 'கேட்டை',
            'மூலம்', 'பூராடம்', 'உத்திராடம்', 'திருவோணம்', 'அவிட்டம்', 'சதயம்',
            'பூரட்டாதி', 'உத்திரட்டாதி', 'ரேவதி'
        ]
    
    # STEP 1: INPUT PARAMETERS
    def process_input_parameters(self, birth_date: date, birth_time: time, birth_place: str) -> Dict:
        """
        STEP 1: Collect and process input parameters
        """
        try:
            # Get coordinates and timezone
            latitude, longitude, timezone_str = self.get_coordinates_and_timezone(birth_place)
            
            # Calculate Julian Day
            julian_day = self.calculate_julian_day(birth_date, birth_time, timezone_str)
            
            # Get Ayanamsa (Lahiri for Tamil Nadu)
            ayanamsa = swe.get_ayanamsa(julian_day)
            
            return {
                'birth_date': birth_date,
                'birth_time': birth_time,
                'birth_place': birth_place,
                'latitude': latitude,
                'longitude': longitude,
                'timezone': timezone_str,
                'julian_day': julian_day,
                'ayanamsa': ayanamsa,
                'calculation_method': 'Swiss Ephemeris with Lahiri Ayanamsa'
            }
            
        except Exception as e:
            print(f"Input parameter processing error: {e}")
            return None
    
    def get_coordinates_and_timezone(self, place_name: str) -> Tuple[float, float, str]:
        """Get latitude, longitude and timezone for a place"""
        try:
            search_query = f"{place_name}, Tamil Nadu, India"
            location = self.geolocator.geocode(search_query, timeout=10)
            
            if location:
                lat, lng = location.latitude, location.longitude
                timezone_str = self.tf.timezone_at(lat=lat, lng=lng)
                return lat, lng, timezone_str or 'Asia/Kolkata'
            else:
                return 13.0827, 80.2707, 'Asia/Kolkata'  # Default to Chennai
                
        except Exception as e:
            print(f"Location error: {e}")
            return 13.0827, 80.2707, 'Asia/Kolkata'
    
    def calculate_julian_day(self, birth_date: date, birth_time: time, timezone_str: str) -> float:
        """Calculate Julian Day for the birth date and time"""
        try:
            tz = pytz.timezone(timezone_str)
            birth_datetime = datetime.combine(birth_date, birth_time)
            localized_dt = tz.localize(birth_datetime)
            utc_dt = localized_dt.astimezone(pytz.UTC)
            
            return swe.julday(
                utc_dt.year, utc_dt.month, utc_dt.day,
                utc_dt.hour + utc_dt.minute/60.0 + utc_dt.second/3600.0
            )
            
        except Exception as e:
            print(f"Julian day calculation error: {e}")
            return swe.julday(birth_date.year, birth_date.month, birth_date.day, 12.0)
    
    # STEP 2: EPHEMERIS CALCULATIONS
    def calculate_ephemeris_data(self, input_params: Dict) -> Dict:
        """
        STEP 2: Calculate planetary positions using Swiss Ephemeris
        """
        try:
            julian_day = input_params['julian_day']
            latitude = input_params['latitude']
            longitude = input_params['longitude']
            
            # Calculate planetary longitudes
            planetary_positions = self.calculate_planetary_longitudes(julian_day)
            
            # Calculate Ascendant (Lagna)
            lagna_data = self.calculate_lagna_and_houses(julian_day, latitude, longitude)
            
            # Calculate Moon's Nakshatra and Pada
            moon_nakshatra_data = self.calculate_moon_nakshatra(planetary_positions.get('Moon', {}))
            
            return {
                'planetary_positions': planetary_positions,
                'lagna_data': lagna_data,
                'moon_nakshatra_data': moon_nakshatra_data,
                'house_cusps': lagna_data.get('house_cusps', [])
            }
            
        except Exception as e:
            print(f"Ephemeris calculation error: {e}")
            return None
    
    def calculate_planetary_longitudes(self, julian_day: float) -> Dict:
        """Calculate sidereal planetary positions"""
        planetary_positions = {}
        
        for planet_name, planet_const in self.planet_constants.items():
            try:
                if planet_name == 'Ketu':
                    # Ketu is 180° opposite to Rahu
                    rahu_result = swe.calc_ut(julian_day, swe.MEAN_NODE, swe.FLG_SIDEREAL)
                    longitude = (rahu_result[0][0] + 180) % 360
                else:
                    result = swe.calc_ut(julian_day, planet_const, swe.FLG_SIDEREAL)
                    longitude = result[0][0]
                
                # Calculate Rasi using exact logic: longitude / 30
                rasi_number = int(longitude / 30) + 1
                degrees_in_sign = longitude % 30
                
                # Calculate Nakshatra (13°20' segments)
                nakshatra_number = int(longitude / (360/27)) + 1
                nakshatra_degrees = longitude % (360/27)
                pada = int(nakshatra_degrees / (360/27/4)) + 1
                
                planetary_positions[planet_name] = {
                    'longitude': round(longitude, 6),
                    'rasi_number': rasi_number,
                    'rasi_name': self.tamil_rasi_names[rasi_number - 1],
                    'degrees_in_sign': round(degrees_in_sign, 2),
                    'nakshatra_number': nakshatra_number,
                    'nakshatra_name': self.tamil_nakshatra_names[nakshatra_number - 1],
                    'pada': pada,
                    'tamil_name': self.tamil_planet_names[planet_name]
                }
                
            except Exception as e:
                print(f"Error calculating {planet_name}: {e}")
                continue
        
        return planetary_positions
    
    def calculate_lagna_and_houses(self, julian_day: float, latitude: float, longitude: float) -> Dict:
        """Calculate Lagna (Ascendant) and house cusps"""
        try:
            # Calculate houses using Placidus system
            houses = swe.houses(julian_day, latitude, longitude, b'P')
            ascendant_longitude = houses[1][0]  # Ascendant is the first house cusp
            
            # Apply sidereal correction
            ayanamsa = swe.get_ayanamsa(julian_day)
            sidereal_ascendant = (ascendant_longitude - ayanamsa) % 360
            
            # Calculate Rasi and Nakshatra for Lagna using exact logic
            lagna_rasi_number = int(sidereal_ascendant / 30) + 1
            lagna_degrees_in_sign = sidereal_ascendant % 30
            
            lagna_nakshatra_number = int(sidereal_ascendant / (360/27)) + 1
            lagna_nakshatra_degrees = sidereal_ascendant % (360/27)
            lagna_pada = int(lagna_nakshatra_degrees / (360/27/4)) + 1
            
            # Calculate all house cusps
            house_cusps = []
            for i in range(12):
                cusp_longitude = (houses[1][i] - ayanamsa) % 360
                cusp_rasi = int(cusp_longitude / 30) + 1
                house_cusps.append({
                    'house': i + 1,
                    'longitude': round(cusp_longitude, 6),
                    'rasi_number': cusp_rasi,
                    'rasi_name': self.tamil_rasi_names[cusp_rasi - 1]
                })
            
            return {
                'longitude': round(sidereal_ascendant, 6),
                'rasi_number': lagna_rasi_number,
                'rasi_name': self.tamil_rasi_names[lagna_rasi_number - 1],
                'degrees_in_sign': round(lagna_degrees_in_sign, 2),
                'nakshatra_number': lagna_nakshatra_number,
                'nakshatra_name': self.tamil_nakshatra_names[lagna_nakshatra_number - 1],
                'pada': lagna_pada,
                'house_cusps': house_cusps
            }
            
        except Exception as e:
            print(f"Lagna calculation error: {e}")
            return {
                'longitude': 0,
                'rasi_number': 1,
                'rasi_name': 'மேஷம்',
                'degrees_in_sign': 0,
                'nakshatra_number': 1,
                'nakshatra_name': 'அசுவினி',
                'pada': 1,
                'house_cusps': []
            }
    
    def calculate_moon_nakshatra(self, moon_data: Dict) -> Dict:
        """Calculate Moon's Nakshatra and Pada details"""
        if not moon_data:
            return {'nakshatra': 'அசுவினி', 'pada': 1, 'lord': 'கேது'}
        
        nakshatra_name = moon_data.get('nakshatra_name', 'அசுவினி')
        pada = moon_data.get('pada', 1)
        
        # Nakshatra lords for Dasa calculation
        nakshatra_lords = [
            'கேது', 'சுக்கிரன்', 'சூரியன்',     # Ashwini, Bharani, Krittika
            'சந்திரன்', 'செவ்வாய்', 'ராகு',      # Rohini, Mrigashira, Ardra
            'குரு', 'சனி', 'புதன்',             # Punarvasu, Pushya, Ashlesha
            'கேது', 'சுக்கிரன்', 'சூரியன்',     # Magha, Purva Phalguni, Uttara Phalguni
            'சந்திரன்', 'செவ்வாய்', 'ராகு',      # Hasta, Chitra, Swati
            'குரு', 'சனி', 'புதன்',             # Vishakha, Anuradha, Jyeshtha
            'கேது', 'சுக்கிரன்', 'சூரியன்',     # Mula, Purva Ashadha, Uttara Ashadha
            'சந்திரன்', 'செவ்வாய்', 'ராகு',      # Shravana, Dhanishta, Shatabhisha
            'குரு', 'சனி', 'புதன்'              # Purva Bhadrapada, Uttara Bhadrapada, Revati
        ]
        
        nakshatra_number = moon_data.get('nakshatra_number', 1)
        lord_index = (nakshatra_number - 1) % 9
        nakshatra_lord = nakshatra_lords[lord_index]
        
        return {
            'nakshatra': nakshatra_name,
            'pada': pada,
            'lord': nakshatra_lord,
            'nakshatra_number': nakshatra_number
        }

    # STEP 3: RASI CHART GENERATION LOGIC
    def generate_rasi_chart(self, ephemeris_data: Dict) -> Dict:
        """
        STEP 3: Generate Rasi chart using exact logical architecture
        Assign planets to Rasi houses (12 boxes)
        """
        try:
            planetary_positions = ephemeris_data['planetary_positions']
            lagna_data = ephemeris_data['lagna_data']
            lagna_rasi = lagna_data['rasi_number']

            # Initialize all 12 houses
            rasi_chart = {}
            for house_num in range(1, 13):
                # Calculate which Rasi falls in this house
                # House 1 = Lagna Rasi, House 2 = Next Rasi, etc.
                rasi_in_house = ((lagna_rasi - 1 + house_num - 1) % 12) + 1
                rasi_name = self.tamil_rasi_names[rasi_in_house - 1]

                rasi_chart[f'House_{house_num}'] = {
                    'house_number': house_num,
                    'rasi_number': rasi_in_house,
                    'rasi_name': rasi_name,
                    'planets': [],
                    'planet_details': []
                }

            # Place planets in houses based on their Rasi
            # Calculate distance from Lagna to determine house
            for planet_name, planet_data in planetary_positions.items():
                planet_rasi = planet_data['rasi_number']

                # Find which house this Rasi falls in
                # Distance from Lagna Rasi determines house number
                house_number = ((planet_rasi - lagna_rasi) % 12) + 1
                house_key = f'House_{house_number}'

                if house_key in rasi_chart:
                    rasi_chart[house_key]['planets'].append(planet_data['tamil_name'])
                    rasi_chart[house_key]['planet_details'].append({
                        'name': planet_data['tamil_name'],
                        'degrees': planet_data['degrees_in_sign'],
                        'nakshatra': planet_data['nakshatra_name'],
                        'pada': planet_data['pada']
                    })

            return rasi_chart

        except Exception as e:
            print(f"Rasi chart generation error: {e}")
            return {}

    # STEP 4: NAVAMSA CHART LOGIC
    def generate_navamsa_chart(self, ephemeris_data: Dict) -> Dict:
        """
        STEP 4: Generate Navamsa chart using exact D9 logic
        Divide each Rasi into 9 equal parts (padas) of 3°20′ (3.333°)
        """
        try:
            planetary_positions = ephemeris_data['planetary_positions']
            lagna_data = ephemeris_data['lagna_data']

            # Calculate Navamsa Lagna
            lagna_navamsa_rasi = self.calculate_navamsa_rasi(lagna_data['longitude'])

            # Initialize all 12 houses for Navamsa chart
            navamsa_chart = {}
            for house_num in range(1, 13):
                # Calculate which Navamsa Rasi falls in this house
                rasi_in_house = ((lagna_navamsa_rasi - 1 + house_num - 1) % 12) + 1
                rasi_name = self.tamil_rasi_names[rasi_in_house - 1]

                navamsa_chart[f'House_{house_num}'] = {
                    'house_number': house_num,
                    'rasi_number': rasi_in_house,
                    'rasi_name': rasi_name,
                    'planets': [],
                    'planet_details': []
                }

            # Place planets in Navamsa houses
            for planet_name, planet_data in planetary_positions.items():
                planet_navamsa_rasi = self.calculate_navamsa_rasi(planet_data['longitude'])

                # Find which house this Navamsa Rasi falls in
                house_number = ((planet_navamsa_rasi - lagna_navamsa_rasi) % 12) + 1
                house_key = f'House_{house_number}'

                if house_key in navamsa_chart:
                    navamsa_chart[house_key]['planets'].append(planet_data['tamil_name'])
                    navamsa_chart[house_key]['planet_details'].append({
                        'name': planet_data['tamil_name'],
                        'degrees': planet_data['degrees_in_sign'],
                        'navamsa_degrees': (planet_data['longitude'] % 30) % 3.333
                    })

            return navamsa_chart

        except Exception as e:
            print(f"Navamsa chart generation error: {e}")
            return {}

    def calculate_navamsa_rasi(self, longitude: float) -> int:
        """
        Calculate Navamsa Rasi using exact D9 chart rules
        Each sign maps differently to Navamsa signs
        """
        # Get the main Rasi
        main_rasi = int(longitude / 30) + 1

        # Get position within the Rasi (0° to 30°)
        rasi_position = longitude % 30

        # Calculate pada (1 to 9) - each pada is 3°20' (3.333°)
        pada = int(rasi_position / 3.333) + 1
        if pada > 9:
            pada = 9

        # Navamsa Chart Mapping Rules (exact implementation)
        # Fire signs (1,5,9): Aries, Leo, Sagittarius - Forward (1 to 9)
        # Earth signs (2,6,10): Taurus, Virgo, Capricorn - Forward (1 to 9)
        # Air signs (3,7,11): Gemini, Libra, Aquarius - Forward (1 to 9)
        # Water signs (4,8,12): Cancer, Scorpio, Pisces - Forward (1 to 9)

        fire_signs = [1, 5, 9]    # Aries, Leo, Sagittarius
        earth_signs = [2, 6, 10]  # Taurus, Virgo, Capricorn
        air_signs = [3, 7, 11]    # Gemini, Libra, Aquarius
        water_signs = [4, 8, 12]  # Cancer, Scorpio, Pisces

        if main_rasi in fire_signs:
            # Fire signs: Start from Aries (1) and count forward
            navamsa_rasi = ((pada - 1) % 12) + 1
        elif main_rasi in earth_signs:
            # Earth signs: Start from Capricorn (10) and count forward
            navamsa_rasi = ((10 - 1 + pada - 1) % 12) + 1
        elif main_rasi in air_signs:
            # Air signs: Start from Libra (7) and count forward
            navamsa_rasi = ((7 - 1 + pada - 1) % 12) + 1
        elif main_rasi in water_signs:
            # Water signs: Start from Cancer (4) and count forward
            navamsa_rasi = ((4 - 1 + pada - 1) % 12) + 1
        else:
            # Default case
            navamsa_rasi = ((pada - 1) % 12) + 1

        return navamsa_rasi

    # STEP 5: FINAL VISUALIZATION
    def generate_complete_charts(self, name: str, birth_date: date, birth_time: time,
                                birth_place: str, gender: str = 'Male') -> Dict:
        """
        STEP 5: Complete chart generation following exact logical architecture
        """
        try:
            # STEP 1: Process input parameters
            input_params = self.process_input_parameters(birth_date, birth_time, birth_place)
            if not input_params:
                return None

            # STEP 2: Calculate ephemeris data
            ephemeris_data = self.calculate_ephemeris_data(input_params)
            if not ephemeris_data:
                return None

            # STEP 3: Generate Rasi chart
            rasi_chart = self.generate_rasi_chart(ephemeris_data)

            # STEP 4: Generate Navamsa chart
            navamsa_chart = self.generate_navamsa_chart(ephemeris_data)

            # Compile complete data
            complete_chart_data = {
                'personal_details': {
                    'name': name,
                    'gender': gender,
                    'birth_date': birth_date.strftime('%Y-%m-%d'),
                    'birth_time': birth_time.strftime('%H:%M'),
                    'birth_place': birth_place,
                    'latitude': input_params['latitude'],
                    'longitude': input_params['longitude'],
                    'timezone': input_params['timezone'],
                    'julian_day': input_params['julian_day'],
                    'ayanamsa': input_params['ayanamsa'],
                    'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                'astrological_details': {
                    'lagna_rasi': ephemeris_data['lagna_data']['rasi_name'],
                    'lagna_nakshatra': ephemeris_data['lagna_data']['nakshatra_name'],
                    'lagna_pada': ephemeris_data['lagna_data']['pada'],
                    'lagna_degrees': ephemeris_data['lagna_data']['degrees_in_sign'],
                    'moon_raasi': ephemeris_data['planetary_positions'].get('Moon', {}).get('rasi_name', 'தெரியவில்லை'),
                    'moon_nakshatra': ephemeris_data['moon_nakshatra_data']['nakshatra'],
                    'moon_pada': ephemeris_data['moon_nakshatra_data']['pada'],
                    'moon_degrees': ephemeris_data['planetary_positions'].get('Moon', {}).get('degrees_in_sign', 0)
                },
                'planetary_positions': ephemeris_data['planetary_positions'],
                'lagna_details': ephemeris_data['lagna_data'],
                'rasi_chart': rasi_chart,
                'navamsa_chart': navamsa_chart,
                'calculation_method': 'Swiss Ephemeris with Lahiri Ayanamsa - Exact Logical Architecture',
                'architecture_steps': 'STEP 1-5 Implementation: Input→Ephemeris→Rasi→Navamsa→Visualization'
            }

            return complete_chart_data

        except Exception as e:
            print(f"Complete chart generation error: {e}")
            return None

# Test function
if __name__ == "__main__":
    print("🔧 Testing Corrected Chart Generator...")
    print("Following exact STEP 1-5 logical architecture\n")

    # Test data
    test_name = "சரியான கணக்கீடு"
    test_date = date(1990, 8, 15)
    test_time = time(10, 30)
    test_place = "சென்னை"
    test_gender = "Male"

    # Generate charts using corrected logic
    generator = CorrectedChartGenerator()
    chart_data = generator.generate_complete_charts(
        test_name, test_date, test_time, test_place, test_gender
    )

    if chart_data:
        print("✅ Corrected chart generation successful!")

        # Display key information
        personal = chart_data.get('personal_details', {})
        astro = chart_data.get('astrological_details', {})
        planets = chart_data.get('planetary_positions', {})

        print(f"   Name: {personal.get('name')}")
        print(f"   Lagna Rasi: {astro.get('lagna_rasi')}")
        print(f"   Moon Rasi: {astro.get('moon_raasi')}")
        print(f"   Planets calculated: {len(planets)}")

        # Test planetary positions display
        print("\n🪐 கிரக நிலைகள் (Swiss Ephemeris கணக்கீடு):")
        for planet_name, planet_data in planets.items():
            print(f"   {planet_data['tamil_name']}: {planet_data['rasi_name']} - {planet_data['nakshatra_name']} {planet_data['pada']}ம் பாதம் ({planet_data['degrees_in_sign']:.2f}°)")

        # Check charts
        rasi_chart = chart_data.get('rasi_chart', {})
        navamsa_chart = chart_data.get('navamsa_chart', {})

        print(f"\n📊 Chart Structure:")
        print(f"   Rasi Chart Houses: {len(rasi_chart)}")
        print(f"   Navamsa Chart Houses: {len(navamsa_chart)}")

    else:
        print("❌ Corrected chart generation failed")

    print("\n🎉 Corrected Chart Generator test completed!")
