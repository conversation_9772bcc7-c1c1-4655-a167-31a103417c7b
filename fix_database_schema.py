#!/usr/bin/env python3
"""
Fix database schema by adding missing columns
"""
import os
import sys
import sqlite3

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_database():
    """Add missing columns to the database"""
    db_path = os.path.join(os.getcwd(), 'instance', 'user_documents.db')
    
    print(f"Fixing database at: {db_path}")
    
    try:
        # Connect to SQLite database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check existing columns
        cursor.execute("PRAGMA table_info(document)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"Existing columns: {columns}")
        
        # Add missing columns
        if 'natchathiram' not in columns:
            print("Adding natchathiram column...")
            cursor.execute("ALTER TABLE document ADD COLUMN natchathiram VARCHAR(50)")
        else:
            print("natchathiram column already exists")
        
        if 'raasi' not in columns:
            print("Adding raasi column...")
            cursor.execute("ALTER TABLE document ADD COLUMN raasi VARCHAR(50)")
        else:
            print("raasi column already exists")
        
        if 'vayathu' not in columns:
            print("Adding vayathu column...")
            cursor.execute("ALTER TABLE document ADD COLUMN vayathu INTEGER")
        else:
            print("vayathu column already exists")
        
        # Commit changes
        conn.commit()
        
        # Verify columns were added
        cursor.execute("PRAGMA table_info(document)")
        new_columns = [row[1] for row in cursor.fetchall()]
        print(f"Updated columns: {new_columns}")
        
        conn.close()
        print("✅ Database schema fixed successfully!")
        
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        return False
    
    return True

if __name__ == "__main__":
    fix_database()
