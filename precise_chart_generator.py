#!/usr/bin/env python3
"""
Precise Rasi & Navamsa Chart Generator
Following the exact logical architecture specified for Tamil Jathagam
"""
import json
import math
from datetime import datetime, date, time
from typing import Dict, List, Tuple, Optional

try:
    import swisseph as swe
    from geopy.geocoders import Nominatim
    from timezonefinder import TimezoneFinder
    import pytz
    LIBRARIES_AVAILABLE = True
except ImportError as e:
    print(f"Required libraries not available: {e}")
    LIBRARIES_AVAILABLE = False

# Tamil Zodiac Signs (Raasi) - 12 signs
TAMIL_RAASI = [
    'மேஷம்', 'ரிஷபம்', 'மிதுனம்', 'கடகம்', 'சிம்மம்', 'கன்னி',
    'துலாம்', 'விருச்சிகம்', 'தனுசு', 'மகரம்', 'கும்பம்', 'மீனம்'
]

# Tamil Nakshatras (Stars) - 27 stars
TAMIL_NAKSHATRAS = [
    'அசுவினி', 'பரணி', 'கார்த்திகை', 'ரோகிணி', 'மிருகசீரிடம்', 'திருவாதிரை',
    'புனர்பூசம்', 'பூசம்', 'ஆயில்யம்', 'மகம்', 'பூரம்', 'உத்திரம்',
    'அஸ்தம்', 'சித்திரை', 'சுவாதி', 'விசாகம்', 'அனுஷம்', 'கேட்டை',
    'மூலம்', 'பூராடம்', 'உத்திராடம்', 'திருவோணம்', 'அவிட்டம்', 'சதயம்',
    'பூரட்டாதி', 'உத்திரட்டாதி', 'ரேவதி'
]

# Planets in Tamil
TAMIL_PLANETS = {
    'Sun': 'சூரியன்',
    'Moon': 'சந்திரன்',
    'Mars': 'செவ்வாய்',
    'Mercury': 'புதன்',
    'Jupiter': 'குரு',
    'Venus': 'சுக்கிரன்',
    'Saturn': 'சனி',
    'Rahu': 'ராகு',
    'Ketu': 'கேது'
}

# Swiss Ephemeris planet constants
PLANET_CONSTANTS = {
    'Sun': swe.SUN,
    'Moon': swe.MOON,
    'Mars': swe.MARS,
    'Mercury': swe.MERCURY,
    'Jupiter': swe.JUPITER,
    'Venus': swe.VENUS,
    'Saturn': swe.SATURN,
    'Rahu': swe.MEAN_NODE,
    'Ketu': swe.MEAN_NODE  # Ketu is 180° opposite to Rahu
}

class PreciseChartGenerator:
    """Generate precise Rasi and Navamsa charts using exact logical architecture"""
    
    def __init__(self):
        if not LIBRARIES_AVAILABLE:
            raise ImportError("Required libraries not available")
        
        self.geolocator = Nominatim(user_agent="precise_jathagam_generator")
        self.tf = TimezoneFinder()
        # Set Lahiri Ayanamsa (standard for Tamil Nadu)
        swe.set_sid_mode(swe.SIDM_LAHIRI)
    
    def get_coordinates_and_timezone(self, place_name: str) -> Tuple[float, float, str]:
        """STEP 1: Get latitude, longitude, and timezone from place name"""
        try:
            # Add Tamil Nadu, India for better accuracy
            search_query = f"{place_name}, Tamil Nadu, India"
            location = self.geolocator.geocode(search_query)
            
            if location:
                lat, lng = location.latitude, location.longitude
            else:
                # Fallback to Chennai coordinates
                lat, lng = 13.0827, 80.2707
            
            # Get timezone
            timezone_str = self.tf.timezone_at(lat=lat, lng=lng) or 'Asia/Kolkata'
            
            return lat, lng, timezone_str
            
        except Exception as e:
            print(f"Location error: {e}")
            return 13.0827, 80.2707, 'Asia/Kolkata'
    
    def calculate_julian_day(self, birth_date: date, birth_time: time, timezone_str: str) -> float:
        """STEP 1: Convert DOB, TOB, Place → Julian Day"""
        try:
            # Create datetime object
            dt = datetime.combine(birth_date, birth_time)
            
            # Apply timezone
            tz = pytz.timezone(timezone_str)
            local_dt = tz.localize(dt)
            utc_dt = local_dt.utctimetuple()
            
            # Calculate Julian Day
            jd = swe.julday(
                utc_dt.tm_year,
                utc_dt.tm_mon,
                utc_dt.tm_mday,
                utc_dt.tm_hour + utc_dt.tm_min/60.0 + utc_dt.tm_sec/3600.0
            )
            
            return jd
        except Exception as e:
            print(f"Julian day calculation error: {e}")
            return swe.julday(birth_date.year, birth_date.month, birth_date.day, 
                            birth_time.hour + birth_time.minute/60.0)
    
    def calculate_planetary_positions(self, julian_day: float) -> Dict[str, Dict]:
        """STEP 2: Calculate planetary longitudes using Swiss Ephemeris"""
        positions = {}
        
        for planet_name, planet_const in PLANET_CONSTANTS.items():
            try:
                if planet_name == 'Ketu':
                    # Ketu is 180° opposite to Rahu
                    rahu_pos = swe.calc_ut(julian_day, swe.MEAN_NODE, swe.FLG_SIDEREAL)[0][0]
                    longitude = (rahu_pos + 180) % 360
                else:
                    # Calculate sidereal position (already adjusted with Lahiri Ayanamsa)
                    result = swe.calc_ut(julian_day, planet_const, swe.FLG_SIDEREAL)
                    longitude = result[0][0]
                
                # STEP 3: Calculate Rasi number (1-12)
                rasi_number = int(longitude / 30) + 1  # 1 = Mesham, ..., 12 = Meenam
                rasi_name = TAMIL_RAASI[rasi_number - 1]
                
                # Calculate Nakshatra
                nakshatra_index = int(longitude * 27 / 360)
                nakshatra = TAMIL_NAKSHATRAS[nakshatra_index % 27]
                
                # Calculate degrees within sign
                degrees_in_sign = longitude % 30
                
                # Calculate Nakshatra pada (quarter)
                nakshatra_position = (longitude * 27 / 360) % 1
                pada = int(nakshatra_position * 4) + 1
                
                positions[planet_name] = {
                    'longitude': round(longitude, 4),
                    'rasi_number': rasi_number,
                    'raasi': rasi_name,
                    'nakshatra': nakshatra,
                    'pada': pada,
                    'degrees_in_sign': round(degrees_in_sign, 2),
                    'tamil_name': TAMIL_PLANETS[planet_name]
                }
                
            except Exception as e:
                print(f"Error calculating {planet_name}: {e}")
                positions[planet_name] = {
                    'longitude': 0,
                    'rasi_number': 1,
                    'raasi': 'மேஷம்',
                    'nakshatra': 'அசுவினி',
                    'pada': 1,
                    'degrees_in_sign': 0,
                    'tamil_name': TAMIL_PLANETS[planet_name]
                }
        
        return positions
    
    def calculate_ascendant_and_houses(self, julian_day: float, latitude: float, longitude: float) -> Tuple[Dict, List]:
        """STEP 2 & 4: Calculate Ascendant (Lagna) and 12 house cusps"""
        try:
            # Calculate houses using Placidus system
            houses_result = swe.houses(julian_day, latitude, longitude, b'P')
            ascendant_longitude = houses_result[1][0]  # Ascendant is the first house cusp
            house_cusps = houses_result[0]  # All 12 house cusps
            
            # Convert to sidereal
            ayanamsa = swe.get_ayanamsa(julian_day)
            sidereal_ascendant = (ascendant_longitude - ayanamsa) % 360
            
            # Calculate Rasi and Nakshatra for Ascendant
            lagna_rasi_number = int(sidereal_ascendant / 30) + 1
            lagna_raasi = TAMIL_RAASI[lagna_rasi_number - 1]
            
            nakshatra_index = int(sidereal_ascendant * 27 / 360)
            lagna_nakshatra = TAMIL_NAKSHATRAS[nakshatra_index % 27]
            
            degrees_in_sign = sidereal_ascendant % 30
            
            ascendant_data = {
                'longitude': round(sidereal_ascendant, 4),
                'rasi_number': lagna_rasi_number,
                'raasi': lagna_raasi,
                'nakshatra': lagna_nakshatra,
                'degrees_in_sign': round(degrees_in_sign, 2)
            }
            
            # Convert all house cusps to sidereal
            sidereal_cusps = []
            for cusp in house_cusps:
                sidereal_cusp = (cusp - ayanamsa) % 360
                sidereal_cusps.append(sidereal_cusp)
            
            return ascendant_data, sidereal_cusps
            
        except Exception as e:
            print(f"Ascendant calculation error: {e}")
            return {
                'longitude': 0,
                'rasi_number': 1,
                'raasi': 'மேஷம்',
                'nakshatra': 'அசுவினி',
                'degrees_in_sign': 0
            }, [0] * 12

    def generate_rasi_chart(self, ascendant_data: Dict, planetary_positions: Dict) -> Dict:
        """STEP 3: Generate Rasi Chart using 30° segments"""
        rasi_chart = {}

        # Initialize all 12 houses
        lagna_rasi_number = ascendant_data['rasi_number']

        for house_num in range(1, 13):
            # Calculate which Rasi this house represents
            # House 1 = Lagna Rasi, House 2 = Next Rasi, etc.
            rasi_number = ((lagna_rasi_number - 1 + house_num - 1) % 12) + 1
            rasi_name = TAMIL_RAASI[rasi_number - 1]

            house_name = f"House_{house_num}"
            rasi_chart[house_name] = {
                'house_number': house_num,
                'rasi_number': rasi_number,
                'rasi_name': rasi_name,
                'planets': []
            }

        # Place planets in houses based on their distance from Lagna
        lagna_longitude = ascendant_data['longitude']

        for planet, data in planetary_positions.items():
            planet_longitude = data['longitude']

            # Calculate house number based on distance from Lagna
            # Each house = 30°, starting from Lagna
            house_offset = int((planet_longitude - lagna_longitude) / 30) % 12
            house_number = house_offset + 1

            house_name = f"House_{house_number}"
            rasi_chart[house_name]['planets'].append(data['tamil_name'])

        return rasi_chart

    def calculate_navamsa_rasi(self, planet_longitude: float) -> Tuple[int, str]:
        """STEP 4: Calculate Navamsa Rasi using precise D9 logic"""
        # Get the Rasi the planet is in
        rasi_number = int(planet_longitude / 30) + 1
        rasi_index = rasi_number - 1

        # Get position within the Rasi (0-30°)
        rasi_pos = planet_longitude % 30

        # Calculate pada (1-9) - each pada = 3°20' = 3.333°
        pada = int(rasi_pos / 3.333) + 1
        if pada > 9:
            pada = 9

        # Navamsa Chart Mapping Rules
        # Aries, Leo, Sagittarius (Fire signs): Forward (1 to 9)
        # Taurus, Virgo, Capricorn (Earth signs): Forward (1 to 9)
        # Gemini, Libra, Aquarius (Air signs): Backward (9 to 1)
        # Cancer, Scorpio, Pisces (Water signs): Backward (9 to 1)

        fire_signs = [1, 5, 9]    # Aries, Leo, Sagittarius
        earth_signs = [2, 6, 10]  # Taurus, Virgo, Capricorn
        air_signs = [3, 7, 11]    # Gemini, Libra, Aquarius
        water_signs = [4, 8, 12]  # Cancer, Scorpio, Pisces

        if rasi_number in fire_signs:
            # Forward direction: pada 1→Aries, pada 2→Taurus, etc.
            navamsa_rasi_number = ((pada - 1) % 12) + 1
        elif rasi_number in earth_signs:
            # Forward direction starting from Capricorn
            navamsa_rasi_number = ((9 + pada - 1) % 12) + 1
        elif rasi_number in air_signs:
            # Forward direction starting from Libra
            navamsa_rasi_number = ((6 + pada - 1) % 12) + 1
        elif rasi_number in water_signs:
            # Forward direction starting from Cancer
            navamsa_rasi_number = ((3 + pada - 1) % 12) + 1
        else:
            # Default fallback
            navamsa_rasi_number = 1

        navamsa_rasi_name = TAMIL_RAASI[navamsa_rasi_number - 1]

        return navamsa_rasi_number, navamsa_rasi_name

    def generate_navamsa_chart(self, ascendant_data: Dict, planetary_positions: Dict) -> Dict:
        """STEP 4: Generate Navamsa Chart using 3°20' divisions"""
        navamsa_chart = {}

        # Calculate Navamsa Lagna
        lagna_longitude = ascendant_data['longitude']
        navamsa_lagna_number, navamsa_lagna_name = self.calculate_navamsa_rasi(lagna_longitude)

        # Initialize all 12 houses for Navamsa
        for house_num in range(1, 13):
            # Calculate which Navamsa Rasi this house represents
            rasi_number = ((navamsa_lagna_number - 1 + house_num - 1) % 12) + 1
            rasi_name = TAMIL_RAASI[rasi_number - 1]

            house_name = f"House_{house_num}"
            navamsa_chart[house_name] = {
                'house_number': house_num,
                'rasi_number': rasi_number,
                'rasi_name': rasi_name,
                'planets': []
            }

        # Place planets in Navamsa houses
        for planet, data in planetary_positions.items():
            planet_longitude = data['longitude']

            # Calculate planet's Navamsa Rasi
            planet_navamsa_number, planet_navamsa_name = self.calculate_navamsa_rasi(planet_longitude)

            # Calculate house number based on distance from Navamsa Lagna
            house_offset = (planet_navamsa_number - navamsa_lagna_number) % 12
            house_number = house_offset + 1

            house_name = f"House_{house_number}"
            navamsa_chart[house_name]['planets'].append(data['tamil_name'])

        return navamsa_chart

    def generate_precise_charts(self, name: str, birth_date: date, birth_time: time,
                              birth_place: str, gender: str = "Male") -> Optional[Dict]:
        """Main function to generate precise Rasi and Navamsa charts"""
        try:
            # STEP 1: Get coordinates and timezone
            latitude, longitude, timezone_str = self.get_coordinates_and_timezone(birth_place)

            # STEP 1: Calculate Julian Day
            julian_day = self.calculate_julian_day(birth_date, birth_time, timezone_str)

            # STEP 2: Calculate planetary positions using Swiss Ephemeris
            planetary_positions = self.calculate_planetary_positions(julian_day)

            # STEP 2 & 4: Calculate Ascendant and house cusps
            ascendant_data, house_cusps = self.calculate_ascendant_and_houses(
                julian_day, latitude, longitude
            )

            # STEP 3: Generate Rasi Chart
            rasi_chart = self.generate_rasi_chart(ascendant_data, planetary_positions)

            # STEP 4: Generate Navamsa Chart
            navamsa_chart = self.generate_navamsa_chart(ascendant_data, planetary_positions)

            # Get Moon's position for main details
            moon_data = planetary_positions.get('Moon', {})

            # Create comprehensive chart data
            chart_data = {
                'personal_details': {
                    'name': name,
                    'gender': gender,
                    'birth_date': birth_date.strftime('%Y-%m-%d'),
                    'birth_time': birth_time.strftime('%H:%M'),
                    'birth_place': birth_place,
                    'latitude': round(latitude, 6),
                    'longitude': round(longitude, 6),
                    'timezone': timezone_str,
                    'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                'astrological_details': {
                    'moon_raasi': moon_data.get('raasi', 'தெரியவில்லை'),
                    'moon_nakshatra': moon_data.get('nakshatra', 'தெரியவில்லை'),
                    'moon_pada': moon_data.get('pada', 1),
                    'lagna_raasi': ascendant_data['raasi'],
                    'lagna_nakshatra': ascendant_data['nakshatra'],
                    'lagna_degrees': ascendant_data['degrees_in_sign']
                },
                'planetary_positions': planetary_positions,
                'ascendant': ascendant_data,
                'house_cusps': house_cusps,
                'rasi_chart': rasi_chart,
                'navamsa_chart': navamsa_chart,
                'calculation_method': 'Swiss Ephemeris with Lahiri Ayanamsa - Precise Logic'
            }

            return chart_data

        except Exception as e:
            print(f"Chart generation error: {e}")
            return None

# Test function
if __name__ == "__main__":
    try:
        generator = PreciseChartGenerator()

        # Test with sample data
        test_date = date(1990, 8, 15)
        test_time = time(10, 30)
        test_charts = generator.generate_precise_charts(
            "துல்லிய சோதனை", test_date, test_time, "சென்னை", "Male"
        )

        if test_charts:
            print("✅ Precise charts generated successfully!")
            print(f"Lagna Raasi: {test_charts['astrological_details']['lagna_raasi']}")
            print(f"Moon Raasi: {test_charts['astrological_details']['moon_raasi']}")
            print(f"Rasi Chart Houses: {len(test_charts['rasi_chart'])}")
            print(f"Navamsa Chart Houses: {len(test_charts['navamsa_chart'])}")
        else:
            print("❌ Failed to generate precise charts")

    except Exception as e:
        print(f"❌ Test error: {e}")
        print("Note: Ensure pyswisseph, geopy, and timezonefinder are installed")
