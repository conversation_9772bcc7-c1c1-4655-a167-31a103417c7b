#!/usr/bin/env python3
"""
Tamil Calendar Integration System
Implements Tamil calendar, Panchangam, Tithi, Nakshatra, Yoga, Karana calculations
Based on tamil-calendar and panchangam library specifications
"""
import swisseph as swe
from datetime import datetime, date, time
from typing import Dict, List, Tuple, Optional
import math

class TamilCalendarIntegration:
    """
    Tamil Calendar and Panchangam calculator
    Implements features from tamil-calendar and panchangam libraries
    """
    
    def __init__(self):
        # Set Lahiri <PERSON>a for Tamil Nadu
        swe.set_sid_mode(swe.SIDM_LAHIRI)
        
        # Tamil month names (Solar calendar - சூரிய மாதங்கள்)
        self.tamil_solar_months = [
            'சித்திரை',    # April-May (<PERSON>sha)
            'வைகாசி',     # May-June (Vrishabha)
            'ஆனி',        # June-July (Mithuna)
            'ஆடி',        # July-August (Kataka)
            'ஆவணி',       # August-September (Simha)
            'புரட்டாசி',   # September-October (Kanya)
            'ஐப்பசி',     # October-November (Tula)
            'கார்த்திகை',  # November-December (<PERSON>ris<PERSON><PERSON>)
            'மார்கழி',     # December-January (<PERSON><PERSON><PERSON>)
            'தை',         # January-February (<PERSON><PERSON>)
            'மாசி',        # February-March (<PERSON><PERSON><PERSON>)
            'பங்குனி'      # March-April (<PERSON><PERSON>)
        ]
        
        # Tamil weekday names
        self.tamil_weekdays = [
            'ஞாயிறு',     # Sunday
            'திங்கள்',    # Monday
            'செவ்வாய்',   # Tuesday
            'புதன்',      # Wednesday
            'வியாழன்',    # Thursday
            'வெள்ளி',     # Friday
            'சனி'         # Saturday
        ]
        
        # Tamil Nakshatra names (27 stars)
        self.tamil_nakshatras = [
            'அசுவினி', 'பரணி', 'கார்த்திகை', 'ரோகிணி', 'மிருகசீர்ஷம்', 'திருவாதிரை',
            'புனர்பூசம்', 'பூசம்', 'ஆயில்யம்', 'மகம்', 'பூரம்', 'உத்திரம்',
            'ஹஸ்தம்', 'சித்திரை', 'சுவாதி', 'விசாகம்', 'அனுஷம்', 'கேட்டை',
            'மூலம்', 'பூராடம்', 'உத்திராடம்', 'திருவோணம்', 'அவிட்டம்', 'சதயம்',
            'பூரட்டாதி', 'உத்திரட்டாதி', 'ரேவதி'
        ]
        
        # Tamil Yoga names (27 yogas)
        self.tamil_yogas = [
            'விஷ்கம்பம்', 'பிரீதி', 'ஆயுஷ்மான்', 'சௌபாக்யம்', 'சோபனம்', 'அதிகண்டம்',
            'சுகர்மா', 'த்ருதி', 'சூலம்', 'கண்டம்', 'வ்ருத்தி', 'த்ருவம்',
            'வ்யாகதம்', 'ஹர்ஷணம்', 'வஜ்ரம்', 'சித்தி', 'வ்யதீபாதம்', 'வரீயான்',
            'பரிகம்', 'சிவம்', 'சித்தம்', 'சாத்யம்', 'சுபம்', 'சுக்லம்',
            'ப்ரம்மம்', 'மாஹேந்திரம்', 'வைத்ருதி'
        ]
        
        # Tamil Karana names (11 karanas)
        self.tamil_karanas = [
            'பவ', 'பாலவ', 'கௌலவ', 'தைதுல', 'கர', 'வணிஜ', 'விஷ்டி',
            'சகுனி', 'சதுஷ்பாத', 'நாக', 'கிம்ஸ்துக்ன'
        ]
        
        # Tamil Tithi names (30 tithis)
        self.tamil_tithis = [
            # Shukla Paksha (வளர்பிறை)
            'அமாவாசை', 'பிரதமை', 'துவிதீயை', 'திருதீயை', 'சதுர்த்தி', 'பஞ்சமி',
            'ஷஷ்டி', 'சப்தமி', 'அஷ்டமி', 'நவமி', 'தசமி', 'ஏகாதசி',
            'துவாதசி', 'திரயோதசி', 'சதுர்தசி', 'பௌர்ணமி',
            # Krishna Paksha (தேய்பிறை)
            'பிரதமை', 'துவிதீயை', 'திருதீயை', 'சதுர்த்தி', 'பஞ்சமி', 'ஷஷ்டி',
            'சப்தமி', 'அஷ்டமி', 'நவமி', 'தசமி', 'ஏகாதசி', 'துவாதசி',
            'திரயோதசி', 'சதுர்தசி'
        ]
        
        # Major Tamil festivals and their dates
        self.tamil_festivals = {
            'தமிழ் புத்தாண்டு': (1, 1),      # Chithirai 1
            'ஆகாச கங்கை': (1, 3),           # Chithirai 3
            'சித்திரா பௌர்ணமி': (1, 15),     # Chithirai Pournami
            'வைகாசி விசாகம்': (2, 15),       # Vaikasi Visakam
            'ஆனி திருமஞ்சனம்': (3, 10),      # Aani Thirumanjanam
            'ஆடி அமாவாசை': (4, 1),          # Aadi Amavasai
            'ஆடி பெருக்கு': (4, 18),         # Aadi Perukku
            'ஆவணி அவிட்டம்': (5, 15),        # Avani Avittam
            'விநாயக சதுர்த்தி': (5, 4),      # Vinayaka Chaturthi
            'நவராத்திரி': (6, 1),           # Navarathri
            'விஜயதசமி': (6, 10),           # Vijayadashami
            'தீபாவளி': (7, 1),              # Deepavali
            'கார்த்திகை தீபம்': (8, 15),     # Karthikai Deepam
            'மார்கழி திருவாதிரை': (9, 15),   # Margazhi Thiruvathirai
            'தை அமாவாசை': (10, 1),          # Thai Amavasai
            'தை பூசம்': (10, 8),            # Thai Pusam
            'மாசி மகம்': (11, 10),           # Masi Magam
            'மஹா சிவராத்திரி': (11, 14),     # Maha Shivarathri
            'பங்குனி உத்திரம்': (12, 12)     # Panguni Uthiram
        }
    
    def calculate_julian_day_number(self, date_obj: date, time_obj: time = None) -> float:
        """Calculate Julian Day Number for given date and time"""
        if time_obj is None:
            time_obj = time(6, 0)  # Default sunrise time
        
        return swe.julday(
            date_obj.year, date_obj.month, date_obj.day,
            time_obj.hour + time_obj.minute/60.0 + time_obj.second/3600.0
        )
    
    def get_tamil_solar_month(self, date_obj: date) -> Tuple[str, int]:
        """
        Get Tamil solar month and date
        Based on Sun's position in zodiac signs
        """
        try:
            jd = self.calculate_julian_day_number(date_obj)
            
            # Calculate Sun's position
            sun_pos = swe.calc_ut(jd, swe.SUN, swe.FLG_SIDEREAL)[0][0]
            
            # Determine Tamil month based on Sun's zodiac position
            month_index = int(sun_pos / 30)  # Each sign is 30 degrees
            tamil_month = self.tamil_solar_months[month_index]
            
            # Calculate Tamil date (approximate)
            days_in_sign = (sun_pos % 30) / 30 * 30  # Approximate days
            tamil_date = int(days_in_sign) + 1
            
            return tamil_month, tamil_date
            
        except Exception as e:
            print(f"Tamil solar month calculation error: {e}")
            return 'சித்திரை', 1
    
    def calculate_tithi(self, jd: float) -> Tuple[str, float]:
        """
        Calculate Tithi (lunar day)
        Based on Moon-Sun longitude difference
        """
        try:
            # Get Sun and Moon positions
            sun_pos = swe.calc_ut(jd, swe.SUN, swe.FLG_SIDEREAL)[0][0]
            moon_pos = swe.calc_ut(jd, swe.MOON, swe.FLG_SIDEREAL)[0][0]
            
            # Calculate longitude difference
            diff = (moon_pos - sun_pos) % 360
            
            # Each tithi is 12 degrees
            tithi_number = int(diff / 12)
            tithi_progress = (diff % 12) / 12 * 100  # Percentage completed
            
            # Get tithi name
            if tithi_number < len(self.tamil_tithis):
                tithi_name = self.tamil_tithis[tithi_number]
            else:
                tithi_name = 'அமாவாசை'
            
            return tithi_name, tithi_progress
            
        except Exception as e:
            print(f"Tithi calculation error: {e}")
            return 'பிரதமை', 0.0
    
    def calculate_nakshatra(self, jd: float) -> Tuple[str, int, float]:
        """
        Calculate Nakshatra (star) based on Moon's position
        """
        try:
            # Get Moon's position
            moon_pos = swe.calc_ut(jd, swe.MOON, swe.FLG_SIDEREAL)[0][0]
            
            # Each nakshatra is 13°20' (360°/27)
            nakshatra_span = 360.0 / 27
            nakshatra_number = int(moon_pos / nakshatra_span)
            
            # Calculate pada (quarter within nakshatra)
            nakshatra_progress = (moon_pos % nakshatra_span)
            pada = int(nakshatra_progress / (nakshatra_span / 4)) + 1
            
            # Calculate percentage completed in current nakshatra
            nakshatra_percent = (nakshatra_progress / nakshatra_span) * 100
            
            # Get nakshatra name
            if nakshatra_number < len(self.tamil_nakshatras):
                nakshatra_name = self.tamil_nakshatras[nakshatra_number]
            else:
                nakshatra_name = 'அசுவினி'
            
            return nakshatra_name, pada, nakshatra_percent
            
        except Exception as e:
            print(f"Nakshatra calculation error: {e}")
            return 'அசுவினி', 1, 0.0
    
    def calculate_yoga(self, jd: float) -> Tuple[str, float]:
        """
        Calculate Yoga based on Sun and Moon positions
        """
        try:
            # Get Sun and Moon positions
            sun_pos = swe.calc_ut(jd, swe.SUN, swe.FLG_SIDEREAL)[0][0]
            moon_pos = swe.calc_ut(jd, swe.MOON, swe.FLG_SIDEREAL)[0][0]
            
            # Yoga = (Sun longitude + Moon longitude) mod 360
            yoga_longitude = (sun_pos + moon_pos) % 360
            
            # Each yoga is 13°20' (360°/27)
            yoga_span = 360.0 / 27
            yoga_number = int(yoga_longitude / yoga_span)
            yoga_progress = (yoga_longitude % yoga_span) / yoga_span * 100
            
            # Get yoga name
            if yoga_number < len(self.tamil_yogas):
                yoga_name = self.tamil_yogas[yoga_number]
            else:
                yoga_name = 'விஷ்கம்பம்'
            
            return yoga_name, yoga_progress
            
        except Exception as e:
            print(f"Yoga calculation error: {e}")
            return 'விஷ்கம்பம்', 0.0
    
    def calculate_karana(self, jd: float) -> Tuple[str, float]:
        """
        Calculate Karana (half tithi)
        """
        try:
            # Get tithi first
            tithi_name, tithi_progress = self.calculate_tithi(jd)
            
            # Each tithi has 2 karanas
            # Get Sun and Moon positions for precise calculation
            sun_pos = swe.calc_ut(jd, swe.SUN, swe.FLG_SIDEREAL)[0][0]
            moon_pos = swe.calc_ut(jd, swe.MOON, swe.FLG_SIDEREAL)[0][0]
            
            diff = (moon_pos - sun_pos) % 360
            
            # Each karana is 6 degrees
            karana_number = int(diff / 6) % 11
            karana_progress = (diff % 6) / 6 * 100
            
            # Get karana name
            if karana_number < len(self.tamil_karanas):
                karana_name = self.tamil_karanas[karana_number]
            else:
                karana_name = 'பவ'
            
            return karana_name, karana_progress
            
        except Exception as e:
            print(f"Karana calculation error: {e}")
            return 'பவ', 0.0
    
    def calculate_sunrise_sunset(self, date_obj: date, latitude: float, longitude: float) -> Tuple[time, time]:
        """
        Calculate sunrise and sunset times for given location
        """
        try:
            jd = self.calculate_julian_day_number(date_obj)
            
            # Calculate sunrise
            sunrise_jd = swe.rise_trans(
                jd, swe.SUN, longitude, latitude, 
                rsmi=swe.CALC_RISE | swe.BIT_DISC_CENTER
            )[1][0]
            
            # Calculate sunset
            sunset_jd = swe.rise_trans(
                jd, swe.SUN, longitude, latitude,
                rsmi=swe.CALC_SET | swe.BIT_DISC_CENTER
            )[1][0]
            
            # Convert to time objects
            sunrise_decimal = (sunrise_jd % 1) * 24
            sunset_decimal = (sunset_jd % 1) * 24
            
            sunrise_time = time(
                int(sunrise_decimal),
                int((sunrise_decimal % 1) * 60)
            )
            
            sunset_time = time(
                int(sunset_decimal),
                int((sunset_decimal % 1) * 60)
            )
            
            return sunrise_time, sunset_time
            
        except Exception as e:
            print(f"Sunrise/sunset calculation error: {e}")
            return time(6, 0), time(18, 0)  # Default times
    
    def get_tamil_weekday(self, date_obj: date) -> str:
        """Get Tamil weekday name"""
        weekday_index = date_obj.weekday()
        # Convert Monday=0 to Sunday=0 format
        tamil_weekday_index = (weekday_index + 1) % 7
        return self.tamil_weekdays[tamil_weekday_index]
    
    def check_festival(self, date_obj: date) -> List[str]:
        """
        Check if the given date has any Tamil festivals
        """
        festivals_today = []
        
        try:
            tamil_month, tamil_date = self.get_tamil_solar_month(date_obj)
            month_index = self.tamil_solar_months.index(tamil_month) + 1
            
            # Check for festivals
            for festival_name, (fest_month, fest_date) in self.tamil_festivals.items():
                if fest_month == month_index and fest_date == tamil_date:
                    festivals_today.append(festival_name)
            
            # Check for special lunar festivals
            jd = self.calculate_julian_day_number(date_obj)
            tithi_name, _ = self.calculate_tithi(jd)
            
            if tithi_name == 'அமாவாசை':
                festivals_today.append('அமாவாசை')
            elif tithi_name == 'பௌர்ணமி':
                festivals_today.append('பௌர்ணமி')
            
        except Exception as e:
            print(f"Festival check error: {e}")
        
        return festivals_today

    def generate_complete_panchangam(self, date_obj: date, time_obj: time,
                                   latitude: float, longitude: float, place_name: str) -> Dict:
        """
        Generate complete Tamil Panchangam for given date, time and location
        Following traditional Tamil Nadu Panchangam format
        """
        try:
            jd = self.calculate_julian_day_number(date_obj, time_obj)

            # Calculate all Panchangam elements
            tamil_month, tamil_date = self.get_tamil_solar_month(date_obj)
            tamil_weekday = self.get_tamil_weekday(date_obj)

            tithi_name, tithi_progress = self.calculate_tithi(jd)
            nakshatra_name, pada, nakshatra_progress = self.calculate_nakshatra(jd)
            yoga_name, yoga_progress = self.calculate_yoga(jd)
            karana_name, karana_progress = self.calculate_karana(jd)

            sunrise_time, sunset_time = self.calculate_sunrise_sunset(date_obj, latitude, longitude)
            festivals = self.check_festival(date_obj)

            # Calculate day length
            sunrise_minutes = sunrise_time.hour * 60 + sunrise_time.minute
            sunset_minutes = sunset_time.hour * 60 + sunset_time.minute
            day_length_minutes = sunset_minutes - sunrise_minutes
            day_length_hours = day_length_minutes // 60
            day_length_mins = day_length_minutes % 60

            panchangam_data = {
                'date_info': {
                    'gregorian_date': date_obj.strftime('%Y-%m-%d'),
                    'tamil_date': f"{tamil_month} {tamil_date}",
                    'tamil_month': tamil_month,
                    'tamil_day': tamil_date,
                    'weekday': tamil_weekday,
                    'place': place_name
                },
                'panchangam_elements': {
                    'tithi': {
                        'name': tithi_name,
                        'progress': round(tithi_progress, 1),
                        'description': f"{tithi_name} ({tithi_progress:.1f}% முடிந்தது)"
                    },
                    'nakshatra': {
                        'name': nakshatra_name,
                        'pada': pada,
                        'progress': round(nakshatra_progress, 1),
                        'description': f"{nakshatra_name} {pada}ம் பாதம் ({nakshatra_progress:.1f}% முடிந்தது)"
                    },
                    'yoga': {
                        'name': yoga_name,
                        'progress': round(yoga_progress, 1),
                        'description': f"{yoga_name} ({yoga_progress:.1f}% முடிந்தது)"
                    },
                    'karana': {
                        'name': karana_name,
                        'progress': round(karana_progress, 1),
                        'description': f"{karana_name} ({karana_progress:.1f}% முடிந்தது)"
                    }
                },
                'astronomical_info': {
                    'sunrise': sunrise_time.strftime('%H:%M'),
                    'sunset': sunset_time.strftime('%H:%M'),
                    'day_length': f"{day_length_hours}:{day_length_mins:02d}",
                    'julian_day': round(jd, 6)
                },
                'festivals': festivals,
                'calculation_method': 'Swiss Ephemeris with Lahiri Ayanamsa (Tamil Nadu Traditional)',
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return panchangam_data

        except Exception as e:
            print(f"Panchangam generation error: {e}")
            return self._get_default_panchangam(date_obj, place_name)

    def _get_default_panchangam(self, date_obj: date, place_name: str) -> Dict:
        """Generate default Panchangam data in case of errors"""
        return {
            'date_info': {
                'gregorian_date': date_obj.strftime('%Y-%m-%d'),
                'tamil_date': 'சித்திரை 1',
                'tamil_month': 'சித்திரை',
                'tamil_day': 1,
                'weekday': self.get_tamil_weekday(date_obj),
                'place': place_name
            },
            'panchangam_elements': {
                'tithi': {'name': 'பிரதமை', 'progress': 0, 'description': 'பிரதமை'},
                'nakshatra': {'name': 'அசுவினி', 'pada': 1, 'progress': 0, 'description': 'அசுவினி 1ம் பாதம்'},
                'yoga': {'name': 'விஷ்கம்பம்', 'progress': 0, 'description': 'விஷ்கம்பம்'},
                'karana': {'name': 'பவ', 'progress': 0, 'description': 'பவ'}
            },
            'astronomical_info': {
                'sunrise': '06:00',
                'sunset': '18:00',
                'day_length': '12:00',
                'julian_day': 0
            },
            'festivals': [],
            'calculation_method': 'Default values (calculation error)',
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def format_panchangam_for_display(self, panchangam_data: Dict) -> str:
        """
        Format Panchangam data for display in traditional Tamil style
        """
        if not panchangam_data:
            return "பஞ்சாங்க தகவல் கிடைக்கவில்லை"

        date_info = panchangam_data.get('date_info', {})
        elements = panchangam_data.get('panchangam_elements', {})
        astro_info = panchangam_data.get('astronomical_info', {})
        festivals = panchangam_data.get('festivals', [])

        formatted_text = f"""
📅 தமிழ் பஞ்சாங்கம் 📅

📍 இடம்: {date_info.get('place', 'தெரியவில்லை')}
📆 தேதி: {date_info.get('gregorian_date', 'தெரியவில்லை')}
🗓️ தமிழ் தேதி: {date_info.get('tamil_date', 'தெரியவில்லை')}
📅 வாரம்: {date_info.get('weekday', 'தெரியவில்லை')}

🌙 பஞ்சாங்க அம்சங்கள்:
• திதி: {elements.get('tithi', {}).get('description', 'தெரியவில்லை')}
• நட்சத்திரம்: {elements.get('nakshatra', {}).get('description', 'தெரியவில்லை')}
• யோகம்: {elements.get('yoga', {}).get('description', 'தெரியவில்லை')}
• கரணம்: {elements.get('karana', {}).get('description', 'தெரியவில்லை')}

🌅 வானியல் தகவல்கள்:
• சூரிய உதயம்: {astro_info.get('sunrise', 'தெரியவில்லை')}
• சூரிய அஸ்தமனம்: {astro_info.get('sunset', 'தெரியவில்லை')}
• பகல் நேரம்: {astro_info.get('day_length', 'தெரியவில்லை')}

🎉 பண்டிகைகள்:
{self._format_festivals(festivals)}

🔬 கணக்கீட்டு முறை: {panchangam_data.get('calculation_method', 'Swiss Ephemeris')}
⏰ உருவாக்கப்பட்ட நேரம்: {panchangam_data.get('generated_at', 'தெரியவில்லை')}
"""

        return formatted_text

    def _format_festivals(self, festivals: List[str]) -> str:
        """Format festival list for display"""
        if not festivals:
            return "• இன்று சிறப்பு பண்டிகைகள் இல்லை"

        festival_text = ""
        for festival in festivals:
            festival_text += f"• {festival}\n"

        return festival_text.strip()

    def get_muhurtham_times(self, date_obj: date, latitude: float, longitude: float) -> Dict:
        """
        Calculate auspicious times (Muhurtham) for the given date
        """
        try:
            sunrise_time, sunset_time = self.calculate_sunrise_sunset(date_obj, latitude, longitude)

            # Calculate day duration in minutes
            sunrise_minutes = sunrise_time.hour * 60 + sunrise_time.minute
            sunset_minutes = sunset_time.hour * 60 + sunset_time.minute
            day_duration = sunset_minutes - sunrise_minutes

            # Divide day into 8 parts for muhurtham calculation
            muhurtham_duration = day_duration // 8

            muhurtham_times = {}
            muhurtham_names = [
                'ருத்ர காலம்', 'காலராத்திரி', 'குலிக காலம்', 'ராகு காலம்',
                'யமகண்டம்', 'அபிஜித் முகூர்த்தம்', 'சுப முகூர்த்தம்', 'அமிர்த காலம்'
            ]

            for i, name in enumerate(muhurtham_names):
                start_minutes = sunrise_minutes + (i * muhurtham_duration)
                end_minutes = start_minutes + muhurtham_duration

                start_time = time(start_minutes // 60, start_minutes % 60)
                end_time = time(end_minutes // 60, end_minutes % 60)

                muhurtham_times[name] = {
                    'start': start_time.strftime('%H:%M'),
                    'end': end_time.strftime('%H:%M'),
                    'duration': f"{muhurtham_duration} நிமிடங்கள்"
                }

            return muhurtham_times

        except Exception as e:
            print(f"Muhurtham calculation error: {e}")
            return {}

# Test function
if __name__ == "__main__":
    print("📅 Testing Tamil Calendar Integration...")

    # Test data
    test_date = date(2024, 1, 15)
    test_time = time(10, 30)
    test_latitude = 13.0827  # Chennai
    test_longitude = 80.2707
    test_place = "சென்னை"

    # Create Tamil calendar instance
    tamil_calendar = TamilCalendarIntegration()

    # Test Panchangam generation
    panchangam_data = tamil_calendar.generate_complete_panchangam(
        test_date, test_time, test_latitude, test_longitude, test_place
    )

    if panchangam_data:
        print("✅ Tamil Panchangam generation successful!")

        # Display key information
        date_info = panchangam_data.get('date_info', {})
        elements = panchangam_data.get('panchangam_elements', {})

        print(f"   Tamil Date: {date_info.get('tamil_date')}")
        print(f"   Weekday: {date_info.get('weekday')}")
        print(f"   Tithi: {elements.get('tithi', {}).get('name')}")
        print(f"   Nakshatra: {elements.get('nakshatra', {}).get('name')}")
        print(f"   Yoga: {elements.get('yoga', {}).get('name')}")
        print(f"   Karana: {elements.get('karana', {}).get('name')}")

        # Test formatted display
        formatted_panchangam = tamil_calendar.format_panchangam_for_display(panchangam_data)
        print("\n" + "="*50)
        print(formatted_panchangam)

    else:
        print("❌ Tamil Panchangam generation failed")

    print("\n🎉 Tamil Calendar Integration test completed!")
