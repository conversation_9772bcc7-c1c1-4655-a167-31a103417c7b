#!/usr/bin/env python3
"""
Add astrological fields to Document table
"""
import os
import sys

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import db

def migrate_database():
    """Add new columns to Document table"""
    with app.app_context():
        print("Adding astrological fields to Document table...")
        
        try:
            # Check if columns already exist
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('document')]
            
            if 'natchathiram' not in columns:
                print("Adding natchathiram column...")
                db.engine.execute('ALTER TABLE document ADD COLUMN natchathiram VARCHAR(50)')
            else:
                print("natchathiram column already exists")
            
            if 'raasi' not in columns:
                print("Adding raasi column...")
                db.engine.execute('ALTER TABLE document ADD COLUMN raasi VARCHAR(50)')
            else:
                print("raasi column already exists")
            
            if 'vayathu' not in columns:
                print("Adding vayathu column...")
                db.engine.execute('ALTER TABLE document ADD COLUMN vayathu INTEGER')
            else:
                print("vayathu column already exists")
            
            print("✅ Database migration completed successfully!")
            
        except Exception as e:
            print(f"❌ Migration error: {e}")
            print("Creating new tables instead...")
            # If migration fails, create all tables
            db.create_all()
            print("✅ Tables created successfully!")

if __name__ == "__main__":
    migrate_database()
