<!DOCTYPE html>
<html lang="ta">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ t.user }} {{ t.dashboard }} - ஆவண மேலாண்மை அமைப்பு</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .welcome-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .documents-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .document-card {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 1.5rem;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .document-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .document-icon {
            font-size: 2rem;
        }

        .document-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }

        .document-info {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .document-actions {
            margin-top: 1rem;
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background-color: #5a6fd8;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                flex-direction: column;
                gap: 1rem;
            }

            .documents-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                📄 ஆவண மேலாண்மை
            </div>
            <div class="nav-menu">
                <div class="user-info">
                    <span>{{ t.welcome }}, {{ current_user.full_name }}!</span>
                    <a href="{{ url_for('logout') }}" class="nav-link">{{ t.logout }}</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1 class="welcome-title">{{ t.user }} {{ t.dashboard }}</h1>
            <p class="welcome-subtitle">ஸ்கேன் செய்யப்பட்ட ஆவணங்களைப் பார்க்கவும்</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ documents|length }}</div>
                <div class="stat-label">மொத்த ஆவணங்கள்</div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="documents-section" style="margin-bottom: 2rem;">
            <h2 class="section-title">🔍 ஆவண தேடல்</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                <input type="text" id="search-name" placeholder="பெயர்" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                <input type="text" id="search-city" placeholder="நகரம்" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                <select id="search-natchathiram" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                    <option value="">நட்சத்திரம்</option>
                    <option value="அசுவினி">அசுவினி</option>
                    <option value="பரணி">பரணி</option>
                    <option value="கார்த்திகை">கார்த்திகை</option>
                    <option value="ரோகிணி">ரோகிணி</option>
                    <option value="மிருகசீரிடம்">மிருகசீரிடம்</option>
                    <option value="திருவாதிரை">திருவாதிரை</option>
                    <option value="புனர்பூசம்">புனர்பூசம்</option>
                    <option value="பூசம்">பூசம்</option>
                    <option value="ஆயில்யம்">ஆயில்யம்</option>
                    <option value="மகம்">மகம்</option>
                    <option value="பூரம்">பூரம்</option>
                    <option value="உத்திரம்">உத்திரம்</option>
                    <option value="அஸ்தம்">அஸ்தம்</option>
                    <option value="சித்திரை">சித்திரை</option>
                    <option value="சுவாதி">சுவாதி</option>
                    <option value="விசாகம்">விசாகம்</option>
                    <option value="அனுஷம்">அனுஷம்</option>
                    <option value="கேட்டை">கேட்டை</option>
                    <option value="மூலம்">மூலம்</option>
                    <option value="பூராடம்">பூராடம்</option>
                    <option value="உத்திராடம்">உத்திராடம்</option>
                    <option value="திருவோணம்">திருவோணம்</option>
                    <option value="அவிட்டம்">அவிட்டம்</option>
                    <option value="சதயம்">சதயம்</option>
                    <option value="பூரட்டாதி">பூரட்டாதி</option>
                    <option value="உத்திரட்டாதி">உத்திரட்டாதி</option>
                    <option value="ரேவதி">ரேவதி</option>
                </select>
                <select id="search-raasi" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                    <option value="">ராசி</option>
                    <option value="மேஷம்">மேஷம்</option>
                    <option value="ரிஷபம்">ரிஷபம்</option>
                    <option value="மிதுனம்">மிதுனம்</option>
                    <option value="கடகம்">கடகம்</option>
                    <option value="சிம்மம்">சிம்மம்</option>
                    <option value="கன்னி">கன்னி</option>
                    <option value="துலாம்">துலாம்</option>
                    <option value="விருச்சிகம்">விருச்சிகம்</option>
                    <option value="தனுசு">தனுசு</option>
                    <option value="மகரம்">மகரம்</option>
                    <option value="கும்பம்">கும்பம்</option>
                    <option value="மீனம்">மீனம்</option>
                </select>
                <input type="number" id="search-vayathu-min" placeholder="குறைந்த வயது" min="1" max="120" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                <input type="number" id="search-vayathu-max" placeholder="அதிக வயது" min="1" max="120" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
            </div>
            <div style="text-align: center; margin-bottom: 1rem;">
                <button onclick="performSearch()" style="background: #667eea; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">🔍 தேடு</button>
                <button onclick="clearSearch()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">🗑️ அழி</button>
            </div>
        </div>

        <!-- Documents Section -->
        <div class="documents-section">
            <h2 class="section-title">
                📄 ஸ்கேன் செய்யப்பட்ட ஆவணங்கள்
            </h2>

            {% if documents %}
                <div class="documents-grid" id="documents-grid">
                    {% for doc in documents %}
                        <div class="document-card">
                            <div class="document-header">
                                <div class="document-icon">📄</div>
                                <div class="document-title">{{ doc.filename }}</div>
                            </div>

                            <div class="document-info">
                                <strong>{{ t.date }}:</strong> {{ doc.created_at }}
                            </div>

                            {% if doc.user %}
                                <div class="document-info">
                                    <strong>{{ t.user }}:</strong> {{ doc.user.name }}
                                </div>
                                <div class="document-info">
                                    <strong>{{ t.city }}:</strong> {{ doc.user.city }}
                                </div>
                            {% endif %}

                            {% if doc.natchathiram %}
                                <div class="document-info">
                                    <strong>நட்சத்திரம்:</strong> {{ doc.natchathiram }}
                                </div>
                            {% endif %}

                            {% if doc.raasi %}
                                <div class="document-info">
                                    <strong>ராசி:</strong> {{ doc.raasi }}
                                </div>
                            {% endif %}

                            {% if doc.vayathu %}
                                <div class="document-info">
                                    <strong>வயது:</strong> {{ doc.vayathu }}
                                </div>
                            {% endif %}

                            <div class="document-info">
                                <strong>{{ t.size }}:</strong>
                                {% if doc.file_size %}
                                    {{ "%.1f"|format(doc.file_size / 1024) }} KB
                                {% else %}
                                    தெரியவில்லை
                                {% endif %}
                            </div>

                            <div class="document-actions">
                                {% if doc.document_type == 'scanned' %}
                                    <a href="{{ url_for('serve_scanned_file', filename=doc.filename) }}"
                                       class="btn btn-primary" target="_blank">{{ t.view }}</a>
                                {% else %}
                                    <a href="{{ url_for('serve_uploaded_file', filename=doc.filename) }}"
                                       class="btn btn-primary" target="_blank">{{ t.view }}</a>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <h3>ஆவணங்கள் இல்லை</h3>
                    <p>இன்னும் ஸ்கேன் செய்யப்பட்ட ஆவணங்கள் எதுவும் இல்லை.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        function performSearch() {
            const filters = {
                name: document.getElementById('search-name').value,
                city: document.getElementById('search-city').value,
                natchathiram: document.getElementById('search-natchathiram').value,
                raasi: document.getElementById('search-raasi').value,
                vayathu_min: document.getElementById('search-vayathu-min').value,
                vayathu_max: document.getElementById('search-vayathu-max').value,
                document_type: 'scanned' // Users only see scanned documents
            };

            const queryParams = new URLSearchParams();
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    queryParams.append(key, filters[key]);
                }
            });

            fetch(`/advanced_search?${queryParams.toString()}`)
                .then(response => response.json())
                .then(data => {
                    updateDocumentsGrid(data.documents);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    alert('❌ தேடல் பிழை');
                });
        }

        function clearSearch() {
            document.getElementById('search-name').value = '';
            document.getElementById('search-city').value = '';
            document.getElementById('search-natchathiram').value = '';
            document.getElementById('search-raasi').value = '';
            document.getElementById('search-vayathu-min').value = '';
            document.getElementById('search-vayathu-max').value = '';
            location.reload(); // Reload to show all documents
        }

        function updateDocumentsGrid(documents) {
            const grid = document.getElementById('documents-grid');
            grid.innerHTML = '';

            if (documents.length === 0) {
                grid.innerHTML = '<div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: #666;"><div style="font-size: 4rem; margin-bottom: 1rem;">📄</div><h3>தேடல் முடிவுகள் இல்லை</h3><p>வேறு தேடல் வார்த்தைகளை முயற்சிக்கவும்.</p></div>';
                return;
            }

            documents.forEach(doc => {
                const card = document.createElement('div');
                card.className = 'document-card';
                card.innerHTML = `
                    <div class="document-header">
                        <div class="document-icon">📄</div>
                        <div class="document-title">${doc.filename}</div>
                    </div>

                    <div class="document-info">
                        <strong>தேதி:</strong> ${doc.created_at}
                    </div>

                    ${doc.user_name ? `
                        <div class="document-info">
                            <strong>பயனர்:</strong> ${doc.user_name}
                        </div>
                        <div class="document-info">
                            <strong>நகரம்:</strong> ${doc.user_city}
                        </div>
                    ` : ''}

                    ${doc.natchathiram ? `
                        <div class="document-info">
                            <strong>நட்சத்திரம்:</strong> ${doc.natchathiram}
                        </div>
                    ` : ''}

                    ${doc.raasi ? `
                        <div class="document-info">
                            <strong>ராசி:</strong> ${doc.raasi}
                        </div>
                    ` : ''}

                    ${doc.vayathu ? `
                        <div class="document-info">
                            <strong>வயது:</strong> ${doc.vayathu}
                        </div>
                    ` : ''}

                    <div class="document-info">
                        <strong>அளவு:</strong> ${doc.file_size ? (doc.file_size / 1024).toFixed(1) + ' KB' : 'தெரியவில்லை'}
                    </div>

                    <div class="document-actions">
                        <a href="/static/scanned/${doc.filename}" class="btn btn-primary" target="_blank">பார்</a>
                    </div>
                `;
                grid.appendChild(card);
            });
        }
    </script>
</body>
</html>
