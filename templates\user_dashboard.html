<!DOCTYPE html>
<html lang="ta">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ t.user }} {{ t.dashboard }} - ஆவண மேலாண்மை அமைப்பு</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .welcome-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .documents-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .document-card {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 1.5rem;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .document-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .document-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .document-icon {
            font-size: 2rem;
        }

        .document-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }

        .document-info {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .document-actions {
            margin-top: 1rem;
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background-color: #5a6fd8;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                flex-direction: column;
                gap: 1rem;
            }

            .documents-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                📄 ஆவண மேலாண்மை
            </div>
            <div class="nav-menu">
                <div class="user-info">
                    <span>{{ t.welcome }}, {{ current_user.full_name }}!</span>
                    <a href="{{ url_for('logout') }}" class="nav-link">{{ t.logout }}</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1 class="welcome-title">{{ t.user }} {{ t.dashboard }}</h1>
            <p class="welcome-subtitle">ஸ்கேன் செய்யப்பட்ட ஆவணங்களைப் பார்க்கவும்</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ documents|length }}</div>
                <div class="stat-label">மொத்த ஆவணங்கள்</div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="documents-section" style="margin-bottom: 2rem;">
            <h2 class="section-title">🔍 ஆவண தேடல்</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                <input type="text" id="search-name" placeholder="பெயர்" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                <input type="text" id="search-city" placeholder="நகரம்" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                <select id="search-natchathiram" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                    <option value="">நட்சத்திரம்</option>
                    <option value="அசுவினி">அசுவினி</option>
                    <option value="பரணி">பரணி</option>
                    <option value="கார்த்திகை">கார்த்திகை</option>
                    <option value="ரோகிணி">ரோகிணி</option>
                    <option value="மிருகசீரிடம்">மிருகசீரிடம்</option>
                    <option value="திருவாதிரை">திருவாதிரை</option>
                    <option value="புனர்பூசம்">புனர்பூசம்</option>
                    <option value="பூசம்">பூசம்</option>
                    <option value="ஆயில்யம்">ஆயில்யம்</option>
                    <option value="மகம்">மகம்</option>
                    <option value="பூரம்">பூரம்</option>
                    <option value="உத்திரம்">உத்திரம்</option>
                    <option value="அஸ்தம்">அஸ்தம்</option>
                    <option value="சித்திரை">சித்திரை</option>
                    <option value="சுவாதி">சுவாதி</option>
                    <option value="விசாகம்">விசாகம்</option>
                    <option value="அனுஷம்">அனுஷம்</option>
                    <option value="கேட்டை">கேட்டை</option>
                    <option value="மூலம்">மூலம்</option>
                    <option value="பூராடம்">பூராடம்</option>
                    <option value="உத்திராடம்">உத்திராடம்</option>
                    <option value="திருவோணம்">திருவோணம்</option>
                    <option value="அவிட்டம்">அவிட்டம்</option>
                    <option value="சதயம்">சதயம்</option>
                    <option value="பூரட்டாதி">பூரட்டாதி</option>
                    <option value="உத்திரட்டாதி">உத்திரட்டாதி</option>
                    <option value="ரேவதி">ரேவதி</option>
                </select>
                <select id="search-raasi" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                    <option value="">ராசி</option>
                    <option value="மேஷம்">மேஷம்</option>
                    <option value="ரிஷபம்">ரிஷபம்</option>
                    <option value="மிதுனம்">மிதுனம்</option>
                    <option value="கடகம்">கடகம்</option>
                    <option value="சிம்மம்">சிம்மம்</option>
                    <option value="கன்னி">கன்னி</option>
                    <option value="துலாம்">துலாம்</option>
                    <option value="விருச்சிகம்">விருச்சிகம்</option>
                    <option value="தனுசு">தனுசு</option>
                    <option value="மகரம்">மகரம்</option>
                    <option value="கும்பம்">கும்பம்</option>
                    <option value="மீனம்">மீனம்</option>
                </select>
                <input type="number" id="search-vayathu-min" placeholder="குறைந்த வயது" min="1" max="120" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                <input type="number" id="search-vayathu-max" placeholder="அதிக வயது" min="1" max="120" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
            </div>
            <div style="text-align: center; margin-bottom: 1rem;">
                <button onclick="performSearch()" style="background: #667eea; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">🔍 தேடு</button>
                <button onclick="clearSearch()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">🗑️ அழி</button>
            </div>
        </div>

        <!-- Jathagam Generation Section -->
        <div class="documents-section" style="margin-bottom: 2rem;">
            <h2 class="section-title">🌟 ஜாதக உருவாக்கம்</h2>
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 2rem; border-radius: 10px; text-align: center; color: white; margin-bottom: 1rem;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🌟</div>
                <h3 style="margin-bottom: 1rem; color: white;">உங்கள் ஜாதகம் உருவாக்கவும்</h3>
                <p style="margin-bottom: 1.5rem; opacity: 0.9;">பிறந்த தேதி, நேரம் மற்றும் இடத்தின் அடிப்படையில் துல்லியமான தமிழ் ஜாதகம் உருவாக்கவும்</p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <button onclick="showUserJathagamModal()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 0.8rem 2rem; border-radius: 25px; cursor: pointer; font-size: 1rem; font-weight: bold; transition: all 0.3s;">
                        🌟 ஜாதகம் உருவாக்கு
                    </button>
                    <button onclick="showUserJathagamScannerModal()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 0.8rem 2rem; border-radius: 25px; cursor: pointer; font-size: 1rem; font-weight: bold; transition: all 0.3s;">
                        📷 ஜாதகம் ஸ்கேன் செய்
                    </button>
                </div>
            </div>
        </div>

        <!-- Documents Section -->
        <div class="documents-section">
            <h2 class="section-title">
                📄 ஸ்கேன் செய்யப்பட்ட ஆவணங்கள்
            </h2>

            {% if documents %}
                <div class="documents-grid" id="documents-grid">
                    {% for doc in documents %}
                        <div class="document-card">
                            <div class="document-header">
                                <div class="document-icon">📄</div>
                                <div class="document-title">{{ doc.filename }}</div>
                            </div>

                            <div class="document-info">
                                <strong>{{ t.date }}:</strong> {{ doc.created_at }}
                            </div>

                            {% if doc.user %}
                                <div class="document-info">
                                    <strong>{{ t.user }}:</strong> {{ doc.user.name }}
                                </div>
                                <div class="document-info">
                                    <strong>{{ t.city }}:</strong> {{ doc.user.city }}
                                </div>
                            {% endif %}

                            {% if doc.natchathiram %}
                                <div class="document-info">
                                    <strong>நட்சத்திரம்:</strong> {{ doc.natchathiram }}
                                </div>
                            {% endif %}

                            {% if doc.raasi %}
                                <div class="document-info">
                                    <strong>ராசி:</strong> {{ doc.raasi }}
                                </div>
                            {% endif %}

                            {% if doc.vayathu %}
                                <div class="document-info">
                                    <strong>வயது:</strong> {{ doc.vayathu }}
                                </div>
                            {% endif %}

                            <div class="document-info">
                                <strong>{{ t.size }}:</strong>
                                {% if doc.file_size %}
                                    {{ "%.1f"|format(doc.file_size / 1024) }} KB
                                {% else %}
                                    தெரியவில்லை
                                {% endif %}
                            </div>

                            <div class="document-actions">
                                {% if doc.document_type == 'scanned' %}
                                    <a href="{{ url_for('serve_scanned_file', filename=doc.filename) }}"
                                       class="btn btn-primary" target="_blank">{{ t.view }}</a>
                                {% else %}
                                    <a href="{{ url_for('serve_uploaded_file', filename=doc.filename) }}"
                                       class="btn btn-primary" target="_blank">{{ t.view }}</a>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <h3>ஆவணங்கள் இல்லை</h3>
                    <p>இன்னும் ஸ்கேன் செய்யப்பட்ட ஆவணங்கள் எதுவும் இல்லை.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        function performSearch() {
            const filters = {
                name: document.getElementById('search-name').value,
                city: document.getElementById('search-city').value,
                natchathiram: document.getElementById('search-natchathiram').value,
                raasi: document.getElementById('search-raasi').value,
                vayathu_min: document.getElementById('search-vayathu-min').value,
                vayathu_max: document.getElementById('search-vayathu-max').value,
                document_type: 'scanned' // Users only see scanned documents
            };

            const queryParams = new URLSearchParams();
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    queryParams.append(key, filters[key]);
                }
            });

            fetch(`/advanced_search?${queryParams.toString()}`)
                .then(response => response.json())
                .then(data => {
                    updateDocumentsGrid(data.documents);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    alert('❌ தேடல் பிழை');
                });
        }

        function clearSearch() {
            document.getElementById('search-name').value = '';
            document.getElementById('search-city').value = '';
            document.getElementById('search-natchathiram').value = '';
            document.getElementById('search-raasi').value = '';
            document.getElementById('search-vayathu-min').value = '';
            document.getElementById('search-vayathu-max').value = '';
            location.reload(); // Reload to show all documents
        }

        function updateDocumentsGrid(documents) {
            const grid = document.getElementById('documents-grid');
            grid.innerHTML = '';

            if (documents.length === 0) {
                grid.innerHTML = '<div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: #666;"><div style="font-size: 4rem; margin-bottom: 1rem;">📄</div><h3>தேடல் முடிவுகள் இல்லை</h3><p>வேறு தேடல் வார்த்தைகளை முயற்சிக்கவும்.</p></div>';
                return;
            }

            documents.forEach(doc => {
                const card = document.createElement('div');
                card.className = 'document-card';
                card.innerHTML = `
                    <div class="document-header">
                        <div class="document-icon">📄</div>
                        <div class="document-title">${doc.filename}</div>
                    </div>

                    <div class="document-info">
                        <strong>தேதி:</strong> ${doc.created_at}
                    </div>

                    ${doc.user_name ? `
                        <div class="document-info">
                            <strong>பயனர்:</strong> ${doc.user_name}
                        </div>
                        <div class="document-info">
                            <strong>நகரம்:</strong> ${doc.user_city}
                        </div>
                    ` : ''}

                    ${doc.natchathiram ? `
                        <div class="document-info">
                            <strong>நட்சத்திரம்:</strong> ${doc.natchathiram}
                        </div>
                    ` : ''}

                    ${doc.raasi ? `
                        <div class="document-info">
                            <strong>ராசி:</strong> ${doc.raasi}
                        </div>
                    ` : ''}

                    ${doc.vayathu ? `
                        <div class="document-info">
                            <strong>வயது:</strong> ${doc.vayathu}
                        </div>
                    ` : ''}

                    <div class="document-info">
                        <strong>அளவு:</strong> ${doc.file_size ? (doc.file_size / 1024).toFixed(1) + ' KB' : 'தெரியவில்லை'}
                    </div>

                    <div class="document-actions">
                        <a href="/static/scanned/${doc.filename}" class="btn btn-primary" target="_blank">பார்</a>
                    </div>
                `;
                grid.appendChild(card);
            });
        }

        function showUserJathagamModal() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 500px; width: 90%; margin: 2rem;">
                        <h3 style="margin-bottom: 1rem; text-align: center; color: #333;">🌟 ஜாதக உருவாக்கம்</h3>
                        <p style="text-align: center; color: #666; margin-bottom: 2rem;">உங்கள் பிறந்த விவரங்களின் அடிப்படையில் துல்லியமான தமிழ் ஜாதகம் உருவாக்கவும்</p>

                        <form onsubmit="generateUserJathagam(event)">
                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பெயர்:</label>
                                    <input type="text" id="user-jathagam-name" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பாலினம்:</label>
                                    <select id="user-jathagam-gender" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                        <option value="Male">ஆண்</option>
                                        <option value="Female">பெண்</option>
                                    </select>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பிறந்த தேதி:</label>
                                    <input type="date" id="user-jathagam-birth-date" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பிறந்த நேரம்:</label>
                                    <input type="time" id="user-jathagam-birth-time" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                            </div>

                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பிறந்த இடம்:</label>
                                <input type="text" id="user-jathagam-birth-place" placeholder="சென்னை, தமிழ்நாடு" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                            </div>

                            <div style="background: #e3f2fd; padding: 1rem; border-radius: 5px; margin-bottom: 1rem;">
                                <p style="font-size: 0.9rem; color: #1976d2; margin: 0;">
                                    <strong>குறிப்பு:</strong> இந்த ஜாதகம் Swiss Ephemeris மூலம் துல்லியமான கணக்கீட்டுடன் உருவாக்கப்படும்.
                                    இது கிரக நிலைகள், ராசி, நட்சத்திரம், லக்னம் மற்றும் பாவ அமைப்பு ஆகியவற்றை உள்ளடக்கும்.
                                </p>
                            </div>

                            <div style="text-align: center; margin-top: 1.5rem;">
                                <button type="submit" style="background: #667eea; color: white; border: none; padding: 0.8rem 2rem; border-radius: 25px; margin-right: 0.5rem; cursor: pointer; font-weight: bold;">🌟 ஜாதகம் உருவாக்கு</button>
                                <button type="button" onclick="closeUserJathagamModal()" style="background: #6c757d; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 25px; cursor: pointer;">ரத்து செய்</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            modal.id = 'user-jathagam-modal';
            document.body.appendChild(modal);
        }

        function generateUserJathagam(event) {
            event.preventDefault();
            const name = document.getElementById('user-jathagam-name').value;
            const gender = document.getElementById('user-jathagam-gender').value;
            const birthDate = document.getElementById('user-jathagam-birth-date').value;
            const birthTime = document.getElementById('user-jathagam-birth-time').value;
            const birthPlace = document.getElementById('user-jathagam-birth-place').value || 'சென்னை';

            // Show loading message
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '⏳ உருவாக்குகிறது...';
            submitBtn.disabled = true;

            const formData = new FormData();
            formData.append('name', name);
            formData.append('gender', gender);
            formData.append('birth_date', birthDate);
            formData.append('birth_time', birthTime);
            formData.append('birth_place', birthPlace);

            fetch('/generate_user_jathagam', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;

                if (data.success) {
                    closeUserJathagamModal();
                    // Store data for enhanced print system
                    storeUserJathagamDataForPrint(data.jathagam);
                    showUserJathagamResult(data.jathagam, data.formatted, data.chart_html);
                } else {
                    alert('❌ ஜாதக உருவாக்கம் பிழை: ' + data.message);
                }
            })
            .catch(error => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                console.error('Jathagam generation error:', error);
                alert('❌ ஜாதக உருவாக்கம் பிழை: ' + error.message);
            });
        }

        function closeUserJathagamModal() {
            const modal = document.getElementById('user-jathagam-modal');
            if (modal) {
                modal.remove();
            }
        }

        function showUserJathagamResult(jathagam, formatted, chartHtml) {
            const modal = document.createElement('div');
            const personal = jathagam.personal_details || {};
            const astro = jathagam.astrological_details || {};
            const planets = jathagam.planetary_positions || {};

            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 1000px; width: 95%; margin: 2rem; max-height: 90vh; overflow-y: auto;">
                        <h3 style="margin-bottom: 1rem; text-align: center; color: #333;">🌟 ${personal.name || 'தெரியவில்லை'} அவர்களின் ஜாதகம் 🌟</h3>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                            <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
                                <h4 style="color: #495057; margin-bottom: 1rem;">📋 அடிப்படை தகவல்கள்</h4>
                                <div style="display: grid; gap: 0.5rem;">
                                    <div><strong>பெயர்:</strong> ${personal.name || 'தெரியவில்லை'}</div>
                                    <div><strong>பாலினம்:</strong> ${personal.gender === 'Male' ? 'ஆண்' : 'பெண்'}</div>
                                    <div><strong>பிறந்த தேதி:</strong> ${personal.birth_date || 'தெரியவில்லை'}</div>
                                    <div><strong>பிறந்த நேரம்:</strong> ${personal.birth_time || 'தெரியவில்லை'}</div>
                                    <div><strong>பிறந்த இடம்:</strong> ${personal.birth_place || 'தெரியவில்லை'}</div>
                                </div>
                            </div>

                            <div style="background: #e3f2fd; padding: 1.5rem; border-radius: 8px;">
                                <h4 style="color: #1976d2; margin-bottom: 1rem;">🌙 முக்கிய ஜோதிட தகவல்கள்</h4>
                                <div style="display: grid; gap: 0.5rem;">
                                    <div><strong>சந்திர ராசி:</strong> ${astro.moon_raasi || 'தெரியவில்லை'}</div>
                                    <div><strong>நட்சத்திரம்:</strong> ${astro.moon_nakshatra || 'தெரியவில்லை'}</div>
                                    <div><strong>பாதம்:</strong> ${astro.moon_pada || 'தெரியவில்லை'}</div>
                                    <div><strong>லக்ன ராசி:</strong> ${astro.lagna_raasi || 'தெரியவில்லை'}</div>
                                    <div><strong>லக்ன நட்சத்திரம்:</strong> ${astro.lagna_nakshatra || 'தெரியவில்லை'}</div>
                                </div>
                            </div>
                        </div>

                        <div style="margin-bottom: 2rem;">
                            <h4 style="color: #333; margin-bottom: 1rem; text-align: center;">📊 ஜாதக சக்கரங்கள்</h4>
                            ${chartHtml || '<p style="text-align: center; color: #666;">சக்கரம் கிடைக்கவில்லை</p>'}
                        </div>

                        <div style="text-align: center; margin-top: 1.5rem; display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                            <button onclick="enhancedPrintSystem.enhancedPrintJathagam()" style="background: #28a745; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">🖨️ மேம்பட்ட அச்சு</button>
                            <button onclick="printUserJathagam()" style="background: #17a2b8; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">🖨️ விரைவு அச்சு</button>
                            <button onclick="saveUserJathagamAsPDF()" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">💾 PDF சேமி</button>
                            <button onclick="shareUserJathagam()" style="background: #ffc107; color: #212529; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">📤 பகிர்</button>
                            <button onclick="closeUserJathagamResult()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">மூடு</button>
                        </div>
                    </div>
                </div>
            `;
            modal.id = 'user-jathagam-result-modal';
            document.body.appendChild(modal);
        }

        function closeUserJathagamResult() {
            const modal = document.getElementById('user-jathagam-result-modal');
            if (modal) {
                modal.remove();
            }
        }

        function printUserJathagam() {
            const modal = document.getElementById('user-jathagam-result-modal');
            if (!modal) return;

            const content = modal.querySelector('div > div').cloneNode(true);
            const buttons = content.querySelector('div[style*="text-align: center; margin-top: 1.5rem"]');
            if (buttons) buttons.remove();

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>ஜாதகம் - ${content.querySelector('h3').textContent}</title>
                    <meta charset="UTF-8">
                    <style>
                        body { font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', sans-serif; margin: 20px; }
                        .chart-container { page-break-inside: avoid; }
                        .south-indian-chart-12 { margin: 20px auto; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>${content.outerHTML}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
        }

        function saveUserJathagamAsPDF() {
            alert('📄 PDF சேமிப்பு:\n\n1. "🖨️ அச்சிடு" பொத்தானை அழுத்தவும்\n2. அச்சுப்பொறி தேர்வில் "Save as PDF" தேர்ந்தெடுக்கவும்\n3. கோப்பு பெயர் மற்றும் இடத்தை தேர்ந்தெடுத்து சேமிக்கவும்');
            printUserJathagam();
        }

        function shareUserJathagam() {
            const modal = document.getElementById('user-jathagam-result-modal');
            if (!modal) return;

            const nameElement = modal.querySelector('h3');
            const name = nameElement ? nameElement.textContent : 'ஜாதகம்';

            if (navigator.share) {
                navigator.share({
                    title: name,
                    text: 'தமிழ் ஜாதகம் பகிர்வு',
                    url: window.location.href
                }).catch(console.error);
            } else {
                const shareText = `${name}\n\nதமிழ் ஜாதகம் உருவாக்கப்பட்டது\n${window.location.href}`;
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(shareText).then(() => {
                        alert('📋 ஜாதக தகவல் கிளிப்போர்டில் நகலெடுக்கப்பட்டது!');
                    });
                } else {
                    alert('📤 பகிர்வு:\n\n' + shareText);
                }
            }
        }

        function showUserJathagamScannerModal() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 700px; width: 90%; margin: 2rem;">
                        <h3 style="margin-bottom: 1rem; text-align: center; color: #333;">📷 ஜாதகம் ஸ்கேன் செய்</h3>
                        <p style="text-align: center; color: #666; margin-bottom: 2rem;">உங்கள் உள்ளூர் ஸ்கேனர்/பிரிண்டரில் இருந்து ஜாதக ஆவணத்தை ஸ்கேன் செய்யவும்</p>

                        <div style="background: #e8f5e8; padding: 1rem; border-radius: 5px; margin-bottom: 1.5rem; border-left: 4px solid #28a745;">
                            <h4 style="color: #155724; margin-bottom: 0.5rem;">🔒 முழு தனியுரிமை பாதுகாப்பு</h4>
                            <ul style="margin: 0; color: #155724; font-size: 0.9rem;">
                                <li>🖥️ உங்கள் உள்ளூர் ஸ்கேனர் மட்டும் பயன்படுத்தப்படும்</li>
                                <li>🚫 சர்வர் ஸ்கேனர்கள் அணுகப்படாது</li>
                                <li>📄 ஸ்கேன் செய்யப்பட்ட ஆவணம் உங்கள் கணினியில் மட்டும் சேமிக்கப்படும்</li>
                                <li>🔒 எந்த தகவலும் சர்வருக்கு அனுப்பப்படாது</li>
                            </ul>
                        </div>

                        <div style="text-align: center; margin-top: 1.5rem;">
                            <button onclick="startUserFileUploadScan()" style="background: #28a745; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer; font-weight: bold;">
                                📄 ஆவணம் பதிவேற்று
                            </button>
                            <button onclick="closeUserJathagamScannerModal()" style="background: #6c757d; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer; margin-left: 0.5rem;">
                                மூடு
                            </button>
                        </div>
                    </div>
                </div>
            `;
            modal.id = 'user-jathagam-scanner-modal';
            document.body.appendChild(modal);
        }

        function closeUserJathagamScannerModal() {
            const modal = document.getElementById('user-jathagam-scanner-modal');
            if (modal) {
                modal.remove();
            }
        }

        function startUserFileUploadScan() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.pdf,.jpg,.jpeg,.png,.tiff';
            input.multiple = false;

            input.onchange = function(event) {
                const file = event.target.files[0];
                if (file) {
                    console.log('📄 User file selected:', file.name);
                    uploadUserScannedDocument(file);
                }
            };

            input.click();
        }

        async function uploadUserScannedDocument(file) {
            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('document_type', 'user_scanned_jathagam');
                formData.append('scan_method', 'file_upload');

                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert('✅ ஸ்கேன் செய்யப்பட்ட ஜாதகம் வெற்றிகரமாக சேமிக்கப்பட்டது!');
                    closeUserJathagamScannerModal();
                    // Refresh document list if needed
                    if (typeof loadUserDocuments === 'function') {
                        loadUserDocuments();
                    }
                } else {
                    throw new Error(result.message || 'Upload failed');
                }
            } catch (error) {
                console.error('Upload error:', error);
                alert('❌ பதிவேற்றம் பிழை: ' + error.message);
            }
        }

        // Store current Jathagam data for enhanced print system
        function storeUserJathagamDataForPrint(jathagamData) {
            window.currentJathagamData = jathagamData;
        }
    </script>

    <!-- Enhanced Print System -->
    <script src="{{ url_for('static', filename='js/enhanced_print_system.js') }}"></script>

    <!-- Client-Side Scanner System -->
    <script src="{{ url_for('static', filename='js/client_side_scanner.js') }}"></script>
</body>
</html>
