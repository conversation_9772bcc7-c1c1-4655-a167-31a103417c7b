# User Data Manager - Scanner-First Workflow

## Overview
A comprehensive web application for managing user data with prioritized scanner integration, fallback file upload, and database storage capabilities. The application follows a **scanner-first workflow** where users are prompted to scan documents first before entering personal details.

## Key Features

### 🖨️ Scanner-First Workflow
- **Priority Scanning**: Automatically check for connected scanners on page load
- **Smart Fallback**: If no scanner available, display file upload option
- **Real Scanner Detection**: Supports Windows WIA and Linux SANE scanner interfaces
- **Mock Scanner**: Testing fallback when no physical scanner is connected

### 📄 Document Management
- **Two-Stage Process**: First capture/upload document, then enter user details
- **Document Association**: Link documents to users after personal information is provided
- **Multiple Formats**: Support for images (PNG, JPG, JPEG) and documents (PDF, TXT, CSV, JSON, XML)
- **Secure Storage**: Separate directories for scanned and uploaded files

### 👤 User Management
- **Individual Processing**: Each user is processed individually with their document
- **Database Storage**: All user data persistently stored in SQLite database
- **No Bulk Processing**: Removed bulk data extraction for focused workflow
- **Document Tracking**: Track document count per user

### 🔍 Search and Browse
- **Real-time Search**: Search users by name or city
- **Document Archive**: Browse all documents with user associations
- **User Documents**: View all documents for specific users
- **Statistics Display**: User count and document statistics

### 🌐 Modern Web Interface
- **Responsive Design**: Modern, mobile-friendly interface with gradient backgrounds
- **Interactive Elements**: Real-time scanner status, file upload feedback
- **Step-by-Step Workflow**: Clear visual indicators for each step
- **Toast Notifications**: User feedback for all operations

## Scanner-First Workflow Process

### Step 1: Document Capture
1. **Scanner Check**: Application automatically checks for connected scanners
2. **If Scanner Available**: 
   - Display "✅ Real scanner detected and ready to scan" message
   - Show "📷 Scan Document" button
   - Hide file upload section
3. **If No Scanner**: 
   - Display "❌ No scanner available" message
   - Show file upload section for manual document upload

### Step 2: User Details Entry
1. **After Document Capture**: User details form appears
2. **Required Fields**: Full name and city must be provided
3. **Document Association**: Previously captured document is automatically linked
4. **Database Storage**: User and document are saved together

### Step 3: Completion
1. **Success Confirmation**: Display success message with user and document info
2. **Form Reset**: Clear form for next user
3. **User Directory Update**: New user appears in the directory

## How to Use

### Starting the Application
```bash
cd "c:\Users\<USER>\OneDrive\Desktop\project"
python app.py
```

The application will be available at: http://127.0.0.1:5000

### Adding Users

#### Manual Entry
1. Go to the home page
2. Fill in the "Add User" form with name and city
3. Click "Add User" button

#### File Upload
1. Go to the home page
2. Drag and drop a file or click to browse in the "Upload User Data File" section
3. Supported formats:
   - **CSV**: `John Smith,New York` (one user per line)
   - **JSON**: `{"name": "John Smith", "city": "New York"}`
   - **TXT**: `Name: John Smith City: New York`
   - **XML**: `<name>John Smith</name><city>New York</city>`

#### Scanner Integration
1. Go to the home page
2. Check scanner status in the "Scanner Integration" section
3. Click "Scan Document" when scanner is connected
4. After scanning, enter user name and city for the document
5. Click "Save User" to associate the document with the user

### Viewing Documents

#### Document Archive
1. Click "View Documents" button on the home page
2. Browse all scanned and uploaded documents
3. Use the search box to filter documents
4. Click "View Document" to open in new tab
5. Click "Quick View" for modal preview

#### User-Specific Documents
1. On the home page, click "View Documents" next to any user
2. See all documents associated with that specific user
3. Download or view individual documents

### Search Functionality
- **Global Search**: Use the search box on the documents page
- **API Search**: Use `/api/search?q=query` endpoint
- **Real-time Results**: Search results update as you type

### API Usage

#### Get All Users
```
GET /api/users
```

#### Search Users and Documents
```
GET /api/search?q=search_term
```

#### Check Scanner Status
```
GET /check_scanner
```

#### View User Documents
```
GET /user/{user_id}/documents
```

## Directory Structure
```
project/
├── app.py                 # Main Flask application
├── database.py           # Database models and utilities
├── mock_scanner.py       # Mock scanner functionality
├── test_features.py      # Feature testing script
├── templates/
│   ├── index.html        # Home page
│   ├── documents.html    # Document archive
│   └── user_documents.html # User-specific documents
├── uploads/              # Uploaded files storage
├── scanned/              # Scanned documents storage
└── instance/
    └── user_documents.db # SQLite database
```

## Database Schema

### Users Table
- `id`: Primary key
- `name`: User name
- `city`: User city
- `added_at`: Timestamp
- `source`: Data source (manual, file, scanned)

### Documents Table
- `id`: Primary key
- `filename`: Stored filename
- `original_filename`: Original filename
- `file_path`: Full file path
- `file_type`: File extension
- `file_size`: File size in bytes
- `document_type`: Type (scanned, uploaded)
- `created_at`: Timestamp
- `user_id`: Foreign key to users table

## Security Features
- **File Type Validation**: Only allowed file types are accepted
- **File Size Limits**: Maximum 16MB file uploads
- **Secure Filenames**: All filenames are sanitized
- **Directory Isolation**: Files stored in dedicated directories

## Error Handling
- **Database Rollback**: Automatic rollback on database errors
- **File Validation**: Comprehensive file validation
- **User Feedback**: Clear error messages and notifications
- **Graceful Degradation**: Fallback options when features unavailable

## Testing
Run the comprehensive test suite:
```bash
python test_features.py
```

This will test all major features and provide a detailed report.

## Troubleshooting

### Scanner Issues
- Ensure scanner drivers are installed
- Check scanner connection to computer
- Turn on scanner and wait for ready status
- Use mock scanner for testing when real scanner unavailable

### File Upload Issues
- Check file format (must be .txt, .csv, .json, .xml, .png, .jpg, .jpeg, .pdf)
- Ensure file size is under 16MB
- Verify file contains valid user data in expected format

### Database Issues
- Database is automatically created on first run
- Located at `instance/user_documents.db`
- Use "Clear All" button to reset data if needed

## Performance Notes
- Application uses SQLite for simplicity
- Suitable for moderate data volumes
- For production use, consider PostgreSQL or MySQL
- File serving is handled by Flask (consider nginx for production)

## Future Enhancements
- User authentication and authorization
- Document deletion functionality
- Advanced filtering options
- Export functionality
- Batch document processing
- Email notifications
- Document versioning

## Migration Notes

### Database Migration Completed
- **Issue**: Some older documents had relative file paths instead of absolute paths
- **Solution**: Applied database migration to convert relative paths to absolute paths
- **Cleanup**: Removed orphaned database records for files that no longer exist
- **Result**: All document file paths are now absolute and verified to exist

### Migration Scripts
- `check_migration.py`: Analyze database state and identify migration issues
- `migrate_database.py`: Perform database migration and cleanup
- Use these scripts if you encounter file path issues

### Image Upload Fix
- **Issue**: Image files (PNG, JPG, JPEG) were causing errors when uploaded because the system tried to read them as text files
- **Solution**: Added file type detection in `extract_user_data_from_file()` function
- **Result**: Image files are now properly stored without attempting text extraction
- **Behavior**: 
  - Text files (.txt, .csv, .json, .xml): User data extraction attempted
  - Image files (.png, .jpg, .jpeg, .pdf): Stored as documents without user extraction
