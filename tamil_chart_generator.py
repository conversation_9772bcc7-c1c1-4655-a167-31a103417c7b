#!/usr/bin/env python3
"""
Tamil Chart Generator for South Indian Style Jathagam Charts
Creates Rasi and Navamsa charts with proper Tamil Unicode support
"""
import json
from typing import Dict, List

class TamilChartGenerator:
    """Generate South Indian style charts with Tamil Unicode"""
    
    def __init__(self):
        # South Indian chart house mapping (fixed positions)
        self.south_indian_positions = {
            1: (1, 1),   # லக்னம் - Top right
            2: (0, 1),   # தன - Top center
            3: (0, 0),   # சகோதர - Top left
            4: (1, 0),   # மாதா - Left center
            5: (2, 0),   # புத்திர - Bottom left
            6: (2, 1),   # ரிபு - Bottom center
            7: (2, 2),   # கலத்திர - Bottom right
            8: (1, 2),   # ஆயுள் - Right center
            9: (0, 2),   # பாக்கிய - Top right corner
            10: (0, 1),  # கர்ம - Top center (shared with 2nd house)
            11: (1, 2),  # லாப - Right center (shared with 8th house)
            12: (2, 1)   # வ்யய - Bottom center (shared with 6th house)
        }
        
        # Tamil house names
        self.house_names = [
            'லக்னம்', 'தன', 'சகோதர', 'மாதா', 'புத்திர', 'ரிபு',
            'கலத்திர', 'ஆயுள்', 'பாக்கிய', 'கர்ம', 'லாப', 'வ்யய'
        ]
        
        # Tamil Raasi names
        self.raasi_names = [
            'மேஷம்', 'ரிஷபம்', 'மிதுனம்', 'கடகம்', 'சிம்மம்', 'கன்னி',
            'துலாம்', 'விருச்சிகம்', 'தனுசு', 'மகரம்', 'கும்பம்', 'மீனம்'
        ]
        
        # Planet abbreviations in Tamil
        self.planet_abbrev = {
            'சூரியன்': 'சூ',
            'சந்திரன்': 'ச',
            'செவ்வாய்': 'செ',
            'புதன்': 'பு',
            'குரு': 'கு',
            'சுக்கிரன்': 'சு',
            'சனி': 'ச',
            'ராகு': 'ரா',
            'கேது': 'கே'
        }
    
    def generate_rasi_chart_html(self, rasi_chart: Dict, title: str = "ராசி சக்கரம்") -> str:
        """Generate HTML for South Indian style Rasi chart with proper 12 houses"""

        chart_html = f"""
        <div class="chart-container">
            <h3 class="chart-title">{title}</h3>
            <div class="south-indian-chart-12">
        """

        # Define the 12-house layout in South Indian style
        # Layout: 4x4 grid with center cells empty
        house_layout = [
            [4,  3,  2,  1],   # Top row: 4th, 3rd, 2nd, 1st house
            [5,  0,  0,  12],  # Second row: 5th, empty, empty, 12th house
            [6,  0,  0,  11],  # Third row: 6th, empty, empty, 11th house
            [7,  8,  9,  10]   # Bottom row: 7th, 8th, 9th, 10th house
        ]

        for row_idx, row in enumerate(house_layout):
            chart_html += '<div class="chart-row">'
            for col_idx, house_num in enumerate(row):
                if house_num == 0:
                    # Empty center cells
                    chart_html += '<div class="chart-cell center-cell"></div>'
                else:
                    # House cell
                    house_name = self.house_names[house_num - 1]
                    house_data = rasi_chart.get(house_name, {'raasi': '', 'planets': []})

                    raasi = house_data.get('raasi', '')
                    planets = house_data.get('planets', [])

                    # Abbreviate planet names
                    planet_abbrevs = []
                    for planet in planets:
                        abbrev = self.planet_abbrev.get(planet, planet[:2])
                        planet_abbrevs.append(abbrev)

                    planets_text = ' '.join(planet_abbrevs) if planet_abbrevs else ''

                    chart_html += f"""
                    <div class="chart-cell house-cell house-{house_num}">
                        <div class="house-number">{house_num}</div>
                        <div class="house-name">{house_name}</div>
                        <div class="raasi-name">{raasi}</div>
                        <div class="planets">{planets_text}</div>
                    </div>
                    """

            chart_html += '</div>'

        chart_html += """
            </div>
        </div>
        """

        return chart_html
    
    def _get_house_position(self, house_num: int) -> tuple:
        """Get grid position for house number in South Indian style"""
        # Proper South Indian chart mapping - each house gets its own position
        positions = {
            1: (1, 2),   # லக்னம் - Right center
            2: (0, 2),   # தன - Top right
            3: (0, 1),   # சகோதர - Top center
            4: (0, 0),   # மாதா - Top left
            5: (1, 0),   # புத்திர - Left center
            6: (2, 0),   # ரிபு - Bottom left
            7: (2, 1),   # கலத்திர - Bottom center
            8: (2, 2),   # ஆயுள் - Bottom right
            9: (1, 2),   # பாக்கிய - Right center (shared with 1st)
            10: (0, 1),  # கர்ம - Top center (shared with 3rd)
            11: (1, 0),  # லாப - Left center (shared with 5th)
            12: (2, 1)   # வ்யய - Bottom center (shared with 7th)
        }
        return positions.get(house_num, (1, 1))
    
    def _generate_cell_content(self, houses_in_position: List, row: int, col: int) -> str:
        """Generate content for a chart cell"""
        if not houses_in_position:
            return '<div class="empty-cell"></div>'
        
        content = ""
        for house_num, house_data in houses_in_position:
            house_name = self.house_names[house_num - 1]
            raasi = house_data.get('raasi', '')
            planets = house_data.get('planets', [])
            
            # Abbreviate planet names
            planet_abbrevs = []
            for planet in planets:
                abbrev = self.planet_abbrev.get(planet, planet[:2])
                planet_abbrevs.append(abbrev)
            
            planets_text = ' '.join(planet_abbrevs) if planet_abbrevs else ''
            
            content += f"""
            <div class="house-info">
                <div class="house-number">{house_num}</div>
                <div class="raasi-name">{raasi}</div>
                <div class="planets">{planets_text}</div>
            </div>
            """
        
        return content
    
    def generate_chart_css(self) -> str:
        """Generate CSS for Tamil charts with proper 12-box layout"""
        return """
        <style>
        .chart-container {
            margin: 1rem 0;
            font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', sans-serif;
        }

        .chart-title {
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #333;
        }

        .south-indian-chart-12 {
            display: grid;
            grid-template-rows: repeat(4, 1fr);
            width: 400px;
            height: 400px;
            margin: 0 auto;
            border: 3px solid #333;
            background: #fff;
        }

        .chart-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
        }

        .chart-cell {
            border: 1px solid #666;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0.3rem;
            font-size: 0.7rem;
            position: relative;
            min-height: 100px;
        }

        .house-cell {
            background: #fff;
        }

        .center-cell {
            background: #f8f9fa;
            border: none;
        }

        .house-number {
            font-size: 0.6rem;
            color: #666;
            position: absolute;
            top: 2px;
            left: 2px;
            font-weight: bold;
        }

        .house-name {
            font-size: 0.6rem;
            color: #333;
            margin-bottom: 0.2rem;
            font-weight: 600;
        }

        .raasi-name {
            font-weight: bold;
            color: #000;
            font-size: 0.8rem;
            margin-bottom: 0.3rem;
            text-align: center;
        }

        .planets {
            color: #d32f2f;
            font-weight: bold;
            font-size: 0.7rem;
            line-height: 1.2;
            text-align: center;
        }

        /* House-specific styling */
        .house-1 { background: #fff3e0; } /* லக்னம் */
        .house-2 { background: #f3e5f5; } /* தன */
        .house-3 { background: #e8f5e8; } /* சகோதர */
        .house-4 { background: #e3f2fd; } /* மாதா */
        .house-5 { background: #fff8e1; } /* புத்திர */
        .house-6 { background: #fce4ec; } /* ரிபு */
        .house-7 { background: #f1f8e9; } /* கலத்திர */
        .house-8 { background: #e0f2f1; } /* ஆயுள் */
        .house-9 { background: #fff3e0; } /* பாக்கிய */
        .house-10 { background: #e8eaf6; } /* கர்ம */
        .house-11 { background: #f9fbe7; } /* லாப */
        .house-12 { background: #fdf2e9; } /* வ்யய */

        /* Border styling for proper chart appearance */
        .house-cell {
            border: 2px solid #333;
        }

        @media (max-width: 768px) {
            .south-indian-chart-12 {
                width: 320px;
                height: 320px;
            }

            .chart-cell {
                font-size: 0.6rem;
                min-height: 80px;
                padding: 0.2rem;
            }

            .house-name {
                font-size: 0.5rem;
            }

            .raasi-name {
                font-size: 0.7rem;
            }

            .planets {
                font-size: 0.6rem;
            }
        }

        @media (max-width: 480px) {
            .south-indian-chart-12 {
                width: 280px;
                height: 280px;
            }

            .chart-cell {
                font-size: 0.5rem;
                min-height: 70px;
                padding: 0.1rem;
            }

            .house-name {
                font-size: 0.4rem;
            }

            .raasi-name {
                font-size: 0.6rem;
            }

            .planets {
                font-size: 0.5rem;
            }
        }
        </style>
        """
    
    def generate_complete_chart_html(self, jathagam_data: Dict) -> str:
        """Generate complete HTML with both Rasi and Navamsa charts"""
        if not jathagam_data:
            return "<p>ஜாதக தகவல் கிடைக்கவில்லை</p>"
        
        rasi_chart = jathagam_data.get('rasi_chart', {})
        navamsa_chart = jathagam_data.get('navamsa_chart', {})
        personal = jathagam_data.get('personal_details', {})
        astro = jathagam_data.get('astrological_details', {})
        
        html = self.generate_chart_css()
        
        html += f"""
        <div class="jathagam-container">
            <div class="jathagam-header">
                <h2>🌟 {personal.get('name', 'தெரியவில்லை')} அவர்களின் ஜாதகம் 🌟</h2>
                <div class="birth-details">
                    <p><strong>பிறந்த தேதி:</strong> {personal.get('birth_date', 'தெரியவில்லை')}</p>
                    <p><strong>பிறந்த நேரம்:</strong> {personal.get('birth_time', 'தெரியவில்லை')}</p>
                    <p><strong>பிறந்த இடம்:</strong> {personal.get('birth_place', 'தெரியவில்லை')}</p>
                </div>
                <div class="key-details">
                    <p><strong>ராசி:</strong> {astro.get('moon_raasi', 'தெரியவில்லை')}</p>
                    <p><strong>நட்சத்திரம்:</strong> {astro.get('moon_nakshatra', 'தெரியவில்லை')}</p>
                    <p><strong>லக்னம்:</strong> {astro.get('lagna_raasi', 'தெரியவில்லை')}</p>
                </div>
            </div>
            
            <div class="charts-container">
                <div class="chart-section">
                    {self.generate_rasi_chart_html(rasi_chart, "ராசி சக்கரம்")}
                </div>
                <div class="chart-section">
                    {self.generate_rasi_chart_html(navamsa_chart, "நவாம்ச சக்கரம்")}
                </div>
            </div>
        </div>
        """
        
        return html

# Test function
if __name__ == "__main__":
    # Test chart generation
    chart_gen = TamilChartGenerator()
    
    # Sample data
    sample_rasi_chart = {
        'லக்னம்': {'raasi': 'மேஷம்', 'planets': ['சூரியன்']},
        'தன': {'raasi': 'ரிஷபம்', 'planets': ['சந்திரன்', 'புதன்']},
        'சகோதர': {'raasi': 'மிதுனம்', 'planets': []},
        'மாதா': {'raasi': 'கடகம்', 'planets': ['குரு']},
        'புத்திர': {'raasi': 'சிம்மம்', 'planets': []},
        'ரிபு': {'raasi': 'கன்னி', 'planets': ['செவ்வாய்']},
        'கலத்திர': {'raasi': 'துலாம்', 'planets': ['சுக்கிரன்']},
        'ஆயுள்': {'raasi': 'விருச்சிகம்', 'planets': []},
        'பாக்கிய': {'raasi': 'தனுசு', 'planets': ['சனி']},
        'கர்ம': {'raasi': 'மகரம்', 'planets': []},
        'லாப': {'raasi': 'கும்பம்', 'planets': ['ராகு']},
        'வ்யய': {'raasi': 'மீனம்', 'planets': ['கேது']}
    }
    
    html_output = chart_gen.generate_rasi_chart_html(sample_rasi_chart)
    print("✅ Tamil chart HTML generated successfully!")
    print("Chart contains proper Tamil Unicode and South Indian style layout.")
