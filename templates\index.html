<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Data Manager - Scanner First</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }

        .input-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .users-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            max-height: 80vh;
            overflow-y: auto;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            grid-column: 1 / -1;
        }

        h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        /* Alert Styles */
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        /* Section Styles */
        .scanner-section, .file-upload-section, .user-details-form {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .scanner-section:hover, .file-upload-section:hover {
            transform: translateY(-2px);
        }

        .scanner-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .scanner-connected {
            background: linear-gradient(45deg, #d4edda, #c3e6cb);
            border: 1px solid #b8dabd;
            color: #155724;
        }

        .scanner-disconnected {
            background: linear-gradient(45deg, #f8d7da, #f1b0b7);
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .info-box {
            background: #e8f4fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        /* Button Styles */
        .scan-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 10px;
        }

        .scan-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }

        .scan-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .submit-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .file-input {
            width: 100%;
            padding: 10px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            background: #f8f9fa;
            margin-bottom: 15px;
            cursor: pointer;
        }

        .file-input:hover {
            border-color: #764ba2;
            background: #e9ecef;
        }

        /* Users List Styles */
        .users-list {
            list-style: none;
        }

        .user-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .user-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .user-name {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .user-city {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .user-source {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }

        .user-actions {
            margin-top: 10px;
        }

        .view-docs-btn {
            background: linear-gradient(45deg, #48bb78, #38a169);
            border: 1px solid rgba(255,255,255,0.3);
            color: white !important;
            padding: 5px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .view-docs-btn:hover {
            background: linear-gradient(45deg, #38a169, #2f855a);
            transform: translateY(-1px);
        }

        .clear-btn {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin-left: 10px;
        }

        .stats {
            text-align: center;
            padding: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .stats-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }

        .search-box {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 20px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 User Data Manager</h1>
        
        <!-- Document Input Section -->
        <div class="input-section">
            <h2>📄 Document Processing</h2>
            
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <!-- Scanner Section (Primary Option) -->
            <div class="scanner-section">
                <h3>📱 Scanner Option (Preferred)</h3>
                <div class="info-box">
                    <strong>📋 Step 1:</strong> Check if scanner is available and use it to capture document.<br>
                    <small>If scanner is not available, you can upload a file manually below.</small>
                </div>
                
                <div id="scannerStatus" class="scanner-status scanner-disconnected">
                    <span>🔍 Checking scanner...</span>
                    <button onclick="checkScanner()" style="background: none; border: none; color: inherit; cursor: pointer;">🔄</button>
                </div>
                
                <button id="scanBtn" class="scan-btn" onclick="scanDocument()" disabled>
                    📷 Scan Document
                </button>
                
                <div id="scanProgress" style="display: none;">
                    <p>📱 Scanning in progress...</p>
                </div>
            </div>
            
            <!-- File Upload Section (Fallback Option) -->
            <div class="file-upload-section">
                <h3>📁 File Upload (If Scanner Not Available)</h3>
                <div class="info-box">
                    <strong>📋 Alternative:</strong> If scanner is not available, upload a document file.<br>
                    <small>Supported: Images (PNG, JPG, JPEG), Documents (PDF), Data files (CSV, JSON, TXT, XML)</small>
                </div>
                
                <form id="fileUploadForm" style="display: none;">
                    <input type="file" class="file-input" id="documentFile" 
                           accept=".txt,.csv,.json,.xml,.png,.jpg,.jpeg,.pdf"
                           onchange="handleFileSelect(this)">
                    <button type="button" class="scan-btn" onclick="processUploadedFile()" id="uploadBtn" style="display: none;">
                        📤 Process Uploaded File
                    </button>
                </form>
            </div>
            
            <!-- User Details Form (Shows after document is captured/uploaded) -->
            <div class="user-details-form" id="userDetailsForm" style="display: none;">
                <h3>👤 Enter User Details</h3>
                <form method="post" action="/" id="userForm">
                    <input type="hidden" id="documentPath" name="document_path">
                    <input type="hidden" id="documentType" name="document_type">
                    
                    <div class="form-group">
                        <label for="name">Full Name:</label>
                        <input type="text" id="name" name="name" required placeholder="Enter your full name">
                    </div>
                    
                    <div class="form-group">
                        <label for="city">City:</label>
                        <input type="text" id="city" name="city" required placeholder="Enter your city">
                    </div>
                    
                    <button type="submit" class="submit-btn">💾 Save User with Document</button>
                </form>
            </div>
        </div>
        
        <!-- Users Directory -->
        <div class="users-section">
            <h2>👥 User Directory 
                {% if users %}
                    <button class="clear-btn" onclick="clearUsers()">Clear All</button>
                {% endif %}
            </h2>
            
            {% if users %}
                <div class="stats">
                    <span class="stats-number">{{ users|length }}</span>
                    <span>Total Users</span>
                </div>
                <input type="text" class="search-box" placeholder="🔍 Search users by name or city..." onkeyup="searchUsers(this.value)">
                <ul class="users-list">
                    {% for user in users %}
                        <li class="user-card" onclick="highlightUser(this)" data-name="{{ user.name.lower() }}" data-city="{{ user.city.lower() }}">
                            <div class="user-name">{{ user.name }}
                                {% if user.source %}
                                    <span class="user-source">{{ user.source }}</span>
                                {% endif %}
                            </div>
                            <div class="user-city">📍 {{ user.city }}</div>
                            <div class="user-actions">
                                <a href="/user/{{ user.id }}/documents" class="view-docs-btn">📄 View Documents ({{ user.document_count }})</a>
                                <button class="edit-btn" onclick="event.stopPropagation(); showEditModal({{ user.id }}, '{{ user.name|escapejs }}', '{{ user.city|escapejs }}')">✏️ Edit</button>
                                <button class="delete-btn" onclick="event.stopPropagation(); deleteUser({{ user.id }}, '{{ user.name|escapejs }}')">🗑️ Delete</button>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <div class="info-box">
                    <p><strong>👋 Welcome!</strong></p>
                    <p>Start by scanning a document or uploading a file, then enter user details.</p>
                </div>
            {% endif %}
            
            <div style="text-align: center; margin-top: 20px;">
                <a href="/documents" style="background: linear-gradient(45deg, #17a2b8, #138496); color: white; padding: 10px 20px; border-radius: 8px; text-decoration: none; font-weight: 600;">📚 View All Documents</a>
            </div>
        </div>
    </div>
    
    <script>
        let currentDocumentPath = null;
        let currentDocumentType = null;
        
        // Check scanner status on page load
        window.onload = function() {
            checkScanner();
        };
        
        function checkScanner() {
            fetch('/check_scanner')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('scannerStatus');
                    const scanBtn = document.getElementById('scanBtn');
                    const fileUploadForm = document.getElementById('fileUploadForm');
                    
                    if (data.connected) {
                        statusDiv.className = 'scanner-status scanner-connected';
                        statusDiv.innerHTML = '<span>' + data.message + '</span><button onclick="checkScanner()" style="background: none; border: none; color: inherit; cursor: pointer;">🔄</button>';
                        scanBtn.disabled = false;
                        fileUploadForm.style.display = 'none';
                    } else {
                        statusDiv.className = 'scanner-status scanner-disconnected';
                        statusDiv.innerHTML = '<span>❌ No scanner available</span><button onclick="checkScanner()" style="background: none; border: none; color: inherit; cursor: pointer;">🔄</button>';
                        scanBtn.disabled = true;
                        fileUploadForm.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error checking scanner:', error);
                    const statusDiv = document.getElementById('scannerStatus');
                    statusDiv.className = 'scanner-status scanner-disconnected';
                    statusDiv.innerHTML = '<span>❌ Scanner check failed</span><button onclick="checkScanner()" style="background: none; border: none; color: inherit; cursor: pointer;">🔄</button>';
                    document.getElementById('scanBtn').disabled = true;
                    document.getElementById('fileUploadForm').style.display = 'block';
                });
        }
        
        function scanDocument() {
            const progressDiv = document.getElementById('scanProgress');
            const scanBtn = document.getElementById('scanBtn');
            
            progressDiv.style.display = 'block';
            scanBtn.disabled = true;
            scanBtn.textContent = '📱 Scanning...';
            
            fetch('/scan_document', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    progressDiv.style.display = 'none';
                    scanBtn.disabled = false;
                    scanBtn.textContent = '📷 Scan Document';
                    
                    if (data.success) {
                        currentDocumentPath = data.filename;
                        currentDocumentType = 'scanned';
                        showUserDetailsForm();
                        alert('✅ Document scanned successfully! Please enter user details below.');
                    } else {
                        alert('❌ Scanning failed: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error scanning:', error);
                    progressDiv.style.display = 'none';
                    scanBtn.disabled = false;
                    scanBtn.textContent = '📷 Scan Document';
                    alert('❌ Scanning error occurred');
                });
        }
        
        function handleFileSelect(input) {
            const uploadBtn = document.getElementById('uploadBtn');
            
            if (input.files && input.files[0]) {
                uploadBtn.style.display = 'block';
                uploadBtn.textContent = `📤 Process: ${input.files[0].name}`;
            } else {
                uploadBtn.style.display = 'none';
            }
        }
        
        function processUploadedFile() {
            const fileInput = document.getElementById('documentFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file first.');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            fetch('/upload_document', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentDocumentPath = data.filename;
                    currentDocumentType = 'uploaded';
                    showUserDetailsForm();
                    alert('✅ File uploaded successfully! Please enter user details below.');
                } else {
                    alert('❌ Upload failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error uploading:', error);
                alert('❌ Upload error occurred');
            });
        }
        
        function showUserDetailsForm() {
            const userDetailsForm = document.getElementById('userDetailsForm');
            const documentPathInput = document.getElementById('documentPath');
            const documentTypeInput = document.getElementById('documentType');
            
            documentPathInput.value = currentDocumentPath;
            documentTypeInput.value = currentDocumentType;
            
            userDetailsForm.style.display = 'block';
            userDetailsForm.scrollIntoView({ behavior: 'smooth' });
        }
        
        function searchUsers(searchTerm) {
            const userCards = document.querySelectorAll('.user-card');
            const term = searchTerm.toLowerCase();
            
            userCards.forEach(card => {
                const name = card.getAttribute('data-name');
                const city = card.getAttribute('data-city');
                
                if (name.includes(term) || city.includes(term)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
        
        function highlightUser(element) {
            // Remove previous highlights
            document.querySelectorAll('.user-card').forEach(card => {
                card.style.background = 'linear-gradient(45deg, #f8f9fa, #e9ecef)';
            });
            
            // Highlight selected user
            element.style.background = 'linear-gradient(45deg, #e3f2fd, #bbdefb)';
        }
        
        function clearUsers() {
            if (confirm('Are you sure you want to clear all users and documents? This action cannot be undone.')) {
                window.location.href = '/clear';
            }
        }
        
        // Auto-clear form after successful submission
        if (window.location.search.includes('success=true')) {
            setTimeout(() => {
                document.getElementById('userForm').reset();
                document.getElementById('userDetailsForm').style.display = 'none';
                currentDocumentPath = null;
                currentDocumentType = null;
            }, 1000);
        }

        function showEditModal(userId, userName, userCity) {
            document.getElementById('editUserId').value = userId;
            document.getElementById('editUserName').value = userName;
            document.getElementById('editUserCity').value = userCity;
            document.getElementById('editUserModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editUserModal').style.display = 'none';
        }

        function submitEditUser() {
            const userId = document.getElementById('editUserId').value;
            const name = document.getElementById('editUserName').value;
            const city = document.getElementById('editUserCity').value;
            fetch(`/edit_user/${userId}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ name, city })
            })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Edit failed: ' + data.message);
                }
            });
        }

        function deleteUser(userId, userName) {
            if (confirm(`Are you sure you want to delete user "${userName}" and all their documents?`)) {
                fetch(`/delete_user/${userId}`, { method: 'POST' })
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Delete failed: ' + data.message);
                    }
                });
            }
        }
    </script>

    <!-- Edit User Modal -->
    <div id="editUserModal" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.3); z-index:1000; align-items:center; justify-content:center;">
        <div style="background:white; padding:30px; border-radius:12px; max-width:350px; margin:100px auto; position:relative;">
            <h3>Edit User</h3>
            <input type="hidden" id="editUserId">
            <div style="margin-bottom:12px;">
                <label>Name:</label>
                <input type="text" id="editUserName" style="width:100%; padding:8px; border-radius:5px; border:1px solid #ccc;">
            </div>
            <div style="margin-bottom:12px;">
                <label>City:</label>
                <input type="text" id="editUserCity" style="width:100%; padding:8px; border-radius:5px; border:1px solid #ccc;">
            </div>
            <div style="text-align:right;">
                <button onclick="closeEditModal()" style="margin-right:10px;">Cancel</button>
                <button onclick="submitEditUser()">Save</button>
            </div>
        </div>
    </div>
</body>
</html>
