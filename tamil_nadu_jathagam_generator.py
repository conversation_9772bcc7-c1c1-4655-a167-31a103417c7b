#!/usr/bin/env python3
"""
Tamil Nadu Based Jathagam Generator
Using pyswi<PERSON><PERSON> (Swiss Ephemeris) with <PERSON><PERSON><PERSON>
Following traditional Tamil astrology methods used in Kalaimagal, Kala Chakra software
"""
import swisseph as swe
from geopy.geocoders import Nominatim
from timezonefinder import TimezoneFinder
import pytz
from datetime import datetime, date, time
from typing import Dict, List, Tuple, Optional
import math

class TamilNaduJathagamGenerator:
    """
    Tamil Nadu based Jathagam generator using Swiss Ephemeris
    Following traditional Tamil astrology calculations
    """
    
    def __init__(self):
        # Set <PERSON>i <PERSON> (default for Tamil Nadu)
        swe.set_sid_mode(swe.SIDM_LAHIRI)
        
        # Initialize location services
        self.geolocator = Nominatim(user_agent="tamil_jathagam_generator")
        self.tf = TimezoneFinder()
        
        # Tamil Rasi names (12 zodiac signs)
        self.tamil_rasi_names = [
            'மேஷம்',      # Aries (0°-30°)
            'ரிஷபம்',     # <PERSON><PERSON> (30°-60°)
            'மிதுனம்',     # <PERSON> (60°-90°)
            'கடகம்',      # <PERSON> (90°-120°)
            'சிம்மம்',     # <PERSON> (120°-150°)
            'கன்னி',      # Virgo (150°-180°)
            'துலாம்',     # <PERSON><PERSON> (180°-210°)
            'விருச்சிகம்', # Scorpio (210°-240°)
            'தனுசு',      # Sagittarius (240°-270°)
            'மகரம்',      # Capricorn (270°-300°)
            'கும்பம்',     # Aquarius (300°-330°)
            'மீனம்'       # Pisces (330°-360°)
        ]
        
        # Tamil Nakshatra names (27 stars)
        self.tamil_nakshatra_names = [
            'அசுவினி', 'பரணி', 'கார்த்திகை', 'ரோகிணி', 'மிருகசீர்ஷம்', 'திருவாதிரை',
            'புனர்பூசம்', 'பூசம்', 'ஆயில்யம்', 'மகம்', 'பூரம்', 'உத்திரம்',
            'ஹஸ்தம்', 'சித்திரை', 'சுவாதி', 'விசாகம்', 'அனுஷம்', 'கேட்டை',
            'மூலம்', 'பூராடம்', 'உத்திராடம்', 'திருவோணம்', 'அவிட்டம்', 'சதயம்',
            'பூரட்டாதி', 'உத்திரட்டாதி', 'ரேவதி'
        ]
        
        # Tamil planet names
        self.tamil_planet_names = {
            'Sun': 'சூரியன்',
            'Moon': 'சந்திரன்',
            'Mars': 'செவ்வாய்',
            'Mercury': 'புதன்',
            'Jupiter': 'குரு',
            'Venus': 'சுக்கிரன்',
            'Saturn': 'சனி',
            'Rahu': 'ராகு',
            'Ketu': 'கேது'
        }
        
        # Swiss Ephemeris planet constants
        self.planet_constants = {
            'Sun': swe.SUN,
            'Moon': swe.MOON,
            'Mars': swe.MARS,
            'Mercury': swe.MERCURY,
            'Jupiter': swe.JUPITER,
            'Venus': swe.VENUS,
            'Saturn': swe.SATURN,
            'Rahu': swe.MEAN_NODE,  # Mean Rahu
            'Ketu': swe.MEAN_NODE   # Ketu is 180° opposite to Rahu
        }
    
    def get_coordinates_and_timezone(self, place_name: str) -> Tuple[float, float, str]:
        """
        Get latitude, longitude and timezone for a place in Tamil Nadu
        """
        try:
            # Search with Tamil Nadu context for better accuracy
            search_query = f"{place_name}, Tamil Nadu, India"
            location = self.geolocator.geocode(search_query, timeout=10)
            
            if location:
                lat, lng = location.latitude, location.longitude
                timezone_str = self.tf.timezone_at(lat=lat, lng=lng)
                return lat, lng, timezone_str or 'Asia/Kolkata'
            else:
                # Fallback to Chennai coordinates
                return 13.0827, 80.2707, 'Asia/Kolkata'
                
        except Exception as e:
            print(f"Location error: {e}")
            # Default to Chennai
            return 13.0827, 80.2707, 'Asia/Kolkata'
    
    def calculate_julian_day(self, birth_date: date, birth_time: time, timezone_str: str) -> float:
        """
        Calculate Julian Day for the birth date and time
        """
        try:
            # Create timezone-aware datetime
            tz = pytz.timezone(timezone_str)
            birth_datetime = datetime.combine(birth_date, birth_time)
            localized_dt = tz.localize(birth_datetime)
            
            # Convert to UTC
            utc_dt = localized_dt.astimezone(pytz.UTC)
            
            # Calculate Julian Day
            julian_day = swe.julday(
                utc_dt.year, utc_dt.month, utc_dt.day,
                utc_dt.hour + utc_dt.minute/60.0 + utc_dt.second/3600.0
            )
            
            return julian_day
            
        except Exception as e:
            print(f"Julian day calculation error: {e}")
            return swe.julday(birth_date.year, birth_date.month, birth_date.day, 12.0)
    
    def calculate_planetary_positions(self, julian_day: float) -> Dict:
        """
        Calculate sidereal planetary positions using Swiss Ephemeris
        """
        planetary_positions = {}
        
        for planet_name, planet_const in self.planet_constants.items():
            try:
                if planet_name == 'Ketu':
                    # Ketu is 180° opposite to Rahu
                    rahu_result = swe.calc_ut(julian_day, swe.MEAN_NODE, swe.FLG_SIDEREAL)
                    ketu_longitude = (rahu_result[0][0] + 180) % 360
                    longitude = ketu_longitude
                else:
                    result = swe.calc_ut(julian_day, planet_const, swe.FLG_SIDEREAL)
                    longitude = result[0][0]
                
                # Calculate Rasi (30° segments)
                rasi_number = int(longitude / 30) + 1
                degrees_in_sign = longitude % 30
                
                # Calculate Nakshatra (13°20' segments)
                nakshatra_number = int(longitude / (360/27)) + 1
                nakshatra_degrees = longitude % (360/27)
                
                # Calculate Pada (1/4 of Nakshatra)
                pada = int(nakshatra_degrees / (360/27/4)) + 1
                
                planetary_positions[planet_name] = {
                    'longitude': longitude,
                    'rasi_number': rasi_number,
                    'rasi_name': self.tamil_rasi_names[rasi_number - 1],
                    'degrees_in_sign': degrees_in_sign,
                    'nakshatra_number': nakshatra_number,
                    'nakshatra_name': self.tamil_nakshatra_names[nakshatra_number - 1],
                    'pada': pada,
                    'tamil_name': self.tamil_planet_names[planet_name]
                }
                
            except Exception as e:
                print(f"Error calculating {planet_name}: {e}")
                continue
        
        return planetary_positions
    
    def calculate_lagna(self, julian_day: float, latitude: float, longitude: float) -> Dict:
        """
        Calculate Lagna (Ascendant) using Swiss Ephemeris
        """
        try:
            # Calculate houses using Placidus system
            houses = swe.houses(julian_day, latitude, longitude, b'P')
            ascendant_longitude = houses[1][0]  # Ascendant is the first house cusp
            
            # Apply sidereal correction
            ayanamsa = swe.get_ayanamsa(julian_day)
            sidereal_ascendant = (ascendant_longitude - ayanamsa) % 360
            
            # Calculate Rasi and Nakshatra for Lagna
            rasi_number = int(sidereal_ascendant / 30) + 1
            degrees_in_sign = sidereal_ascendant % 30
            
            nakshatra_number = int(sidereal_ascendant / (360/27)) + 1
            nakshatra_degrees = sidereal_ascendant % (360/27)
            pada = int(nakshatra_degrees / (360/27/4)) + 1
            
            return {
                'longitude': sidereal_ascendant,
                'rasi_number': rasi_number,
                'rasi_name': self.tamil_rasi_names[rasi_number - 1],
                'degrees_in_sign': degrees_in_sign,
                'nakshatra_number': nakshatra_number,
                'nakshatra_name': self.tamil_nakshatra_names[nakshatra_number - 1],
                'pada': pada
            }
            
        except Exception as e:
            print(f"Lagna calculation error: {e}")
            return {
                'longitude': 0,
                'rasi_number': 1,
                'rasi_name': 'மேஷம்',
                'degrees_in_sign': 0,
                'nakshatra_number': 1,
                'nakshatra_name': 'அசுவினி',
                'pada': 1
            }
    
    def generate_rasi_chart(self, lagna_data: Dict, planetary_positions: Dict) -> Dict:
        """
        Generate Rasi chart (D1) based on Lagna position
        Traditional Tamil Nadu method
        """
        rasi_chart = {}
        lagna_rasi = lagna_data['rasi_number']
        
        # Initialize all 12 houses
        for house_num in range(1, 13):
            # Calculate which Rasi falls in this house
            rasi_in_house = ((lagna_rasi - 1 + house_num - 1) % 12) + 1
            rasi_name = self.tamil_rasi_names[rasi_in_house - 1]
            
            rasi_chart[f'House_{house_num}'] = {
                'rasi_number': rasi_in_house,
                'rasi_name': rasi_name,
                'planets': []
            }
        
        # Place planets in houses based on their Rasi
        for planet_name, planet_data in planetary_positions.items():
            planet_rasi = planet_data['rasi_number']
            
            # Find which house this Rasi falls in
            house_number = ((planet_rasi - lagna_rasi) % 12) + 1
            house_key = f'House_{house_number}'
            
            if house_key in rasi_chart:
                rasi_chart[house_key]['planets'].append(planet_data['tamil_name'])
        
        return rasi_chart
    
    def calculate_navamsa_rasi(self, longitude: float) -> Tuple[int, str]:
        """
        Calculate Navamsa Rasi using traditional Tamil Nadu method
        Each Rasi (30°) is divided into 9 parts of 3°20' each
        """
        # Get the main Rasi
        main_rasi = int(longitude / 30) + 1
        
        # Get position within the Rasi (0° to 30°)
        rasi_position = longitude % 30
        
        # Calculate Navamsa pada (1 to 9)
        navamsa_pada = int(rasi_position / (30/9)) + 1
        
        # Navamsa calculation based on Rasi type
        # Fire signs (1,5,9): Aries, Leo, Sagittarius - Forward count
        # Earth signs (2,6,10): Taurus, Virgo, Capricorn - Forward count  
        # Air signs (3,7,11): Gemini, Libra, Aquarius - Backward count
        # Water signs (4,8,12): Cancer, Scorpio, Pisces - Backward count
        
        fire_signs = [1, 5, 9]    # Aries, Leo, Sagittarius
        earth_signs = [2, 6, 10]  # Taurus, Virgo, Capricorn
        air_signs = [3, 7, 11]    # Gemini, Libra, Aquarius
        water_signs = [4, 8, 12]  # Cancer, Scorpio, Pisces
        
        if main_rasi in fire_signs:
            # Fire signs: Start from Aries (1) and count forward
            navamsa_rasi = ((navamsa_pada - 1) % 12) + 1
        elif main_rasi in earth_signs:
            # Earth signs: Start from Capricorn (10) and count forward
            navamsa_rasi = ((10 - 1 + navamsa_pada - 1) % 12) + 1
        elif main_rasi in air_signs:
            # Air signs: Start from Libra (7) and count forward
            navamsa_rasi = ((7 - 1 + navamsa_pada - 1) % 12) + 1
        else:  # water_signs
            # Water signs: Start from Cancer (4) and count forward
            navamsa_rasi = ((4 - 1 + navamsa_pada - 1) % 12) + 1
        
        navamsa_name = self.tamil_rasi_names[navamsa_rasi - 1]
        return navamsa_rasi, navamsa_name

    def generate_navamsa_chart(self, lagna_data: Dict, planetary_positions: Dict) -> Dict:
        """
        Generate Navamsa chart (D9) using traditional Tamil Nadu method
        """
        navamsa_chart = {}

        # Calculate Navamsa Lagna
        lagna_navamsa_rasi, _ = self.calculate_navamsa_rasi(lagna_data['longitude'])

        # Initialize all 12 houses for Navamsa chart
        for house_num in range(1, 13):
            # Calculate which Navamsa Rasi falls in this house
            rasi_in_house = ((lagna_navamsa_rasi - 1 + house_num - 1) % 12) + 1
            rasi_name = self.tamil_rasi_names[rasi_in_house - 1]

            navamsa_chart[f'House_{house_num}'] = {
                'rasi_number': rasi_in_house,
                'rasi_name': rasi_name,
                'planets': []
            }

        # Place planets in Navamsa houses
        for planet_name, planet_data in planetary_positions.items():
            planet_navamsa_rasi, _ = self.calculate_navamsa_rasi(planet_data['longitude'])

            # Find which house this Navamsa Rasi falls in
            house_number = ((planet_navamsa_rasi - lagna_navamsa_rasi) % 12) + 1
            house_key = f'House_{house_number}'

            if house_key in navamsa_chart:
                navamsa_chart[house_key]['planets'].append(planet_data['tamil_name'])

        return navamsa_chart

    def generate_complete_jathagam(self, name: str, birth_date: date, birth_time: time,
                                 birth_place: str, gender: str = 'Male') -> Dict:
        """
        Generate complete Tamil Nadu style Jathagam using Swiss Ephemeris
        """
        try:
            # Step 1: Get coordinates and timezone
            latitude, longitude, timezone_str = self.get_coordinates_and_timezone(birth_place)

            # Step 2: Calculate Julian Day
            julian_day = self.calculate_julian_day(birth_date, birth_time, timezone_str)

            # Step 3: Calculate planetary positions (sidereal)
            planetary_positions = self.calculate_planetary_positions(julian_day)

            # Step 4: Calculate Lagna (Ascendant)
            lagna_data = self.calculate_lagna(julian_day, latitude, longitude)

            # Step 5: Generate Rasi chart (D1)
            rasi_chart = self.generate_rasi_chart(lagna_data, planetary_positions)

            # Step 6: Generate Navamsa chart (D9)
            navamsa_chart = self.generate_navamsa_chart(lagna_data, planetary_positions)

            # Step 7: Get Moon's details for main Rasi and Nakshatra
            moon_data = planetary_positions.get('Moon', {})

            # Step 8: Compile complete Jathagam data
            jathagam_data = {
                'personal_details': {
                    'name': name,
                    'gender': gender,
                    'birth_date': birth_date.strftime('%Y-%m-%d'),
                    'birth_time': birth_time.strftime('%H:%M'),
                    'birth_place': birth_place,
                    'latitude': round(latitude, 6),
                    'longitude': round(longitude, 6),
                    'timezone': timezone_str,
                    'julian_day': round(julian_day, 6),
                    'ayanamsa': 'Lahiri',
                    'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                'astrological_details': {
                    'lagna_rasi': lagna_data['rasi_name'],
                    'lagna_nakshatra': lagna_data['nakshatra_name'],
                    'lagna_pada': lagna_data['pada'],
                    'lagna_degrees': round(lagna_data['degrees_in_sign'], 2),
                    'moon_raasi': moon_data.get('rasi_name', 'தெரியவில்லை'),
                    'moon_nakshatra': moon_data.get('nakshatra_name', 'தெரியவில்லை'),
                    'moon_pada': moon_data.get('pada', 1),
                    'moon_degrees': round(moon_data.get('degrees_in_sign', 0), 2)
                },
                'planetary_positions': planetary_positions,
                'lagna_details': lagna_data,
                'rasi_chart': rasi_chart,
                'navamsa_chart': navamsa_chart,
                'calculation_method': 'Swiss Ephemeris with Lahiri Ayanamsa (Tamil Nadu Traditional)',
                'software_info': 'Tamil Nadu Jathagam Generator - Following Kalaimagal/Kala Chakra methods'
            }

            return jathagam_data

        except Exception as e:
            print(f"Jathagam generation error: {e}")
            return None

    def get_planet_table_data(self, planetary_positions: Dict) -> List[Dict]:
        """
        Generate planet table data in traditional Tamil format
        """
        planet_table = []

        # Order planets as per traditional Tamil Jathagam
        planet_order = ['Sun', 'Moon', 'Mars', 'Mercury', 'Jupiter', 'Venus', 'Saturn', 'Rahu', 'Ketu']

        for planet_name in planet_order:
            if planet_name in planetary_positions:
                planet_data = planetary_positions[planet_name]
                planet_table.append({
                    'planet': planet_data['tamil_name'],
                    'degrees': f"{planet_data['degrees_in_sign']:.1f}°",
                    'rasi': planet_data['rasi_name'],
                    'nakshatra': planet_data['nakshatra_name'],
                    'pada': planet_data['pada']
                })

        return planet_table

    def format_for_display(self, jathagam_data: Dict) -> str:
        """
        Format Jathagam data for display in traditional Tamil style
        """
        if not jathagam_data:
            return "ஜாதக தகவல் கிடைக்கவில்லை"

        personal = jathagam_data.get('personal_details', {})
        astro = jathagam_data.get('astrological_details', {})

        formatted_text = f"""
🌟 {personal.get('name', 'தெரியவில்லை')} அவர்களின் தமிழ் ஜாதகம் 🌟

📋 அடிப்படை தகவல்கள்:
• பெயர்: {personal.get('name', 'தெரியவில்லை')}
• பாலினம்: {'ஆண்' if personal.get('gender') == 'Male' else 'பெண்'}
• பிறந்த தேதி: {personal.get('birth_date', 'தெரியவில்லை')}
• பிறந்த நேரம்: {personal.get('birth_time', 'தெரியவில்லை')}
• பிறந்த இடம்: {personal.get('birth_place', 'தெரியவில்லை')}

🌙 முக்கிய ஜோதிட தகவல்கள்:
• லக்ன ராசி: {astro.get('lagna_rasi', 'தெரியவில்லை')}
• லக்ன நட்சத்திரம்: {astro.get('lagna_nakshatra', 'தெரியவில்லை')}
• லக்ன பாதம்: {astro.get('lagna_pada', 'தெரியவில்லை')}
• சந்திர ராசி: {astro.get('moon_raasi', 'தெரியவில்லை')}
• சந்திர நட்சத்திரம்: {astro.get('moon_nakshatra', 'தெரியவில்லை')}
• சந்திர பாதம்: {astro.get('moon_pada', 'தெரியவில்லை')}

🔬 கணக்கீட்டு முறை:
• {jathagam_data.get('calculation_method', 'Swiss Ephemeris')}
• அயனாம்சம்: {personal.get('ayanamsa', 'Lahiri')}
• உருவாக்கப்பட்ட நேரம்: {personal.get('generated_at', 'தெரியவில்லை')}

📊 இந்த ஜாதகம் தமிழ்நாட்டு பாரம்பரிய ஜோதிட முறைப்படி உருவாக்கப்பட்டது.
"""

        return formatted_text

# Test function
if __name__ == "__main__":
    print("🌟 Testing Tamil Nadu Jathagam Generator...")
    print("Using Swiss Ephemeris with Lahiri Ayanamsa\n")

    # Test data
    generator = TamilNaduJathagamGenerator()

    test_name = "தமிழ் சோதனை"
    test_date = date(1990, 8, 15)
    test_time = time(10, 30)
    test_place = "சென்னை"
    test_gender = "Male"

    # Generate Jathagam
    jathagam_data = generator.generate_complete_jathagam(
        test_name, test_date, test_time, test_place, test_gender
    )

    if jathagam_data:
        print("✅ Tamil Nadu Jathagam generation successful!")

        # Display key information
        personal = jathagam_data.get('personal_details', {})
        astro = jathagam_data.get('astrological_details', {})

        print(f"   Name: {personal.get('name')}")
        print(f"   Lagna Rasi: {astro.get('lagna_rasi')}")
        print(f"   Moon Rasi: {astro.get('moon_raasi')}")
        print(f"   Moon Nakshatra: {astro.get('moon_nakshatra')}")

        # Check charts
        rasi_chart = jathagam_data.get('rasi_chart', {})
        navamsa_chart = jathagam_data.get('navamsa_chart', {})

        print(f"   Rasi Chart Houses: {len(rasi_chart)}")
        print(f"   Navamsa Chart Houses: {len(navamsa_chart)}")

        # Display formatted text
        formatted_text = generator.format_for_display(jathagam_data)
        print("\n" + "="*50)
        print(formatted_text)

    else:
        print("❌ Jathagam generation failed")

    print("\n🎉 Tamil Nadu Jathagam Generator test completed!")
