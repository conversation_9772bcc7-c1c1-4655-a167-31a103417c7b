#!/usr/bin/env python3
"""
Test the new Jathagam and upload features
"""
import os
import sys
import requests
import json
from datetime import date, time

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_jathagam_generation():
    """Test Jathagam generation functionality"""
    print("=== Testing Jathagam Generation ===")
    
    try:
        from jathagam_generator import generate_jathagam, format_jathagam_for_display
        
        # Test data
        test_name = "ராம்"
        test_date = date(1995, 5, 15)
        test_time = time(14, 30)
        test_place = "சென்னை"
        
        # Generate Jathagam
        jathagam = generate_jathagam(test_name, test_date, test_time, test_place)
        
        if jathagam:
            print("✅ Jathagam generated successfully!")
            print(f"Name: {jathagam['name']}")
            print(f"Moon Raasi: {jathagam['moon_raasi']}")
            print(f"Moon Nakshatra: {jathagam['moon_nakshatra']}")
            print(f"Lagna Raasi: {jathagam['lagna_raasi']}")
            
            # Test formatting
            formatted = format_jathagam_for_display(jathagam)
            print("✅ Jathagam formatting working!")
            
            return True
        else:
            print("❌ Jathagam generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Jathagam test error: {e}")
        return False

def test_admin_upload_with_jathagam():
    """Test admin upload with Jathagam generation"""
    print("\n=== Testing Admin Upload with Jathagam ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Create a test file
        test_file_content = "Test document content for Jathagam testing"
        
        # Prepare upload data
        files = {
            'file': ('test_jathagam.txt', test_file_content, 'text/plain')
        }
        
        data = {
            'name': 'ஜாதக சோதனை',
            'city': 'சென்னை',
            'natchathiram': 'ரோகிணி',
            'raasi': 'ரிஷபம்',
            'vayathu': '28',
            'birth_date': '1995-05-15',
            'birth_time': '14:30',
            'birth_place': 'சென்னை',
            'generate_jathagam': 'true'
        }
        
        response = session.post('http://localhost:5000/admin_upload', files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Admin upload with Jathagam successful!")
                print(f"Jathagam generated: {result.get('jathagam_generated', False)}")
                return True
            else:
                print(f"❌ Upload failed: {result.get('message')}")
                return False
        else:
            print(f"❌ Upload request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Upload test error: {e}")
        return False

def test_database_schema():
    """Test new database fields"""
    print("\n=== Testing Database Schema ===")
    
    try:
        from app import app
        from database import db, Document
        
        with app.app_context():
            # Check if new fields exist
            docs = Document.query.all()
            print(f"Documents in database: {len(docs)}")
            
            # Test accessing new fields
            for doc in docs:
                print(f"Document: {doc.filename}")
                print(f"  Birth Date: {doc.birth_date}")
                print(f"  Birth Time: {doc.birth_time}")
                print(f"  Birth Place: {doc.birth_place}")
                print(f"  Has Jathagam: {doc.has_jathagam}")
                break  # Just check first document
            
            print("✅ Database schema working!")
            return True
            
    except Exception as e:
        print(f"❌ Database test error: {e}")
        return False

def test_jathagam_view():
    """Test Jathagam viewing functionality"""
    print("\n=== Testing Jathagam View ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Try to view Jathagam for document ID 1 (if exists)
        response = session.get('http://localhost:5000/view_jathagam/1')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Jathagam view successful!")
                print("Jathagam data retrieved successfully")
                return True
            else:
                print(f"ℹ️ No Jathagam for document 1: {result.get('message')}")
                return True  # This is expected if no Jathagam exists
        else:
            print(f"❌ Jathagam view failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Jathagam view test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Jathagam and Upload Features...")
    
    tests = [
        test_jathagam_generation,
        test_database_schema,
        test_admin_upload_with_jathagam,
        test_jathagam_view
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All Jathagam features are working correctly!")
        print("\n🌟 Features Ready:")
        print("  ✅ Jathagam Generation from birth details")
        print("  ✅ Admin upload with astrological fields")
        print("  ✅ Database schema with birth details")
        print("  ✅ Jathagam viewing functionality")
        print("  ✅ Tamil language support throughout")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
