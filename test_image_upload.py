#!/usr/bin/env python3
"""
Test image upload functionality
"""
import os
import sys
from werkzeug.datastructures import FileStorage
from io import BytesIO
from PIL import Image

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, extract_user_data_from_file
from database import db, User, Document

def test_image_upload():
    """Test uploading an image file"""
    with app.app_context():
        print("=== Testing Image Upload ===")
        
        # Count before
        docs_before = Document.query.count()
        print(f"Documents before upload: {docs_before}")
        
        # Create a simple test image
        img = Image.new('RGB', (100, 100), color='red')
        img_buffer = BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # Test extract_user_data_from_file with image
        print("Testing extract_user_data_from_file with image...")
        try:
            # Save the image temporarily to test extraction
            test_img_path = "test_image.png"
            with open(test_img_path, 'wb') as f:
                f.write(img_buffer.getvalue())
            
            extracted_users = extract_user_data_from_file(test_img_path)
            print(f"Extracted users: {extracted_users}")
            
            # Clean up
            os.remove(test_img_path)
            
        except Exception as e:
            print(f"❌ Extract function failed: {e}")
            if os.path.exists(test_img_path):
                os.remove(test_img_path)
        
        # Test actual upload
        img_buffer.seek(0)
        file_storage = FileStorage(
            stream=img_buffer,
            filename='test_image.png',
            content_type='image/png'
        )
        
        print("Testing actual upload route...")
        with app.test_client() as client:
            response = client.post('/upload', 
                                 data={'file': file_storage},
                                 content_type='multipart/form-data')
            
            print(f"Upload response: {response.status_code}")
        
        # Count after
        docs_after = Document.query.count()
        print(f"Documents after upload: {docs_after}")
        print(f"Documents added: {docs_after - docs_before}")
        
        # Check if any image documents exist
        image_docs = Document.query.filter(Document.file_type.in_(['.png', '.jpg', '.jpeg'])).all()
        print(f"Image documents in database: {len(image_docs)}")
        for doc in image_docs:
            exists = os.path.exists(doc.file_path)
            print(f"  - {doc.filename} (Exists: {exists})")

if __name__ == "__main__":
    test_image_upload()
