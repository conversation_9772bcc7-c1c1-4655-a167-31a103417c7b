<!DOCTYPE html>
<html lang="ta">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ t.admin }} {{ t.dashboard }} - ஆவண மேலாண்மை அமைப்பு</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }

        .navbar {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .welcome-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #dc3545;
        }

        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }

        .actions-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .action-card {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }

        .action-card:hover {
            border-color: #dc3545;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .action-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .action-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .action-description {
            color: #666;
            font-size: 0.9rem;
        }

        .documents-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .documents-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .documents-table th,
        .documents-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .documents-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background-color 0.3s;
            margin-right: 0.5rem;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .scanner-status {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }

        .status-connected {
            color: #28a745;
        }

        .status-disconnected {
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                flex-direction: column;
                gap: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .documents-table {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                🔧 {{ t.admin }} ஆவண மேலாண்மை
            </div>
            <div class="nav-menu">
                <div class="user-info">
                    <span>{{ t.welcome }}, {{ current_user.full_name }}! ({{ t.admin }})</span>
                    <a href="{{ url_for('logout') }}" class="nav-link">{{ t.logout }}</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1 class="welcome-title">{{ t.admin }} {{ t.dashboard }}</h1>
            <p class="welcome-subtitle">ஆவணங்களை ஸ்கேன், பதிவேற்று மற்றும் நிர்வகிக்கவும்</p>
        </div>

        <!-- Scanner Status -->
        <div class="scanner-status">
            <h3 class="section-title">🖨️ ஸ்கேனர் நிலை</h3>
            <div id="scanner-status" class="status-indicator">
                <span>சரிபார்க்கிறது...</span>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ documents|length }}</div>
                <div class="stat-label">மொத்த ஆவணங்கள்</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ users|length }}</div>
                <div class="stat-label">ஆவண பயனர்கள்</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ auth_users|length }}</div>
                <div class="stat-label">அங்கீகரிக்கப்பட்ட பயனர்கள்</div>
            </div>
        </div>

        <!-- Admin Actions -->
        <div class="actions-section">
            <h2 class="section-title">⚡ {{ t.admin }} செயல்கள்</h2>
            <div class="actions-grid">
                <div class="action-card" onclick="startScan()">
                    <div class="action-icon">📷</div>
                    <div class="action-title">ஆவணம் ஸ்கேன் செய்</div>
                    <div class="action-description">ஸ்கேனர் பயன்படுத்தி புதிய ஆவணம் ஸ்கேன் செய்யவும்</div>
                </div>
                
                <div class="action-card" onclick="showUploadModal()">
                    <div class="action-icon">📤</div>
                    <div class="action-title">கோப்பு பதிவேற்று</div>
                    <div class="action-description">கணினியில் இருந்து ஆவணம் பதிவேற்றவும்</div>
                </div>
                
                <div class="action-card" onclick="location.href='/clear'">
                    <div class="action-icon">🗑️</div>
                    <div class="action-title">அனைத்தையும் அழி</div>
                    <div class="action-description">அனைத்து ஆவணங்களையும் பயனர்களையும் அழிக்கவும்</div>
                </div>
            </div>
        </div>

        <!-- Documents Management -->
        <div class="documents-section">
            <h2 class="section-title">📄 ஆவண மேலாண்மை</h2>
            
            {% if documents %}
                <table class="documents-table">
                    <thead>
                        <tr>
                            <th>கோப்பு பெயர்</th>
                            <th>வகை</th>
                            <th>பயனர்</th>
                            <th>தேதி</th>
                            <th>செயல்கள்</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for doc in documents %}
                            <tr>
                                <td>{{ doc.filename }}</td>
                                <td>
                                    {% if doc.document_type == 'scanned' %}
                                        📷 ஸ்கேன்
                                    {% else %}
                                        📤 பதிவேற்றம்
                                    {% endif %}
                                </td>
                                <td>
                                    {% if doc.user %}
                                        {{ doc.user.name }} ({{ doc.user.city }})
                                    {% else %}
                                        தெரியவில்லை
                                    {% endif %}
                                </td>
                                <td>{{ doc.created_at }}</td>
                                <td>
                                    {% if doc.document_type == 'scanned' %}
                                        <a href="{{ url_for('serve_scanned_file', filename=doc.filename) }}" 
                                           class="btn btn-primary" target="_blank">{{ t.view }}</a>
                                    {% else %}
                                        <a href="{{ url_for('serve_uploaded_file', filename=doc.filename) }}" 
                                           class="btn btn-primary" target="_blank">{{ t.view }}</a>
                                    {% endif %}
                                    <button class="btn btn-danger" onclick="deleteDocument({{ doc.id }}, '{{ doc.filename }}')">{{ t.delete }}</button>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p style="text-align: center; color: #666; padding: 2rem;">
                    இன்னும் ஆவணங்கள் எதுவும் இல்லை.
                </p>
            {% endif %}
        </div>
    </div>

    <script>
        // Check scanner status on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkScannerStatus();
        });

        function checkScannerStatus() {
            fetch('/check_scanner')
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('scanner-status');
                    if (data.connected) {
                        statusElement.innerHTML = '<span class="status-connected">✅ ' + data.message + '</span>';
                    } else {
                        statusElement.innerHTML = '<span class="status-disconnected">❌ ' + data.message + '</span>';
                    }
                })
                .catch(error => {
                    console.error('Error checking scanner:', error);
                    document.getElementById('scanner-status').innerHTML = '<span class="status-disconnected">❌ ஸ்கேனர் சரிபார்ப்பு பிழை</span>';
                });
        }

        function startScan() {
            // Check scanner status first
            fetch('/check_scanner')
                .then(response => response.json())
                .then(data => {
                    if (!data.connected) {
                        alert('❌ ஸ்கேனர் இணைக்கப்படவில்லை. முதலில் ஸ்கேனரை இணைக்கவும்.');
                        return;
                    }

                    // Show scanning modal
                    showScanModal();
                })
                .catch(error => {
                    console.error('Scanner check error:', error);
                    alert('❌ ஸ்கேனர் சரிபார்ப்பு பிழை');
                });
        }

        function showScanModal() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 400px; width: 90%; text-align: center;">
                        <h3 style="margin-bottom: 1rem;">📷 ஆவணம் ஸ்கேன் செய்</h3>
                        <div id="scan-status" style="margin: 1rem 0; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
                            ஸ்கேன் செய்ய தயாராகிறது...
                        </div>
                        <div style="margin-top: 1rem;">
                            <button onclick="performScan()" style="background: #28a745; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">ஸ்கேன் தொடங்கு</button>
                            <button onclick="closeScanModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">ரத்து செய்</button>
                        </div>
                    </div>
                </div>
            `;
            modal.id = 'scan-modal';
            document.body.appendChild(modal);
        }

        function performScan() {
            const statusDiv = document.getElementById('scan-status');
            statusDiv.innerHTML = '📷 ஸ்கேன் செய்கிறது... தயவுசெய்து காத்திருக்கவும்';

            fetch('/scan_document', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusDiv.innerHTML = '✅ ஸ்கேன் வெற்றிகரமாக முடிந்தது!';
                    setTimeout(() => {
                        closeScanModal();
                        showUserInfoModal(data.filename);
                    }, 1500);
                } else {
                    statusDiv.innerHTML = '❌ ஸ்கேன் பிழை: ' + data.message;
                }
            })
            .catch(error => {
                console.error('Scan error:', error);
                statusDiv.innerHTML = '❌ ஸ்கேன் பிழை ஏற்பட்டது';
            });
        }

        function showUserInfoModal(filename) {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 400px; width: 90%;">
                        <h3 style="margin-bottom: 1rem;">👤 பயனர் தகவல்</h3>
                        <form onsubmit="saveScannedDocument(event, '${filename}')">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பெயர்:</label>
                                <input type="text" id="user-name" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">நகரம்:</label>
                                <input type="text" id="user-city" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                            <div style="text-align: center; margin-top: 1.5rem;">
                                <button type="submit" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">சேமி</button>
                                <button type="button" onclick="closeUserInfoModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">ரத்து செய்</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            modal.id = 'user-info-modal';
            document.body.appendChild(modal);
        }

        function saveScannedDocument(event, filename) {
            event.preventDefault();
            const name = document.getElementById('user-name').value;
            const city = document.getElementById('user-city').value;

            const formData = new FormData();
            formData.append('name', name);
            formData.append('city', city);

            fetch(`/process_scanned/${filename}`, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    closeUserInfoModal();
                    alert('✅ ஆவணம் வெற்றிகரமாக சேமிக்கப்பட்டது!');
                    location.reload(); // Refresh to show new document
                } else {
                    alert('❌ ஆவணம் சேமிப்பதில் பிழை');
                }
            })
            .catch(error => {
                console.error('Save error:', error);
                alert('❌ சேமிப்பு பிழை');
            });
        }

        function closeScanModal() {
            const modal = document.getElementById('scan-modal');
            if (modal) {
                modal.remove();
            }
        }

        function closeUserInfoModal() {
            const modal = document.getElementById('user-info-modal');
            if (modal) {
                modal.remove();
            }
        }

        function showUploadModal() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 400px; width: 90%;">
                        <h3 style="margin-bottom: 1rem;">📤 கோப்பு பதிவேற்று</h3>
                        <form onsubmit="uploadFile(event)">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">கோப்பு தேர்வு:</label>
                                <input type="file" id="upload-file" accept=".pdf,.png,.jpg,.jpeg,.txt,.csv" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பெயர்:</label>
                                <input type="text" id="upload-user-name" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">நகரம்:</label>
                                <input type="text" id="upload-user-city" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                            <div style="text-align: center; margin-top: 1.5rem;">
                                <button type="submit" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">பதிவேற்று</button>
                                <button type="button" onclick="closeUploadModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">ரத்து செய்</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            modal.id = 'upload-modal';
            document.body.appendChild(modal);
        }

        function uploadFile(event) {
            event.preventDefault();
            const fileInput = document.getElementById('upload-file');
            const name = document.getElementById('upload-user-name').value;
            const city = document.getElementById('upload-user-city').value;

            if (!fileInput.files[0]) {
                alert('கோப்பு தேர்வு செய்யவும்');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('name', name);
            formData.append('city', city);

            fetch('/admin_upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeUploadModal();
                    alert('✅ கோப்பு வெற்றிகரமாக பதிவேற்றப்பட்டது!');
                    location.reload();
                } else {
                    alert('❌ பதிவேற்று பிழை: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                alert('❌ பதிவேற்று பிழை');
            });
        }

        function closeUploadModal() {
            const modal = document.getElementById('upload-modal');
            if (modal) {
                modal.remove();
            }
        }

        function deleteDocument(docId, filename) {
            if (confirm('நீங்கள் "' + filename + '" ஆவணத்தை நீக்க விரும்புகிறீர்களா?')) {
                fetch(`/delete_document/${docId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ஆவணம் வெற்றிகரமாக நீக்கப்பட்டது!');
                        location.reload();
                    } else {
                        alert('❌ நீக்கு பிழை: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    alert('❌ நீக்கு பிழை');
                });
            }
        }
    </script>
</body>
</html>
