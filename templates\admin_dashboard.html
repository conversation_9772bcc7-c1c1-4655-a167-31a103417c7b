<!DOCTYPE html>
<html lang="ta">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ t.admin }} {{ t.dashboard }} - ஆவண மேலாண்மை அமைப்பு</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.6;
        }

        .navbar {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .welcome-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #dc3545;
        }

        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }

        .actions-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .action-card {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }

        .action-card:hover {
            border-color: #dc3545;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .action-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .action-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .action-description {
            color: #666;
            font-size: 0.9rem;
        }

        .documents-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .documents-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .documents-table th,
        .documents-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .documents-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background-color 0.3s;
            margin-right: 0.5rem;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .scanner-status {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }

        .status-connected {
            color: #28a745;
        }

        .status-disconnected {
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-menu {
                flex-direction: column;
                gap: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .documents-table {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                🔧 {{ t.admin }} ஆவண மேலாண்மை
            </div>
            <div class="nav-menu">
                <div class="user-info">
                    <span>{{ t.welcome }}, {{ current_user.full_name }}! ({{ t.admin }})</span>
                    <a href="{{ url_for('logout') }}" class="nav-link">{{ t.logout }}</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1 class="welcome-title">{{ t.admin }} {{ t.dashboard }}</h1>
            <p class="welcome-subtitle">ஆவணங்களை ஸ்கேன், பதிவேற்று மற்றும் நிர்வகிக்கவும்</p>
        </div>

        <!-- Scanner Status -->
        <div class="scanner-status">
            <h3 class="section-title">🖨️ ஸ்கேனர் நிலை</h3>
            <div id="scanner-status" class="status-indicator">
                <span>சரிபார்க்கிறது...</span>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ documents|length }}</div>
                <div class="stat-label">மொத்த ஆவணங்கள்</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ users|length }}</div>
                <div class="stat-label">ஆவண பயனர்கள்</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ auth_users|length }}</div>
                <div class="stat-label">அங்கீகரிக்கப்பட்ட பயனர்கள்</div>
            </div>
        </div>

        <!-- Admin Actions -->
        <div class="actions-section">
            <h2 class="section-title">⚡ {{ t.admin }} செயல்கள்</h2>
            <div class="actions-grid">
                <div class="action-card" onclick="startScan()">
                    <div class="action-icon">📷</div>
                    <div class="action-title">ஆவணம் ஸ்கேன் செய்</div>
                    <div class="action-description">ஸ்கேனர் பயன்படுத்தி புதிய ஆவணம் ஸ்கேன் செய்யவும்</div>
                </div>
                
                <div class="action-card" onclick="showUploadModal()">
                    <div class="action-icon">📤</div>
                    <div class="action-title">கோப்பு பதிவேற்று</div>
                    <div class="action-description">கணினியில் இருந்து ஆவணம் பதிவேற்றவும்</div>
                </div>
                
                <div class="action-card" onclick="showJathagamGenerationModal()">
                    <div class="action-icon">🌟</div>
                    <div class="action-title">ஜாதகம் உருவாக்கு</div>
                    <div class="action-description">பெயர், பிறந்த தேதி மற்றும் நேரத்தின் அடிப்படையில் ஜாதகம் உருவாக்கவும்</div>
                </div>

                <div class="action-card" onclick="showJathagamScannerModal()">
                    <div class="action-icon">📷</div>
                    <div class="action-title">ஜாதகம் ஸ்கேன் செய்</div>
                    <div class="action-description">உங்கள் உள்ளூர் ஸ்கேனர்/பிரிண்டரில் இருந்து ஜாதக ஆவணத்தை ஸ்கேன் செய்யவும்</div>
                </div>

                <div class="action-card" onclick="showJathagamListModal()">
                    <div class="action-icon">📋</div>
                    <div class="action-title">ஜாதக பட்டியல்</div>
                    <div class="action-description">உருவாக்கப்பட்ட அனைத்து ஜாதகங்களையும் பார்க்கவும்</div>
                </div>

                <div class="action-card" onclick="location.href='/clear'">
                    <div class="action-icon">🗑️</div>
                    <div class="action-title">அனைத்தையும் அழி</div>
                    <div class="action-description">அனைத்து ஆவணங்களையும் பயனர்களையும் அழிக்கவும்</div>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="actions-section">
            <h2 class="section-title">🔍 ஆவண தேடல்</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                <input type="text" id="search-name" placeholder="பெயர்" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                <input type="text" id="search-city" placeholder="நகரம்" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                <select id="search-natchathiram" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                    <option value="">நட்சத்திரம்</option>
                    <option value="அசுவினி">அசுவினி</option>
                    <option value="பரணி">பரணி</option>
                    <option value="கார்த்திகை">கார்த்திகை</option>
                    <option value="ரோகிணி">ரோகிணி</option>
                    <option value="மிருகசீரிடம்">மிருகசீரிடம்</option>
                    <option value="திருவாதிரை">திருவாதிரை</option>
                    <option value="புனர்பூசம்">புனர்பூசம்</option>
                    <option value="பூசம்">பூசம்</option>
                    <option value="ஆயில்யம்">ஆயில்யம்</option>
                    <option value="மகம்">மகம்</option>
                    <option value="பூரம்">பூரம்</option>
                    <option value="உத்திரம்">உத்திரம்</option>
                    <option value="அஸ்தம்">அஸ்தம்</option>
                    <option value="சித்திரை">சித்திரை</option>
                    <option value="சுவாதி">சுவாதி</option>
                    <option value="விசாகம்">விசாகம்</option>
                    <option value="அனுஷம்">அனுஷம்</option>
                    <option value="கேட்டை">கேட்டை</option>
                    <option value="மூலம்">மூலம்</option>
                    <option value="பூராடம்">பூராடம்</option>
                    <option value="உத்திராடம்">உத்திராடம்</option>
                    <option value="திருவோணம்">திருவோணம்</option>
                    <option value="அவிட்டம்">அவிட்டம்</option>
                    <option value="சதயம்">சதயம்</option>
                    <option value="பூரட்டாதி">பூரட்டாதி</option>
                    <option value="உத்திரட்டாதி">உத்திரட்டாதி</option>
                    <option value="ரேவதி">ரேவதி</option>
                </select>
                <select id="search-raasi" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                    <option value="">ராசி</option>
                    <option value="மேஷம்">மேஷம்</option>
                    <option value="ரிஷபம்">ரிஷபம்</option>
                    <option value="மிதுனம்">மிதுனம்</option>
                    <option value="கடகம்">கடகம்</option>
                    <option value="சிம்மம்">சிம்மம்</option>
                    <option value="கன்னி">கன்னி</option>
                    <option value="துலாம்">துலாம்</option>
                    <option value="விருச்சிகம்">விருச்சிகம்</option>
                    <option value="தனுசு">தனுசு</option>
                    <option value="மகரம்">மகரம்</option>
                    <option value="கும்பம்">கும்பம்</option>
                    <option value="மீனம்">மீனம்</option>
                </select>
                <input type="number" id="search-vayathu-min" placeholder="குறைந்த வயது" min="1" max="120" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                <input type="number" id="search-vayathu-max" placeholder="அதிக வயது" min="1" max="120" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
            </div>
            <div style="text-align: center; margin-bottom: 1rem;">
                <button onclick="performSearch()" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">🔍 தேடு</button>
                <button onclick="clearSearch()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">🗑️ அழி</button>
            </div>
        </div>

        <!-- Documents Management -->
        <div class="documents-section">
            <h2 class="section-title">📄 ஆவண மேலாண்மை</h2>
            
            {% if documents %}
                <table class="documents-table" id="documents-table">
                    <thead>
                        <tr>
                            <th>கோப்பு பெயர்</th>
                            <th>வகை</th>
                            <th>பயனர்</th>
                            <th>நட்சத்திரம்</th>
                            <th>ராசி</th>
                            <th>வயது</th>
                            <th>ஜாதகம்</th>
                            <th>தேதி</th>
                            <th>செயல்கள்</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for doc in documents %}
                            <tr>
                                <td>{{ doc.filename }}</td>
                                <td>
                                    {% if doc.document_type == 'scanned' %}
                                        📷 ஸ்கேன்
                                    {% else %}
                                        📤 பதிவேற்றம்
                                    {% endif %}
                                </td>
                                <td>
                                    {% if doc.user %}
                                        {{ doc.user.name }}<br><small>({{ doc.user.city }})</small>
                                    {% else %}
                                        தெரியவில்லை
                                    {% endif %}
                                </td>
                                <td>{{ doc.natchathiram or '-' }}</td>
                                <td>{{ doc.raasi or '-' }}</td>
                                <td>{{ doc.vayathu or '-' }}</td>
                                <td>
                                    {% if doc.has_jathagam %}
                                        <span style="color: #28a745;">✅ உள்ளது</span>
                                    {% else %}
                                        <span style="color: #6c757d;">❌ இல்லை</span>
                                    {% endif %}
                                </td>
                                <td>{{ doc.created_at }}</td>
                                <td>
                                    {% if doc.document_type == 'scanned' %}
                                        <a href="{{ url_for('serve_scanned_file', filename=doc.filename) }}"
                                           class="btn btn-primary" target="_blank">{{ t.view }}</a>
                                    {% else %}
                                        <a href="{{ url_for('serve_uploaded_file', filename=doc.filename) }}"
                                           class="btn btn-primary" target="_blank">{{ t.view }}</a>
                                    {% endif %}
                                    {% if doc.has_jathagam %}
                                        <button class="btn btn-success" onclick="viewJathagam({{ doc.id }})">🌟 ஜாதகம்</button>
                                    {% endif %}
                                    <button class="btn btn-danger" onclick="deleteDocument({{ doc.id }}, '{{ doc.filename }}')">{{ t.delete }}</button>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p style="text-align: center; color: #666; padding: 2rem;">
                    இன்னும் ஆவணங்கள் எதுவும் இல்லை.
                </p>
            {% endif %}
        </div>
    </div>

    <script>
        // Check scanner status on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkScannerStatus();
        });

        function checkScannerStatus() {
            fetch('/check_scanner')
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('scanner-status');
                    if (data.connected) {
                        statusElement.innerHTML = '<span class="status-connected">✅ ' + data.message + '</span>';
                    } else {
                        statusElement.innerHTML = '<span class="status-disconnected">❌ ' + data.message + '</span>';
                    }
                })
                .catch(error => {
                    console.error('Error checking scanner:', error);
                    document.getElementById('scanner-status').innerHTML = '<span class="status-disconnected">❌ ஸ்கேனர் சரிபார்ப்பு பிழை</span>';
                });
        }

        function startScan() {
            // Check scanner status first
            fetch('/check_scanner')
                .then(response => response.json())
                .then(data => {
                    if (!data.connected) {
                        alert('❌ ஸ்கேனர் இணைக்கப்படவில்லை. முதலில் ஸ்கேனரை இணைக்கவும்.');
                        return;
                    }

                    // Show scanning modal
                    showScanModal();
                })
                .catch(error => {
                    console.error('Scanner check error:', error);
                    alert('❌ ஸ்கேனர் சரிபார்ப்பு பிழை');
                });
        }

        function showScanModal() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 400px; width: 90%; text-align: center;">
                        <h3 style="margin-bottom: 1rem;">📷 ஆவணம் ஸ்கேன் செய்</h3>
                        <div id="scan-status" style="margin: 1rem 0; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
                            ஸ்கேன் செய்ய தயாராகிறது...
                        </div>
                        <div style="margin-top: 1rem;">
                            <button onclick="performScan()" style="background: #28a745; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">ஸ்கேன் தொடங்கு</button>
                            <button onclick="closeScanModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">ரத்து செய்</button>
                        </div>
                    </div>
                </div>
            `;
            modal.id = 'scan-modal';
            document.body.appendChild(modal);
        }

        function performScan() {
            const statusDiv = document.getElementById('scan-status');
            statusDiv.innerHTML = '📷 ஸ்கேன் செய்கிறது... தயவுசெய்து காத்திருக்கவும்';

            fetch('/scan_document', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusDiv.innerHTML = '✅ ஸ்கேன் வெற்றிகரமாக முடிந்தது!';
                    setTimeout(() => {
                        closeScanModal();
                        showUserInfoModal(data.filename);
                    }, 1500);
                } else {
                    statusDiv.innerHTML = '❌ ஸ்கேன் பிழை: ' + data.message;
                }
            })
            .catch(error => {
                console.error('Scan error:', error);
                statusDiv.innerHTML = '❌ ஸ்கேன் பிழை ஏற்பட்டது';
            });
        }

        function showUserInfoModal(filename) {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 500px; width: 90%; margin: 2rem;">
                        <h3 style="margin-bottom: 1rem;">👤 பயனர் மற்றும் ஜோதிட தகவல்</h3>
                        <form onsubmit="saveScannedDocument(event, '${filename}')">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பெயர்:</label>
                                    <input type="text" id="user-name" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">நகரம்:</label>
                                    <input type="text" id="user-city" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">நட்சத்திரம்:</label>
                                    <select id="user-natchathiram" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                        <option value="">தேர்வு செய்யவும்</option>
                                        <option value="அசுவினி">அசுவினி</option>
                                        <option value="பரணி">பரணி</option>
                                        <option value="கார்த்திகை">கார்த்திகை</option>
                                        <option value="ரோகிணி">ரோகிணி</option>
                                        <option value="மிருகசீரிடம்">மிருகசீரிடம்</option>
                                        <option value="திருவாதிரை">திருவாதிரை</option>
                                        <option value="புனர்பூசம்">புனர்பூசம்</option>
                                        <option value="பூசம்">பூசம்</option>
                                        <option value="ஆயில்யம்">ஆயில்யம்</option>
                                        <option value="மகம்">மகம்</option>
                                        <option value="பூரம்">பூரம்</option>
                                        <option value="உத்திரம்">உத்திரம்</option>
                                        <option value="அஸ்தம்">அஸ்தம்</option>
                                        <option value="சித்திரை">சித்திரை</option>
                                        <option value="சுவாதி">சுவாதி</option>
                                        <option value="விசாகம்">விசாகம்</option>
                                        <option value="அனுஷம்">அனுஷம்</option>
                                        <option value="கேட்டை">கேட்டை</option>
                                        <option value="மூலம்">மூலம்</option>
                                        <option value="பூராடம்">பூராடம்</option>
                                        <option value="உத்திராடம்">உத்திராடம்</option>
                                        <option value="திருவோணம்">திருவோணம்</option>
                                        <option value="அவிட்டம்">அவிட்டம்</option>
                                        <option value="சதயம்">சதயம்</option>
                                        <option value="பூரட்டாதி">பூரட்டாதி</option>
                                        <option value="உத்திரட்டாதி">உத்திரட்டாதி</option>
                                        <option value="ரேவதி">ரேவதி</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">ராசி:</label>
                                    <select id="user-raasi" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                        <option value="">தேர்வு செய்யவும்</option>
                                        <option value="மேஷம்">மேஷம்</option>
                                        <option value="ரிஷபம்">ரிஷபம்</option>
                                        <option value="மிதுனம்">மிதுனம்</option>
                                        <option value="கடகம்">கடகம்</option>
                                        <option value="சிம்மம்">சிம்மம்</option>
                                        <option value="கன்னி">கன்னி</option>
                                        <option value="துலாம்">துலாம்</option>
                                        <option value="விருச்சிகம்">விருச்சிகம்</option>
                                        <option value="தனுசு">தனுசு</option>
                                        <option value="மகரம்">மகரம்</option>
                                        <option value="கும்பம்">கும்பம்</option>
                                        <option value="மீனம்">மீனம்</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">வயது:</label>
                                    <input type="number" id="user-vayathu" min="1" max="120" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                            </div>

                            <div style="text-align: center; margin-top: 1.5rem;">
                                <button type="submit" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">சேமி</button>
                                <button type="button" onclick="closeUserInfoModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">ரத்து செய்</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            modal.id = 'user-info-modal';
            document.body.appendChild(modal);
        }

        function saveScannedDocument(event, filename) {
            event.preventDefault();
            const name = document.getElementById('user-name').value;
            const city = document.getElementById('user-city').value;
            const natchathiram = document.getElementById('user-natchathiram').value;
            const raasi = document.getElementById('user-raasi').value;
            const vayathu = document.getElementById('user-vayathu').value;

            const formData = new FormData();
            formData.append('name', name);
            formData.append('city', city);
            formData.append('natchathiram', natchathiram);
            formData.append('raasi', raasi);
            formData.append('vayathu', vayathu);

            fetch(`/process_scanned/${filename}`, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    closeUserInfoModal();
                    alert('✅ ஆவணம் வெற்றிகரமாக சேமிக்கப்பட்டது!');
                    location.reload(); // Refresh to show new document
                } else {
                    alert('❌ ஆவணம் சேமிப்பதில் பிழை');
                }
            })
            .catch(error => {
                console.error('Save error:', error);
                alert('❌ சேமிப்பு பிழை');
            });
        }

        function closeScanModal() {
            const modal = document.getElementById('scan-modal');
            if (modal) {
                modal.remove();
            }
        }

        function closeUserInfoModal() {
            const modal = document.getElementById('user-info-modal');
            if (modal) {
                modal.remove();
            }
        }

        function showUploadModal() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 600px; width: 90%; margin: 2rem;">
                        <h3 style="margin-bottom: 1rem;">📤 கோப்பு பதிவேற்று</h3>
                        <form onsubmit="uploadFile(event)">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">கோப்பு தேர்வு:</label>
                                <input type="file" id="upload-file" accept=".pdf,.png,.jpg,.jpeg,.txt,.csv" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பெயர்:</label>
                                    <input type="text" id="upload-user-name" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">நகரம்:</label>
                                    <input type="text" id="upload-user-city" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">நட்சத்திரம்:</label>
                                    <select id="upload-natchathiram" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                        <option value="">தேர்வு செய்யவும்</option>
                                        <option value="அசுவினி">அசுவினி</option>
                                        <option value="பரணி">பரணி</option>
                                        <option value="கார்த்திகை">கார்த்திகை</option>
                                        <option value="ரோகிணி">ரோகிணி</option>
                                        <option value="மிருகசீரிடம்">மிருகசீரிடம்</option>
                                        <option value="திருவாதிரை">திருவாதிரை</option>
                                        <option value="புனர்பூசம்">புனர்பூசம்</option>
                                        <option value="பூசம்">பூசம்</option>
                                        <option value="ஆயில்யம்">ஆயில்யம்</option>
                                        <option value="மகம்">மகம்</option>
                                        <option value="பூரம்">பூரம்</option>
                                        <option value="உத்திரம்">உத்திரம்</option>
                                        <option value="அஸ்தம்">அஸ்தம்</option>
                                        <option value="சித்திரை">சித்திரை</option>
                                        <option value="சுவாதி">சுவாதி</option>
                                        <option value="விசாகம்">விசாகம்</option>
                                        <option value="அனுஷம்">அனுஷம்</option>
                                        <option value="கேட்டை">கேட்டை</option>
                                        <option value="மூலம்">மூலம்</option>
                                        <option value="பூராடம்">பூராடம்</option>
                                        <option value="உத்திராடம்">உத்திராடம்</option>
                                        <option value="திருவோணம்">திருவோணம்</option>
                                        <option value="அவிட்டம்">அவிட்டம்</option>
                                        <option value="சதயம்">சதயம்</option>
                                        <option value="பூரட்டாதி">பூரட்டாதி</option>
                                        <option value="உத்திரட்டாதி">உத்திரட்டாதி</option>
                                        <option value="ரேவதி">ரேவதி</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">ராசி:</label>
                                    <select id="upload-raasi" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                        <option value="">தேர்வு செய்யவும்</option>
                                        <option value="மேஷம்">மேஷம்</option>
                                        <option value="ரிஷபம்">ரிஷபம்</option>
                                        <option value="மிதுனம்">மிதுனம்</option>
                                        <option value="கடகம்">கடகம்</option>
                                        <option value="சிம்மம்">சிம்மம்</option>
                                        <option value="கன்னி">கன்னி</option>
                                        <option value="துலாம்">துலாம்</option>
                                        <option value="விருச்சிகம்">விருச்சிகம்</option>
                                        <option value="தனுசு">தனுசு</option>
                                        <option value="மகரம்">மகரம்</option>
                                        <option value="கும்பம்">கும்பம்</option>
                                        <option value="மீனம்">மீனம்</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">வயது:</label>
                                    <input type="number" id="upload-vayathu" min="1" max="120" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                            </div>



                            <div style="text-align: center; margin-top: 1.5rem;">
                                <button type="submit" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">பதிவேற்று</button>
                                <button type="button" onclick="closeUploadModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">ரத்து செய்</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            modal.id = 'upload-modal';
            document.body.appendChild(modal);
        }

        function uploadFile(event) {
            event.preventDefault();
            const fileInput = document.getElementById('upload-file');
            const name = document.getElementById('upload-user-name').value;
            const city = document.getElementById('upload-user-city').value;
            const natchathiram = document.getElementById('upload-natchathiram').value;
            const raasi = document.getElementById('upload-raasi').value;
            const vayathu = document.getElementById('upload-vayathu').value;

            if (!fileInput.files[0]) {
                alert('கோப்பு தேர்வு செய்யவும்');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('name', name);
            formData.append('city', city);
            formData.append('natchathiram', natchathiram);
            formData.append('raasi', raasi);
            formData.append('vayathu', vayathu);

            fetch('/admin_upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeUploadModal();
                    alert('✅ கோப்பு வெற்றிகரமாக பதிவேற்றப்பட்டது!');
                    location.reload();
                } else {
                    alert('❌ பதிவேற்று பிழை: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                alert('❌ பதிவேற்று பிழை');
            });
        }

        function closeUploadModal() {
            const modal = document.getElementById('upload-modal');
            if (modal) {
                modal.remove();
            }
        }

        function deleteDocument(docId, filename) {
            if (confirm('நீங்கள் "' + filename + '" ஆவணத்தை நீக்க விரும்புகிறீர்களா?')) {
                fetch(`/delete_document/${docId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ஆவணம் வெற்றிகரமாக நீக்கப்பட்டது!');
                        location.reload();
                    } else {
                        alert('❌ நீக்கு பிழை: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    alert('❌ நீக்கு பிழை');
                });
            }
        }

        function performSearch() {
            const filters = {
                name: document.getElementById('search-name').value,
                city: document.getElementById('search-city').value,
                natchathiram: document.getElementById('search-natchathiram').value,
                raasi: document.getElementById('search-raasi').value,
                vayathu_min: document.getElementById('search-vayathu-min').value,
                vayathu_max: document.getElementById('search-vayathu-max').value
            };

            const queryParams = new URLSearchParams();
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    queryParams.append(key, filters[key]);
                }
            });

            fetch(`/advanced_search?${queryParams.toString()}`)
                .then(response => response.json())
                .then(data => {
                    updateDocumentsTable(data.documents);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    alert('❌ தேடல் பிழை');
                });
        }

        function clearSearch() {
            document.getElementById('search-name').value = '';
            document.getElementById('search-city').value = '';
            document.getElementById('search-natchathiram').value = '';
            document.getElementById('search-raasi').value = '';
            document.getElementById('search-vayathu-min').value = '';
            document.getElementById('search-vayathu-max').value = '';
            location.reload(); // Reload to show all documents
        }

        function updateDocumentsTable(documents) {
            const tbody = document.querySelector('#documents-table tbody');
            tbody.innerHTML = '';

            if (documents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 2rem; color: #666;">தேடல் முடிவுகள் இல்லை</td></tr>';
                return;
            }

            documents.forEach(doc => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${doc.filename}</td>
                    <td>${doc.document_type === 'scanned' ? '📷 ஸ்கேன்' : '📤 பதிவேற்றம்'}</td>
                    <td>${doc.user_name ? doc.user_name + '<br><small>(' + doc.user_city + ')</small>' : 'தெரியவில்லை'}</td>
                    <td>${doc.natchathiram || '-'}</td>
                    <td>${doc.raasi || '-'}</td>
                    <td>${doc.vayathu || '-'}</td>
                    <td>${doc.has_jathagam ? '<span style="color: #28a745;">✅ உள்ளது</span>' : '<span style="color: #6c757d;">❌ இல்லை</span>'}</td>
                    <td>${doc.created_at}</td>
                    <td>
                        <a href="${doc.document_type === 'scanned' ? '/static/scanned/' + doc.filename : '/static/uploads/' + doc.filename}"
                           class="btn btn-primary" target="_blank">பார்</a>
                        ${doc.has_jathagam ? '<button class="btn btn-success" onclick="viewJathagam(' + doc.id + ')">🌟 ஜாதகம்</button>' : ''}
                        <button class="btn btn-danger" onclick="deleteDocument(${doc.id}, '${doc.filename}')">நீக்கு</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function viewJathagam(docId) {
            fetch(`/view_jathagam/${docId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showJathagamModal(data.jathagam, data.formatted);
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Jathagam view error:', error);
                    alert('❌ ஜாதக பார்வை பிழை');
                });
        }

        function showJathagamModal(jathagam, formatted) {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 800px; width: 90%; margin: 2rem; max-height: 80vh; overflow-y: auto;">
                        <h3 style="margin-bottom: 1rem; text-align: center; color: #333;">🌟 ஜாதக விவரங்கள் 🌟</h3>

                        <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px; margin-bottom: 1rem;">
                            <h4 style="color: #495057; margin-bottom: 1rem;">📋 அடிப்படை தகவல்கள்</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <div><strong>பெயர்:</strong> ${jathagam.name}</div>
                                <div><strong>பிறந்த தேதி:</strong> ${jathagam.birth_date}</div>
                                <div><strong>பிறந்த நேரம்:</strong> ${jathagam.birth_time}</div>
                                <div><strong>பிறந்த இடம்:</strong> ${jathagam.birth_place}</div>
                            </div>
                        </div>

                        <div style="background: #e3f2fd; padding: 1.5rem; border-radius: 8px; margin-bottom: 1rem;">
                            <h4 style="color: #1976d2; margin-bottom: 1rem;">🌙 முக்கிய ஜோதிட தகவல்கள்</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <div><strong>சந்திர ராசி:</strong> ${jathagam.moon_raasi}</div>
                                <div><strong>நட்சத்திரம்:</strong> ${jathagam.moon_nakshatra}</div>
                                <div><strong>லக்ன ராசி:</strong> ${jathagam.lagna_raasi}</div>
                            </div>
                        </div>

                        <div style="background: #fff3e0; padding: 1.5rem; border-radius: 8px; margin-bottom: 1rem;">
                            <h4 style="color: #f57c00; margin-bottom: 1rem;">🪐 கிரக நிலைகள்</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 0.5rem; font-size: 0.9rem;">
                                ${Object.entries(jathagam.planetary_positions).map(([planet, details]) =>
                                    `<div><strong>${planet}:</strong> ${details.raasi} (${details.nakshatra})</div>`
                                ).join('')}
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 1.5rem;">
                            <button onclick="closeJathagamModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">மூடு</button>
                        </div>
                    </div>
                </div>
            `;
            modal.id = 'jathagam-modal';
            document.body.appendChild(modal);
        }

        function closeJathagamModal() {
            const modal = document.getElementById('jathagam-modal');
            if (modal) {
                modal.remove();
            }
        }

        function showJathagamGenerationModal() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 500px; width: 90%; margin: 2rem;">
                        <h3 style="margin-bottom: 1rem; text-align: center; color: #333;">🌟 ஜாதக உருவாக்கம்</h3>
                        <p style="text-align: center; color: #666; margin-bottom: 2rem;">பெயர், பிறந்த தேதி மற்றும் நேரத்தின் அடிப்படையில் ஜாதகம் உருவாக்கவும்</p>

                        <form onsubmit="generateJathagam(event)">
                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பெயர்:</label>
                                    <input type="text" id="jathagam-name" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பாலினம்:</label>
                                    <select id="jathagam-gender" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                        <option value="Male">ஆண்</option>
                                        <option value="Female">பெண்</option>
                                    </select>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பிறந்த தேதி:</label>
                                    <input type="date" id="jathagam-birth-date" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பிறந்த நேரம்:</label>
                                    <input type="time" id="jathagam-birth-time" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                            </div>

                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">பிறந்த இடம்:</label>
                                <input type="text" id="jathagam-birth-place" placeholder="சென்னை, தமிழ்நாடு" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                            </div>

                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-bottom: 1rem;">
                                <p style="font-size: 0.9rem; color: #666; margin: 0;">
                                    <strong>குறிப்பு:</strong> இந்த ஜாதகம் தமிழ்நாடு பாரம்பரிய ஜோதிட முறைப்படி உருவாக்கப்படும்.
                                    இது கிரக நிலைகள், ராசி, நட்சத்திரம் மற்றும் பாவ அமைப்பு ஆகியவற்றை உள்ளடக்கும்.
                                </p>
                            </div>

                            <div style="text-align: center; margin-top: 1.5rem;">
                                <button type="submit" style="background: #28a745; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">🌟 ஜாதகம் உருவாக்கு</button>
                                <button type="button" onclick="closeJathagamGenerationModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">ரத்து செய்</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            modal.id = 'jathagam-generation-modal';
            document.body.appendChild(modal);
        }

        function generateJathagam(event) {
            event.preventDefault();
            const name = document.getElementById('jathagam-name').value;
            const gender = document.getElementById('jathagam-gender').value;
            const birthDate = document.getElementById('jathagam-birth-date').value;
            const birthTime = document.getElementById('jathagam-birth-time').value;
            const birthPlace = document.getElementById('jathagam-birth-place').value || 'சென்னை';

            // Show loading message
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '⏳ உருவாக்குகிறது...';
            submitBtn.disabled = true;

            const formData = new FormData();
            formData.append('name', name);
            formData.append('gender', gender);
            formData.append('birth_date', birthDate);
            formData.append('birth_time', birthTime);
            formData.append('birth_place', birthPlace);

            fetch('/generate_standalone_jathagam', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;

                if (data.success) {
                    closeJathagamGenerationModal();
                    // Store data for enhanced print system
                    storeJathagamDataForPrint(data.jathagam);
                    showAccurateJathagamModal(data.jathagam, data.formatted, data.chart_html);
                } else {
                    alert('❌ ஜாதக உருவாக்கம் பிழை: ' + data.message);
                }
            })
            .catch(error => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                console.error('Jathagam generation error:', error);
                alert('❌ ஜாதக உருவாக்கம் பிழை: ' + error.message);
            });
        }

        function closeJathagamGenerationModal() {
            const modal = document.getElementById('jathagam-generation-modal');
            if (modal) {
                modal.remove();
            }
        }

        function showJathagamScannerModal() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 700px; width: 90%; margin: 2rem;">
                        <h3 style="margin-bottom: 1rem; text-align: center; color: #333;">📷 ஜாதகம் ஸ்கேன் செய்</h3>
                        <p style="text-align: center; color: #666; margin-bottom: 2rem;">உங்கள் உள்ளூர் ஸ்கேனர்/பிரிண்டரில் இருந்து ஜாதக ஆவணத்தை ஸ்கேன் செய்யவும்</p>

                        <div style="background: #e8f5e8; padding: 1rem; border-radius: 5px; margin-bottom: 1.5rem; border-left: 4px solid #28a745;">
                            <h4 style="color: #155724; margin-bottom: 0.5rem;">🔒 முழு தனியுரிமை பாதுகாப்பு</h4>
                            <ul style="margin: 0; color: #155724; font-size: 0.9rem;">
                                <li>🖥️ உங்கள் உள்ளூர் ஸ்கேனர் மட்டும் பயன்படுத்தப்படும்</li>
                                <li>🚫 சர்வர் ஸ்கேனர்கள் அணுகப்படாது</li>
                                <li>📄 ஸ்கேன் செய்யப்பட்ட ஆவணம் உங்கள் கணினியில் மட்டும் சேமிக்கப்படும்</li>
                                <li>🔒 எந்த தகவலும் சர்வருக்கு அனுப்பப்படாது</li>
                            </ul>
                        </div>

                        <div id="scanner-detection-status" style="background: #fff3cd; padding: 1rem; border-radius: 5px; margin-bottom: 1.5rem; border-left: 4px solid #ffc107;">
                            <div style="color: #856404;">
                                🔍 உங்கள் உள்ளூர் ஸ்கேனர்களை கண்டறிகிறது...
                            </div>
                        </div>

                        <div id="scanner-list" style="display: none;">
                            <!-- Scanner list will be populated here -->
                        </div>

                        <div id="scan-controls" style="display: none; margin-bottom: 1.5rem;">
                            <h4 style="color: #333; margin-bottom: 1rem;">⚙️ ஸ்கேன் அமைப்புகள்</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">தரம்:</label>
                                    <select id="scan-quality" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                        <option value="300">300 DPI (நல்ல தரம்)</option>
                                        <option value="600" selected>600 DPI (உயர் தரம்)</option>
                                        <option value="1200">1200 DPI (மிக உயர் தரம்)</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">வண்ணம்:</label>
                                    <select id="scan-color" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 5px;">
                                        <option value="color" selected>வண்ணம்</option>
                                        <option value="grayscale">சாம்பல்</option>
                                        <option value="blackwhite">கருப்பு-வெள்ளை</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="scan-preview" style="display: none; margin-bottom: 1.5rem;">
                            <h4 style="color: #333; margin-bottom: 1rem;">👁️ ஸ்கேன் முன்னோட்டம்</h4>
                            <div id="preview-container" style="border: 2px dashed #ddd; padding: 2rem; text-align: center; border-radius: 5px;">
                                <div style="color: #666;">ஸ்கேன் செய்யப்பட்ட ஆவணம் இங்கே காட்டப்படும்</div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 1.5rem;">
                            <button id="start-scan-btn" onclick="startJathagamScan()" style="background: #28a745; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer; font-weight: bold; display: none;">
                                📷 ஸ்கேன் தொடங்கு
                            </button>
                            <button id="save-scan-btn" onclick="saveScannedJathagam()" style="background: #007bff; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer; font-weight: bold; display: none; margin-left: 0.5rem;">
                                💾 ஸ்கேன் சேமி
                            </button>
                            <button onclick="closeJathagamScannerModal()" style="background: #6c757d; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer;">
                                மூடு
                            </button>
                        </div>
                    </div>
                </div>
            `;
            modal.id = 'jathagam-scanner-modal';
            document.body.appendChild(modal);

            // Initialize scanner detection
            initializeJathagamScanner();
        }

        function closeJathagamScannerModal() {
            const modal = document.getElementById('jathagam-scanner-modal');
            if (modal) {
                modal.remove();
            }
        }

        function showJathagamListModal() {
            fetch('/get_jathagam_list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayJathagamList(data.jathagams);
                    } else {
                        alert('❌ ஜாதக பட்டியல் பிழை: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Jathagam list error:', error);
                    alert('❌ ஜாதக பட்டியல் பிழை');
                });
        }

        function displayJathagamList(jathagams) {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 800px; width: 90%; margin: 2rem; max-height: 80vh; overflow-y: auto;">
                        <h3 style="margin-bottom: 1rem; text-align: center; color: #333;">📋 ஜாதக பட்டியல்</h3>

                        ${jathagams.length > 0 ? `
                            <div style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse; margin-top: 1rem;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 0.5rem; border: 1px solid #ddd; text-align: left;">பெயர்</th>
                                            <th style="padding: 0.5rem; border: 1px solid #ddd; text-align: left;">பிறந்த தேதி</th>
                                            <th style="padding: 0.5rem; border: 1px solid #ddd; text-align: left;">ராசி</th>
                                            <th style="padding: 0.5rem; border: 1px solid #ddd; text-align: left;">நட்சத்திரம்</th>
                                            <th style="padding: 0.5rem; border: 1px solid #ddd; text-align: left;">செயல்கள்</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${jathagams.map(doc => `
                                            <tr>
                                                <td style="padding: 0.5rem; border: 1px solid #ddd;">${doc.user_name || 'தெரியவில்லை'}</td>
                                                <td style="padding: 0.5rem; border: 1px solid #ddd;">${doc.birth_date || '-'}</td>
                                                <td style="padding: 0.5rem; border: 1px solid #ddd;">${doc.raasi || '-'}</td>
                                                <td style="padding: 0.5rem; border: 1px solid #ddd;">${doc.natchathiram || '-'}</td>
                                                <td style="padding: 0.5rem; border: 1px solid #ddd;">
                                                    <button onclick="viewJathagam(${doc.id})" style="background: #28a745; color: white; border: none; padding: 0.3rem 0.6rem; border-radius: 3px; cursor: pointer; font-size: 0.8rem;">🌟 பார்</button>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : `
                            <div style="text-align: center; padding: 3rem; color: #666;">
                                <div style="font-size: 4rem; margin-bottom: 1rem;">🌟</div>
                                <h3>ஜாதகங்கள் இல்லை</h3>
                                <p>இன்னும் ஜாதகங்கள் எதுவும் உருவாக்கப்படவில்லை.</p>
                            </div>
                        `}

                        <div style="text-align: center; margin-top: 1.5rem;">
                            <button onclick="closeJathagamListModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">மூடு</button>
                        </div>
                    </div>
                </div>
            `;
            modal.id = 'jathagam-list-modal';
            document.body.appendChild(modal);
        }

        function closeJathagamListModal() {
            const modal = document.getElementById('jathagam-list-modal');
            if (modal) {
                modal.remove();
            }
        }

        function showAccurateJathagamModal(jathagam, formatted, chartHtml) {
            const modal = document.createElement('div');
            const personal = jathagam.personal_details || {};
            const astro = jathagam.astrological_details || {};
            const planets = jathagam.planetary_positions || {};

            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; overflow-y: auto;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 1000px; width: 95%; margin: 2rem; max-height: 90vh; overflow-y: auto;">
                        <h3 style="margin-bottom: 1rem; text-align: center; color: #333;">🌟 ${personal.name || 'தெரியவில்லை'} அவர்களின் ஜாதகம் 🌟</h3>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                            <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
                                <h4 style="color: #495057; margin-bottom: 1rem;">📋 அடிப்படை தகவல்கள்</h4>
                                <div style="display: grid; gap: 0.5rem;">
                                    <div><strong>பெயர்:</strong> ${personal.name || 'தெரியவில்லை'}</div>
                                    <div><strong>பாலினம்:</strong> ${personal.gender === 'Male' ? 'ஆண்' : 'பெண்'}</div>
                                    <div><strong>பிறந்த தேதி:</strong> ${personal.birth_date || 'தெரியவில்லை'}</div>
                                    <div><strong>பிறந்த நேரம்:</strong> ${personal.birth_time || 'தெரியவில்லை'}</div>
                                    <div><strong>பிறந்த இடம்:</strong> ${personal.birth_place || 'தெரியவில்லை'}</div>
                                    <div><strong>அட்சாம்சம்:</strong> ${personal.latitude || 'தெரியவில்லை'}°</div>
                                    <div><strong>தீர்க்காம்சம்:</strong> ${personal.longitude || 'தெரியவில்லை'}°</div>
                                </div>
                            </div>

                            <div style="background: #e3f2fd; padding: 1.5rem; border-radius: 8px;">
                                <h4 style="color: #1976d2; margin-bottom: 1rem;">🌙 முக்கிய ஜோதிட தகவல்கள்</h4>
                                <div style="display: grid; gap: 0.5rem;">
                                    <div><strong>சந்திர ராசி:</strong> ${astro.moon_raasi || 'தெரியவில்லை'}</div>
                                    <div><strong>நட்சத்திரம்:</strong> ${astro.moon_nakshatra || 'தெரியவில்லை'}</div>
                                    <div><strong>பாதம்:</strong> ${astro.moon_pada || 'தெரியவில்லை'}</div>
                                    <div><strong>லக்ன ராசி:</strong> ${astro.lagna_raasi || 'தெரியவில்லை'}</div>
                                    <div><strong>லக்ன நட்சத்திரம்:</strong> ${astro.lagna_nakshatra || 'தெரியவில்லை'}</div>
                                    <div><strong>லக்ன டிகிரி:</strong> ${astro.lagna_degrees || 'தெரியவில்லை'}°</div>
                                </div>
                            </div>
                        </div>

                        <div style="background: #fff3e0; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem;">
                            <h4 style="color: #f57c00; margin-bottom: 1rem;">🪐 கிரக நிலைகள் (Swiss Ephemeris கணக்கீடு)</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 0.5rem; font-size: 0.9rem;">
                                ${Object.entries(planets).map(([planet, details]) =>
                                    '<div style="padding: 0.3rem; background: white; border-radius: 4px;">' +
                                        '<strong>' + (details.tamil_name || planet) + ':</strong> ' +
                                        (details.raasi || 'தெரியவில்லை') + ' - ' + (details.nakshatra || 'தெரியவில்லை') + ' ' +
                                        '(' + (details.degrees_in_sign || 0) + '° | பாதம்: ' + (details.pada || 1) + ')' +
                                    '</div>'
                                ).join('')}
                            </div>
                        </div>

                        <div style="margin-bottom: 2rem;">
                            <h4 style="color: #333; margin-bottom: 1rem; text-align: center;">📊 ஜாதக சக்கரங்கள்</h4>
                            ${chartHtml || '<p style="text-align: center; color: #666;">சக்கரம் கிடைக்கவில்லை</p>'}
                        </div>

                        <div style="background: #f0f0f0; padding: 1rem; border-radius: 5px; margin-bottom: 1rem; font-size: 0.9rem; color: #666;">
                            <strong>கணக்கீட்டு முறை:</strong> ${jathagam.calculation_method || 'Swiss Ephemeris with Lahiri Ayanamsa'}<br>
                            <strong>உருவாக்கப்பட்ட நேரம்:</strong> ${personal.generated_at || 'தெரியவில்லை'}
                        </div>

                        <div style="text-align: center; margin-top: 1.5rem; display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                            <button onclick="enhancedPrintSystem.enhancedPrintJathagam()" style="background: #28a745; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">🖨️ மேம்பட்ட அச்சு</button>
                            <button onclick="printJathagam()" style="background: #17a2b8; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">🖨️ விரைவு அச்சு</button>
                            <button onclick="saveJathagamAsPDF()" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">💾 PDF சேமி</button>
                            <button onclick="shareJathagam()" style="background: #ffc107; color: #212529; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">📤 பகிர்</button>
                            <button onclick="closeAccurateJathagamModal()" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">மூடு</button>
                        </div>
                    </div>
                </div>
            `;
            modal.id = 'accurate-jathagam-modal';
            document.body.appendChild(modal);
        }

        function closeAccurateJathagamModal() {
            const modal = document.getElementById('accurate-jathagam-modal');
            if (modal) {
                modal.remove();
            }
        }

        function printJathagam() {
            // Create a print-friendly version
            const modal = document.getElementById('accurate-jathagam-modal');
            if (!modal) return;

            const content = modal.querySelector('div > div').cloneNode(true);

            // Remove buttons and modal styling for print
            const buttons = content.querySelector('div[style*="text-align: center; margin-top: 1.5rem"]');
            if (buttons) buttons.remove();

            // Create print window
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>ஜாதகம் - ${content.querySelector('h3').textContent}</title>
                    <meta charset="UTF-8">
                    <style>
                        body { font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', sans-serif; margin: 20px; }
                        .chart-container { page-break-inside: avoid; }
                        .south-indian-chart-12 { margin: 20px auto; }
                        @media print {
                            body { margin: 0; }
                            .chart-container { page-break-inside: avoid; }
                        }
                    </style>
                </head>
                <body>
                    ${content.outerHTML}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
        }

        function saveJathagamAsPDF() {
            // Use browser's print to PDF functionality
            alert('📄 PDF சேமிப்பு:\n\n1. "🖨️ அச்சிடு" பொத்தானை அழுத்தவும்\n2. அச்சுப்பொறி தேர்வில் "Save as PDF" தேர்ந்தெடுக்கவும்\n3. கோப்பு பெயர் மற்றும் இடத்தை தேர்ந்தெடுத்து சேமிக்கவும்');
            printJathagam();
        }

        function shareJathagam() {
            const modal = document.getElementById('accurate-jathagam-modal');
            if (!modal) return;

            const nameElement = modal.querySelector('h3');
            const name = nameElement ? nameElement.textContent : 'ஜாதகம்';

            if (navigator.share) {
                navigator.share({
                    title: name,
                    text: 'தமிழ் ஜாதகம் பகிர்வு',
                    url: window.location.href
                }).catch(console.error);
            } else {
                // Fallback for browsers without Web Share API
                const shareText = `${name}\n\nதமிழ் ஜாதகம் உருவாக்கப்பட்டது\n${window.location.href}`;

                if (navigator.clipboard) {
                    navigator.clipboard.writeText(shareText).then(() => {
                        alert('📋 ஜாதக தகவல் கிளிப்போர்டில் நகலெடுக்கப்பட்டது!\n\nWhatsApp, Email அல்லது SMS மூலம் பகிர்ந்து கொள்ளலாம்.');
                    });
                } else {
                    alert('📤 பகிர்வு:\n\n' + shareText);
                }
            }
        }

        // Store current Jathagam data for enhanced print system
        function storeJathagamDataForPrint(jathagamData) {
            window.currentJathagamData = jathagamData;
        }
    </script>

    <!-- Enhanced Print System -->
    <script src="{{ url_for('static', filename='js/enhanced_print_system.js') }}"></script>

    <!-- Client-Side Scanner System -->
    <script src="{{ url_for('static', filename='js/client_side_scanner.js') }}"></script>
</body>
</html>
