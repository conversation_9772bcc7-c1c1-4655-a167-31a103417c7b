#!/usr/bin/env python3
"""
Comprehensive test for both text and image uploads
"""
import os
import sys
from werkzeug.datastructures import FileStorage
from io import BytesIO
from PIL import Image

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, extract_user_data_from_file
from database import db, User, Document

def test_comprehensive_upload():
    """Test both text and image file uploads"""
    with app.app_context():
        print("=== COMPREHENSIVE UPLOAD TEST ===")
        
        # Count before
        users_before = User.query.count()
        docs_before = Document.query.count()
        print(f"Starting: {users_before} users, {docs_before} documents")
        
        # Test 1: CSV file upload (should extract users)
        print("\n📄 Test 1: CSV File Upload")
        csv_content = "Grace Kelly,Monaco\nRobert Johnson,Mississippi\nMary Smith,Texas"
        csv_buffer = BytesIO(csv_content.encode('utf-8'))
        csv_file = FileStorage(
            stream=csv_buffer,
            filename='test_users.csv',
            content_type='text/csv'
        )
        
        with app.test_client() as client:
            response = client.post('/upload', 
                                 data={'file': csv_file},
                                 content_type='multipart/form-data')
            print(f"   CSV Upload response: {response.status_code}")
        
        # Test 2: Image file upload (should NOT try to extract users)
        print("\n🖼️ Test 2: Image File Upload")
        img = Image.new('RGB', (200, 200), color='blue')
        img_buffer = BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        img_file = FileStorage(
            stream=img_buffer,
            filename='test_document.png',
            content_type='image/png'
        )
        
        with app.test_client() as client:
            response = client.post('/upload', 
                                 data={'file': img_file},
                                 content_type='multipart/form-data')
            print(f"   Image Upload response: {response.status_code}")
        
        # Test 3: JSON file upload (should extract users)
        print("\n📋 Test 3: JSON File Upload")
        json_content = '{"name": "Alice Cooper", "city": "Detroit"}'
        json_buffer = BytesIO(json_content.encode('utf-8'))
        json_file = FileStorage(
            stream=json_buffer,
            filename='test_user.json',
            content_type='application/json'
        )
        
        with app.test_client() as client:
            response = client.post('/upload', 
                                 data={'file': json_file},
                                 content_type='multipart/form-data')
            print(f"   JSON Upload response: {response.status_code}")
        
        # Check final results
        users_after = User.query.count()
        docs_after = Document.query.count()
        
        print(f"\n📊 Final Results:")
        print(f"   • Users: {users_before} → {users_after} (Added: {users_after - users_before})")
        print(f"   • Documents: {docs_before} → {docs_after} (Added: {docs_after - docs_before})")
        
        # Check recent uploads
        recent_docs = Document.query.filter_by(document_type='uploaded').order_by(Document.created_at.desc()).limit(6).all()
        print(f"\n📁 Recent Uploaded Documents:")
        for doc in recent_docs:
            user_name = doc.user.name if doc.user else "No User"
            exists = os.path.exists(doc.file_path)
            is_image = doc.file_type in ['.png', '.jpg', '.jpeg']
            print(f"   • {doc.filename} ({doc.file_type}) → {user_name} (File exists: {exists}) {'🖼️' if is_image else '📄'}")
        
        # Verify file type handling
        print(f"\n🔍 File Type Analysis:")
        text_docs = Document.query.filter(Document.file_type.in_(['.csv', '.json', '.txt', '.xml'])).count()
        image_docs = Document.query.filter(Document.file_type.in_(['.png', '.jpg', '.jpeg'])).count()
        print(f"   • Text documents: {text_docs}")
        print(f"   • Image documents: {image_docs}")
        
        print("\n✅ Comprehensive upload test completed!")

if __name__ == "__main__":
    test_comprehensive_upload()
