#!/usr/bin/env python3
"""
Test the new scanner-first workflow
"""
import os
import sys
from werkzeug.datastructures import FileStorage
from io import BytesIO
from PIL import Image

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import db, User, Document

def test_scanner_first_workflow():
    """Test the new scanner-first workflow"""
    with app.app_context():
        print("=== Testing Scanner-First Workflow ===")
        
        # Count before
        users_before = User.query.count()
        docs_before = Document.query.count()
        print(f"Before: {users_before} users, {docs_before} documents")
        
        # Test 1: Check scanner status
        print("\n1. Testing scanner status check...")
        with app.test_client() as client:
            response = client.get('/check_scanner')
            scanner_data = response.get_json()
            print(f"   Scanner status: {scanner_data}")
        
        # Test 2: Test file upload endpoint
        print("\n2. Testing file upload endpoint...")
        img = Image.new('RGB', (100, 100), color='blue')
        img_buffer = BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        file_storage = FileStorage(
            stream=img_buffer,
            filename='test_workflow.png',
            content_type='image/png'
        )
        
        with app.test_client() as client:
            response = client.post('/upload_document', 
                                 data={'file': file_storage},
                                 content_type='multipart/form-data')
            upload_data = response.get_json()
            print(f"   Upload response: {upload_data}")
        
        # Test 3: Test user details submission
        if upload_data and upload_data.get('success'):
            print("\n3. Testing user details submission...")
            with app.test_client() as client:
                response = client.post('/', data={
                    'name': 'Test User Workflow',
                    'city': 'Test City Workflow',
                    'document_path': upload_data['filename'],
                    'document_type': 'uploaded'
                })
                print(f"   User submission response: {response.status_code}")
        
        # Test 4: Check results
        users_after = User.query.count()
        docs_after = Document.query.count()
        print(f"\nAfter: {users_after} users, {docs_after} documents")
        print(f"Added: {users_after - users_before} users, {docs_after - docs_before} documents")
        
        # Test 5: Check database records
        print("\n4. Checking database records...")
        latest_user = User.query.order_by(User.added_at.desc()).first()
        if latest_user:
            print(f"   Latest user: {latest_user.name} from {latest_user.city} (source: {latest_user.source})")
            user_docs = Document.query.filter_by(user_id=latest_user.id).all()
            print(f"   User documents: {len(user_docs)}")
            for doc in user_docs:
                exists = os.path.exists(doc.file_path)
                print(f"     - {doc.filename} (exists: {exists}, type: {doc.document_type})")
        
        print("\n✅ Scanner-first workflow test completed!")

if __name__ == "__main__":
    test_scanner_first_workflow()
