#!/usr/bin/env python3
"""
Test scanner detection methods
"""
import subprocess
import platform

def test_scanner_detection():
    """Test various methods to detect scanners"""
    print("=== Testing Scanner Detection ===")
    print(f"Operating System: {platform.system()}")
    
    if platform.system() == "Windows":
        print("\n1. Testing WIA devices (scan/imaging keywords)...")
        try:
            result = subprocess.run(['powershell', '-Command', 
                                   'Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like "*scan*" -or $_.Name -like "*imaging*"} | Select-Object Name'], 
                                  capture_output=True, text=True, timeout=15)
            print(f"Return code: {result.returncode}")
            print(f"Output: {result.stdout}")
            print(f"Error: {result.stderr}")
        except Exception as e:
            print(f"Error: {e}")
        
        print("\n2. Testing printer/scanner manufacturers...")
        try:
            result = subprocess.run(['powershell', '-Command', 
                                   'Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like "*Canon*" -or $_.Name -like "*HP*" -or $_.Name -like "*Epson*" -or $_.Name -like "*Brother*" -or $_.Name -like "*Samsung*"} | Select-Object Name'], 
                                  capture_output=True, text=True, timeout=15)
            print(f"Return code: {result.returncode}")
            print(f"Output: {result.stdout}")
            print(f"Error: {result.stderr}")
        except Exception as e:
            print(f"Error: {e}")
        
        print("\n3. Testing all printers...")
        try:
            result = subprocess.run(['powershell', '-Command', 
                                   'Get-WmiObject -Class Win32_Printer | Select-Object Name, DriverName'], 
                                  capture_output=True, text=True, timeout=15)
            print(f"Return code: {result.returncode}")
            print(f"Output: {result.stdout}")
            print(f"Error: {result.stderr}")
        except Exception as e:
            print(f"Error: {e}")
        
        print("\n4. Testing WIA service...")
        try:
            result = subprocess.run(['powershell', '-Command', 
                                   'Get-Service -Name "stisvc" | Select-Object Status'], 
                                  capture_output=True, text=True, timeout=15)
            print(f"Return code: {result.returncode}")
            print(f"Output: {result.stdout}")
            print(f"Error: {result.stderr}")
        except Exception as e:
            print(f"Error: {e}")
        
        print("\n5. Testing all PnP devices with 'printer' keyword...")
        try:
            result = subprocess.run(['powershell', '-Command', 
                                   'Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like "*printer*"} | Select-Object Name'], 
                                  capture_output=True, text=True, timeout=15)
            print(f"Return code: {result.returncode}")
            print(f"Output: {result.stdout}")
            print(f"Error: {result.stderr}")
        except Exception as e:
            print(f"Error: {e}")
    
    else:
        print("\n1. Testing SANE devices...")
        try:
            result = subprocess.run(['scanimage', '-L'], capture_output=True, text=True, timeout=15)
            print(f"Return code: {result.returncode}")
            print(f"Output: {result.stdout}")
            print(f"Error: {result.stderr}")
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    test_scanner_detection()
