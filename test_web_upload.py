#!/usr/bin/env python3
"""
Final verification of image upload through web interface
"""
import os
import sys
import requests
from PIL import Image
from io import BytesIO

def test_web_interface_upload():
    """Test uploading through the actual web interface"""
    print("=== WEB INTERFACE IMAGE UPLOAD TEST ===")
    
    # Create a test image
    img = Image.new('RGB', (300, 400), color='green')
    img_buffer = BytesIO()
    img.save(img_buffer, format='JPEG')
    img_buffer.seek(0)
    
    # Upload via the web interface
    url = 'http://127.0.0.1:5000/upload'
    files = {'file': ('verification_image.jpg', img_buffer, 'image/jpeg')}
    
    try:
        response = requests.post(url, files=files, allow_redirects=False)
        print(f"Upload response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 302:
            print("✅ Upload successful (redirected as expected)")
        else:
            print(f"❌ Unexpected response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure Flask app is running.")
    except Exception as e:
        print(f"❌ Error during upload: {e}")

if __name__ == "__main__":
    test_web_interface_upload()
