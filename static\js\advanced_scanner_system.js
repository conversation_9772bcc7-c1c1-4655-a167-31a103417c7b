/**
 * Advanced Client-Side Scanner System
 * Following the exact architecture:
 * User's Scanner/Printer → Client Device (Browser/App) → Web Server
 * 
 * Key Components:
 * 1. Client-Side Scanner Access Options
 * 2. File Upload & Post-processing  
 * 3. Security Considerations
 */

class AdvancedScannerSystem {
    constructor() {
        this.scanners = [];
        this.selectedScanner = null;
        this.scanSettings = {
            resolution: 300,
            colorMode: 'color',
            format: 'PDF',
            quality: 'high'
        };
        this.scannedDocument = null;
        console.log('📷 Advanced Scanner System initialized');
    }

    /**
     * Initialize scanner detection following the architecture
     */
    async initialize() {
        console.log('🔍 Detecting scanners using client-side architecture...');
        
        try {
            // Option 1: Web TWAIN SDK (<PERSON>rowser + Agent)
            await this.detectWebTWAINScanners();
            
            // Option 2: Desktop App Integration (Electron + Node.js)
            await this.detectDesktopScanners();
            
            // Option 3: Browser Media Devices (Camera as scanner)
            await this.detectCameraDevices();
            
            // Option 4: File Upload Alternative
            this.setupFileUploadOption();
            
            this.updateScannerUI();
            
        } catch (error) {
            console.error('Scanner detection error:', error);
            this.showError('ஸ்கேனர் கண்டறிதல் பிழை: ' + error.message);
        }
    }

    /**
     * Option 1: Web TWAIN SDK Detection
     * Architecture: Browser → Web TWAIN Agent → Scanner
     */
    async detectWebTWAINScanners() {
        console.log('🔌 Checking Web TWAIN SDK...');
        
        try {
            // Check for Dynamsoft Web TWAIN
            if (typeof Dynamsoft !== 'undefined' && Dynamsoft.DWT) {
                console.log('✅ Dynamsoft Web TWAIN detected');
                
                const DWObject = await Dynamsoft.DWT.GetWebTwain('dwtcontrolContainer');
                if (DWObject) {
                    const sourceCount = DWObject.SourceCount;
                    
                    for (let i = 0; i < sourceCount; i++) {
                        const sourceName = DWObject.GetSourceNameItems(i);
                        this.scanners.push({
                            name: sourceName,
                            type: 'TWAIN Scanner',
                            method: 'web_twain',
                            capabilities: {
                                resolutions: [150, 200, 300, 600, 1200],
                                colorModes: ['color', 'grayscale', 'blackwhite'],
                                formats: ['PDF', 'JPEG', 'PNG', 'TIFF']
                            },
                            available: true,
                            source: 'Web TWAIN SDK',
                            dwObject: DWObject,
                            sourceIndex: i
                        });
                    }
                }
            } else {
                console.log('⚠️ Web TWAIN SDK not available');
                this.showWebTWAINInstallOption();
            }
        } catch (error) {
            console.log('Web TWAIN detection failed:', error.message);
        }
    }

    /**
     * Option 2: Desktop App Integration
     * Architecture: Electron App → Native Scanner API → Scanner
     */
    async detectDesktopScanners() {
        console.log('🖥️ Checking desktop app integration...');
        
        try {
            // Check if running in Electron
            if (typeof window !== 'undefined' && window.electronAPI) {
                console.log('✅ Electron environment detected');
                
                const electronScanners = await window.electronAPI.getScanners();
                electronScanners.forEach(scanner => {
                    this.scanners.push({
                        name: scanner.name,
                        type: 'Desktop Scanner',
                        method: 'electron_native',
                        capabilities: scanner.capabilities,
                        available: true,
                        source: 'Electron Native API',
                        device: scanner
                    });
                });
            } else {
                console.log('⚠️ Desktop app not available');
                this.showDesktopAppOption();
            }
        } catch (error) {
            console.log('Desktop scanner detection failed:', error.message);
        }
    }

    /**
     * Option 3: Camera as Scanner
     * Architecture: Browser → Media API → Camera Device
     */
    async detectCameraDevices() {
        console.log('📱 Checking camera devices...');
        
        try {
            if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                
                videoDevices.forEach((device, index) => {
                    this.scanners.push({
                        name: device.label || `Camera Scanner ${index + 1}`,
                        type: 'Camera Scanner',
                        method: 'camera_scan',
                        capabilities: {
                            resolutions: ['HD', 'Full HD', '4K'],
                            colorModes: ['color'],
                            formats: ['JPEG', 'PNG']
                        },
                        available: true,
                        source: 'Browser Media API',
                        deviceId: device.deviceId
                    });
                });
                
                console.log(`✅ Found ${videoDevices.length} camera devices`);
            }
        } catch (error) {
            console.log('Camera detection failed:', error.message);
        }
    }

    /**
     * Option 4: File Upload Alternative
     */
    setupFileUploadOption() {
        this.scanners.push({
            name: 'File Upload Scanner',
            type: 'File Upload',
            method: 'file_upload',
            capabilities: {
                formats: ['PDF', 'JPEG', 'PNG', 'TIFF'],
                maxSize: '10MB'
            },
            available: true,
            source: 'Browser File API'
        });
        
        console.log('✅ File upload option ready');
    }

    /**
     * Update scanner UI
     */
    updateScannerUI() {
        const statusDiv = document.getElementById('scanner-status');
        const listDiv = document.getElementById('scanner-list');
        const controlsDiv = document.getElementById('scan-controls');
        
        if (this.scanners.length > 0) {
            statusDiv.innerHTML = `
                <div style="color: #155724; background: #d4edda; padding: 1rem; border-radius: 5px; border-left: 4px solid #28a745;">
                    ✅ ${this.scanners.length} ஸ்கேனர் கண்டறியப்பட்டது
                </div>
            `;
            
            listDiv.innerHTML = this.generateScannerListHTML();
            listDiv.style.display = 'block';
            
            controlsDiv.style.display = 'block';
            
            // Select first scanner by default
            this.selectedScanner = this.scanners[0];
        } else {
            statusDiv.innerHTML = `
                <div style="color: #721c24; background: #f8d7da; padding: 1rem; border-radius: 5px; border-left: 4px solid #dc3545;">
                    ❌ ஸ்கேனர் கண்டறியப்படவில்லை
                </div>
            `;
        }
    }

    /**
     * Generate scanner list HTML
     */
    generateScannerListHTML() {
        return this.scanners.map((scanner, index) => `
            <div style="border: 1px solid #ddd; padding: 1rem; border-radius: 5px; margin-bottom: 0.5rem; ${index === 0 ? 'background: #e8f5e8; border-color: #28a745;' : ''}">
                <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="radio" name="selected-scanner" value="${index}" ${index === 0 ? 'checked' : ''} onchange="advancedScanner.selectScanner(${index})">
                    <div style="margin-left: 0.5rem; flex: 1;">
                        <div style="font-weight: bold; color: #333;">
                            📷 ${scanner.name}
                        </div>
                        <div style="font-size: 0.9em; color: #666;">
                            வகை: ${scanner.type} | மூலம்: ${scanner.source}
                        </div>
                        <div style="font-size: 0.8em; color: #007bff; margin-top: 0.2rem;">
                            📋 ஆதரவு: ${scanner.capabilities.formats.join(', ')}
                        </div>
                    </div>
                </label>
            </div>
        `).join('');
    }

    /**
     * Select scanner
     */
    selectScanner(index) {
        this.selectedScanner = this.scanners[index];
        console.log('Selected scanner:', this.selectedScanner.name);
        this.updateScannerUI();
    }

    /**
     * Start scanning process
     */
    async startScan() {
        if (!this.selectedScanner) {
            alert('❌ ஸ்கேனர் தேர்ந்தெடுக்கப்படவில்லை');
            return;
        }

        console.log('📷 Starting scan with:', this.selectedScanner.name);
        
        try {
            switch (this.selectedScanner.method) {
                case 'web_twain':
                    await this.scanWithWebTWAIN();
                    break;
                case 'electron_native':
                    await this.scanWithElectron();
                    break;
                case 'camera_scan':
                    await this.scanWithCamera();
                    break;
                case 'file_upload':
                    await this.scanWithFileUpload();
                    break;
                default:
                    throw new Error('Unsupported scan method');
            }
        } catch (error) {
            console.error('Scan error:', error);
            alert('❌ ஸ்கேன் பிழை: ' + error.message);
        }
    }

    /**
     * Scan with Web TWAIN
     */
    async scanWithWebTWAIN() {
        const scanner = this.selectedScanner;
        const DWObject = scanner.dwObject;
        
        try {
            // Select scanner source
            DWObject.SelectSourceByIndex(scanner.sourceIndex);
            
            // Configure scan settings
            DWObject.Resolution = this.scanSettings.resolution;
            DWObject.PixelType = this.getPixelType(this.scanSettings.colorMode);
            
            // Acquire image
            DWObject.AcquireImage();
            
            // Wait for scan completion
            DWObject.RegisterEvent('OnPostTransfer', () => {
                console.log('✅ Web TWAIN scan completed');
                this.processTWAINScan(DWObject);
            });
            
        } catch (error) {
            throw new Error('Web TWAIN scan failed: ' + error.message);
        }
    }

    /**
     * Process TWAIN scan result
     */
    processTWAINScan(DWObject) {
        try {
            const imageCount = DWObject.HowManyImagesInBuffer;
            if (imageCount > 0) {
                // Convert to desired format
                if (this.scanSettings.format === 'PDF') {
                    DWObject.SaveAsPDF('scanned_document.pdf', 0, imageCount - 1);
                } else {
                    DWObject.SaveAsJPEG('scanned_document.jpg', 0);
                }
                
                this.showScanPreview(DWObject);
            }
        } catch (error) {
            console.error('TWAIN processing error:', error);
        }
    }

    /**
     * Scan with camera
     */
    async scanWithCamera() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { deviceId: this.selectedScanner.deviceId }
            });
            
            this.showCameraPreview(stream);
            
        } catch (error) {
            throw new Error('Camera access failed: ' + error.message);
        }
    }

    /**
     * Scan with file upload
     */
    async scanWithFileUpload() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.pdf,.jpg,.jpeg,.png,.tiff';
        input.multiple = false;
        
        input.onchange = (event) => {
            const file = event.target.files[0];
            if (file) {
                this.scannedDocument = file;
                this.showFilePreview(file);
            }
        };
        
        input.click();
    }

    /**
     * Show camera preview
     */
    showCameraPreview(stream) {
        const previewDiv = document.getElementById('scan-preview');
        previewDiv.innerHTML = `
            <h4>📷 Camera Preview</h4>
            <video id="camera-video" autoplay style="width: 100%; max-width: 400px; border: 1px solid #ddd;"></video>
            <br><br>
            <button onclick="advancedScanner.captureFromCamera()" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">
                📸 Capture Document
            </button>
        `;
        previewDiv.style.display = 'block';
        
        const video = document.getElementById('camera-video');
        video.srcObject = stream;
        this.cameraStream = stream;
    }

    /**
     * Capture from camera
     */
    captureFromCamera() {
        const video = document.getElementById('camera-video');
        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        const ctx = canvas.getContext('2d');
        ctx.drawImage(video, 0, 0);
        
        canvas.toBlob(blob => {
            this.scannedDocument = new File([blob], 'camera_scan.jpg', { type: 'image/jpeg' });
            this.showFilePreview(this.scannedDocument);
            
            // Stop camera
            if (this.cameraStream) {
                this.cameraStream.getTracks().forEach(track => track.stop());
            }
        }, 'image/jpeg', 0.9);
    }

    /**
     * Show file preview
     */
    showFilePreview(file) {
        const previewDiv = document.getElementById('scan-preview');
        previewDiv.innerHTML = `
            <h4>👁️ Document Preview</h4>
            <div style="text-align: center; padding: 1rem; border: 1px solid #ddd; border-radius: 5px;">
                ${file.type.startsWith('image/') ? 
                    `<img src="${URL.createObjectURL(file)}" style="max-width: 100%; max-height: 300px;">` :
                    `<div style="font-size: 3rem;">📄</div><div>${file.name}</div>`
                }
                <div style="margin-top: 1rem;">
                    <strong>File:</strong> ${file.name}<br>
                    <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                    <strong>Type:</strong> ${file.type}
                </div>
            </div>
        `;
        previewDiv.style.display = 'block';
        
        // Show save button
        document.getElementById('save-scan-btn').style.display = 'inline-block';
    }

    /**
     * Save scanned document
     */
    async saveScannedDocument() {
        if (!this.scannedDocument) {
            alert('❌ ஸ்கேன் செய்யப்பட்ட ஆவணம் இல்லை');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('file', this.scannedDocument);
            formData.append('document_type', 'scanned_document');
            formData.append('scanner_method', this.selectedScanner.method);
            formData.append('scanner_name', this.selectedScanner.name);

            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                alert('✅ ஆவணம் வெற்றிகரமாக சேமிக்கப்பட்டது!');
                this.closeModal();
            } else {
                throw new Error(result.message || 'Upload failed');
            }
        } catch (error) {
            console.error('Save error:', error);
            alert('❌ சேமிப்பு பிழை: ' + error.message);
        }
    }

    /**
     * Show Web TWAIN install option
     */
    showWebTWAINInstallOption() {
        const installDiv = document.createElement('div');
        installDiv.innerHTML = `
            <div style="background: #fff3cd; padding: 1rem; border-radius: 5px; margin: 1rem 0; border-left: 4px solid #ffc107;">
                <h4 style="color: #856404;">🔌 Professional Scanner Support</h4>
                <p style="color: #856404; margin: 0.5rem 0;">
                    For direct scanner access, install the Web TWAIN helper:
                </p>
                <a href="https://www.dynamsoft.com/web-twain/downloads" target="_blank" 
                   style="background: #007bff; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 5px; display: inline-block;">
                    📥 Download Web TWAIN SDK
                </a>
            </div>
        `;
        document.getElementById('scanner-options').appendChild(installDiv);
    }

    /**
     * Show desktop app option
     */
    showDesktopAppOption() {
        const desktopDiv = document.createElement('div');
        desktopDiv.innerHTML = `
            <div style="background: #e2e3e5; padding: 1rem; border-radius: 5px; margin: 1rem 0; border-left: 4px solid #6c757d;">
                <h4 style="color: #495057;">🖥️ Desktop App Available</h4>
                <p style="color: #495057; margin: 0.5rem 0;">
                    For full scanner control, use our desktop application with native scanner access.
                </p>
            </div>
        `;
        document.getElementById('scanner-options').appendChild(desktopDiv);
    }

    /**
     * Get pixel type for TWAIN
     */
    getPixelType(colorMode) {
        switch (colorMode) {
            case 'color': return 2; // RGB
            case 'grayscale': return 1; // Grayscale
            case 'blackwhite': return 0; // Black & White
            default: return 2;
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        const statusDiv = document.getElementById('scanner-status');
        if (statusDiv) {
            statusDiv.innerHTML = `
                <div style="color: #721c24; background: #f8d7da; padding: 1rem; border-radius: 5px; border-left: 4px solid #dc3545;">
                    ❌ ${message}
                </div>
            `;
        }
    }

    /**
     * Close modal
     */
    closeModal() {
        const modal = document.getElementById('advanced-scanner-modal');
        if (modal) {
            modal.remove();
        }
    }
}

// Global instance
let advancedScanner = null;
