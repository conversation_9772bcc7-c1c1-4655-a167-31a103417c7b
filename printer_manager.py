#!/usr/bin/env python3
"""
Advanced Printer Detection and Management System
Automatically detects printer types, connectivity, and optimizes Jathagam printing
"""
import json
import platform
import subprocess
import re
from typing import Dict, List, Optional, Tuple

class PrinterManager:
    """Comprehensive printer detection and management system"""
    
    def __init__(self):
        self.system = platform.system()
        self.detected_printers = []
        self.default_printer = None
        self.printer_capabilities = {}
    
    def detect_all_printers(self) -> List[Dict]:
        """Detect all available printers and their types"""
        try:
            if self.system == "Windows":
                return self._detect_windows_printers()
            elif self.system == "Darwin":  # macOS
                return self._detect_macos_printers()
            elif self.system == "Linux":
                return self._detect_linux_printers()
            else:
                return self._detect_generic_printers()
        except Exception as e:
            print(f"Printer detection error: {e}")
            return []
    
    def _detect_windows_printers(self) -> List[Dict]:
        """Detect Windows printers using WMI and PowerShell"""
        printers = []
        
        try:
            # PowerShell command to get detailed printer information
            ps_command = """
            Get-WmiObject -Class Win32_Printer | Select-Object Name, DriverName, PortName, PrinterStatus, WorkOffline, Local, Network, Shared, Default | ConvertTo-Json
            """
            
            result = subprocess.run(
                ["powershell", "-Command", ps_command],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and result.stdout:
                printer_data = json.loads(result.stdout)
                
                # Handle single printer (not in array)
                if isinstance(printer_data, dict):
                    printer_data = [printer_data]
                
                for printer in printer_data:
                    printer_info = {
                        'name': printer.get('Name', 'Unknown'),
                        'driver': printer.get('DriverName', 'Unknown'),
                        'port': printer.get('PortName', 'Unknown'),
                        'status': self._get_printer_status(printer.get('PrinterStatus', 0)),
                        'online': not printer.get('WorkOffline', True),
                        'local': printer.get('Local', False),
                        'network': printer.get('Network', False),
                        'shared': printer.get('Shared', False),
                        'default': printer.get('Default', False),
                        'type': self._determine_printer_type(printer),
                        'capabilities': self._get_printer_capabilities(printer.get('Name', ''))
                    }
                    printers.append(printer_info)
                    
                    if printer_info['default']:
                        self.default_printer = printer_info
            
        except Exception as e:
            print(f"Windows printer detection error: {e}")
            # Fallback to basic detection
            printers = self._detect_basic_windows_printers()
        
        self.detected_printers = printers
        return printers
    
    def _detect_basic_windows_printers(self) -> List[Dict]:
        """Basic Windows printer detection fallback"""
        printers = []
        try:
            result = subprocess.run(
                ["wmic", "printer", "get", "name,drivername,portname", "/format:csv"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        parts = line.split(',')
                        if len(parts) >= 4:
                            printer_info = {
                                'name': parts[2].strip(),
                                'driver': parts[1].strip(),
                                'port': parts[3].strip(),
                                'status': 'Unknown',
                                'online': True,
                                'local': True,
                                'network': False,
                                'shared': False,
                                'default': False,
                                'type': 'Unknown',
                                'capabilities': {}
                            }
                            printers.append(printer_info)
        except Exception as e:
            print(f"Basic Windows printer detection error: {e}")
        
        return printers
    
    def _detect_macos_printers(self) -> List[Dict]:
        """Detect macOS printers using lpstat and system_profiler"""
        printers = []
        
        try:
            # Get printer list
            result = subprocess.run(
                ["lpstat", "-p", "-d"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                default_printer = None
                
                for line in lines:
                    if line.startswith('system default destination:'):
                        default_printer = line.split(':')[1].strip()
                    elif line.startswith('printer '):
                        parts = line.split()
                        if len(parts) >= 2:
                            printer_name = parts[1]
                            status = 'idle' if 'idle' in line else 'unknown'
                            
                            printer_info = {
                                'name': printer_name,
                                'driver': 'Unknown',
                                'port': 'Unknown',
                                'status': status,
                                'online': 'idle' in line,
                                'local': True,
                                'network': False,
                                'shared': False,
                                'default': printer_name == default_printer,
                                'type': self._determine_macos_printer_type(printer_name),
                                'capabilities': self._get_macos_printer_capabilities(printer_name)
                            }
                            printers.append(printer_info)
                            
                            if printer_info['default']:
                                self.default_printer = printer_info
        
        except Exception as e:
            print(f"macOS printer detection error: {e}")
        
        self.detected_printers = printers
        return printers
    
    def _detect_linux_printers(self) -> List[Dict]:
        """Detect Linux printers using CUPS"""
        printers = []
        
        try:
            # Get printer list using lpstat
            result = subprocess.run(
                ["lpstat", "-p", "-d"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                default_printer = None
                
                for line in lines:
                    if 'system default destination:' in line:
                        default_printer = line.split(':')[1].strip()
                    elif line.startswith('printer '):
                        parts = line.split()
                        if len(parts) >= 2:
                            printer_name = parts[1]
                            status = 'idle' if 'idle' in line else 'unknown'
                            
                            printer_info = {
                                'name': printer_name,
                                'driver': 'Unknown',
                                'port': 'Unknown',
                                'status': status,
                                'online': 'idle' in line,
                                'local': True,
                                'network': False,
                                'shared': False,
                                'default': printer_name == default_printer,
                                'type': self._determine_linux_printer_type(printer_name),
                                'capabilities': self._get_linux_printer_capabilities(printer_name)
                            }
                            printers.append(printer_info)
                            
                            if printer_info['default']:
                                self.default_printer = printer_info
        
        except Exception as e:
            print(f"Linux printer detection error: {e}")
        
        self.detected_printers = printers
        return printers
    
    def _detect_generic_printers(self) -> List[Dict]:
        """Generic printer detection for unsupported systems"""
        return [{
            'name': 'Default Printer',
            'driver': 'Generic',
            'port': 'Unknown',
            'status': 'Unknown',
            'online': True,
            'local': True,
            'network': False,
            'shared': False,
            'default': True,
            'type': 'Generic',
            'capabilities': {'color': True, 'duplex': False, 'paper_sizes': ['A4']}
        }]
    
    def _get_printer_status(self, status_code: int) -> str:
        """Convert Windows printer status code to readable status"""
        status_map = {
            0: 'Unknown',
            1: 'Other',
            2: 'Unknown',
            3: 'Idle',
            4: 'Printing',
            5: 'Warmup',
            6: 'Stopped Printing',
            7: 'Offline'
        }
        return status_map.get(status_code, 'Unknown')
    
    def _determine_printer_type(self, printer_data: Dict) -> str:
        """Determine printer type from Windows printer data"""
        driver = printer_data.get('DriverName', '').lower()
        port = printer_data.get('PortName', '').lower()
        
        if 'pdf' in driver or 'xps' in driver:
            return 'Virtual'
        elif 'laser' in driver or 'hp' in driver:
            return 'Laser'
        elif 'inkjet' in driver or 'canon' in driver or 'epson' in driver:
            return 'Inkjet'
        elif 'dot' in driver or 'matrix' in driver:
            return 'Dot Matrix'
        elif 'thermal' in driver:
            return 'Thermal'
        elif 'usb' in port:
            return 'USB Printer'
        elif 'lpt' in port:
            return 'Parallel Printer'
        elif 'ip_' in port or 'tcp' in port:
            return 'Network Printer'
        else:
            return 'Unknown'
    
    def _determine_macos_printer_type(self, printer_name: str) -> str:
        """Determine macOS printer type"""
        name_lower = printer_name.lower()
        
        if 'hp' in name_lower and 'laser' in name_lower:
            return 'HP Laser'
        elif 'canon' in name_lower:
            return 'Canon Inkjet'
        elif 'epson' in name_lower:
            return 'Epson Inkjet'
        elif 'brother' in name_lower:
            return 'Brother Printer'
        else:
            return 'Unknown'
    
    def _determine_linux_printer_type(self, printer_name: str) -> str:
        """Determine Linux printer type"""
        return self._determine_macos_printer_type(printer_name)  # Similar logic
    
    def _get_printer_capabilities(self, printer_name: str) -> Dict:
        """Get Windows printer capabilities"""
        capabilities = {
            'color': False,
            'duplex': False,
            'paper_sizes': ['A4'],
            'max_resolution': '600x600',
            'supported_formats': ['Text', 'Image']
        }
        
        try:
            # Try to get detailed capabilities using PowerShell
            ps_command = f"""
            Get-PrinterProperty -PrinterName "{printer_name}" | Select-Object PropertyName, Value | ConvertTo-Json
            """
            
            result = subprocess.run(
                ["powershell", "-Command", ps_command],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and result.stdout:
                properties = json.loads(result.stdout)
                if isinstance(properties, dict):
                    properties = [properties]
                
                for prop in properties:
                    prop_name = prop.get('PropertyName', '').lower()
                    prop_value = prop.get('Value', '')
                    
                    if 'color' in prop_name:
                        capabilities['color'] = 'true' in str(prop_value).lower()
                    elif 'duplex' in prop_name:
                        capabilities['duplex'] = 'true' in str(prop_value).lower()
        
        except Exception as e:
            print(f"Capability detection error: {e}")
        
        return capabilities
    
    def _get_macos_printer_capabilities(self, printer_name: str) -> Dict:
        """Get macOS printer capabilities"""
        return {
            'color': True,
            'duplex': False,
            'paper_sizes': ['A4', 'Letter'],
            'max_resolution': '600x600',
            'supported_formats': ['PDF', 'PostScript', 'Text']
        }
    
    def _get_linux_printer_capabilities(self, printer_name: str) -> Dict:
        """Get Linux printer capabilities"""
        return self._get_macos_printer_capabilities(printer_name)
    
    def check_printer_connectivity(self, printer_name: str) -> Dict:
        """Check if a specific printer is connected and ready"""
        connectivity_status = {
            'connected': False,
            'online': False,
            'ready': False,
            'error_message': None,
            'last_checked': None
        }
        
        try:
            if self.system == "Windows":
                connectivity_status = self._check_windows_printer_connectivity(printer_name)
            elif self.system in ["Darwin", "Linux"]:
                connectivity_status = self._check_unix_printer_connectivity(printer_name)
            
            connectivity_status['last_checked'] = self._get_current_timestamp()
        
        except Exception as e:
            connectivity_status['error_message'] = str(e)
        
        return connectivity_status
    
    def _check_windows_printer_connectivity(self, printer_name: str) -> Dict:
        """Check Windows printer connectivity"""
        try:
            ps_command = f"""
            Get-Printer -Name "{printer_name}" | Select-Object PrinterStatus, WorkOffline | ConvertTo-Json
            """
            
            result = subprocess.run(
                ["powershell", "-Command", ps_command],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and result.stdout:
                printer_status = json.loads(result.stdout)
                
                return {
                    'connected': True,
                    'online': not printer_status.get('WorkOffline', True),
                    'ready': printer_status.get('PrinterStatus') == 3,  # 3 = Idle
                    'error_message': None
                }
        
        except Exception as e:
            return {
                'connected': False,
                'online': False,
                'ready': False,
                'error_message': str(e)
            }
    
    def _check_unix_printer_connectivity(self, printer_name: str) -> Dict:
        """Check Unix (macOS/Linux) printer connectivity"""
        try:
            result = subprocess.run(
                ["lpstat", "-p", printer_name],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                output = result.stdout.lower()
                return {
                    'connected': True,
                    'online': 'idle' in output or 'printing' in output,
                    'ready': 'idle' in output,
                    'error_message': None
                }
            else:
                return {
                    'connected': False,
                    'online': False,
                    'ready': False,
                    'error_message': 'Printer not found'
                }
        
        except Exception as e:
            return {
                'connected': False,
                'online': False,
                'ready': False,
                'error_message': str(e)
            }
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def get_optimal_print_settings(self, printer_info: Dict, content_type: str = 'jathagam') -> Dict:
        """Get optimal print settings for Jathagam content"""
        settings = {
            'paper_size': 'A4',
            'orientation': 'portrait',
            'margins': {'top': 20, 'bottom': 20, 'left': 15, 'right': 15},
            'color': False,
            'quality': 'normal',
            'duplex': False,
            'scale': 100
        }
        
        printer_type = printer_info.get('type', '').lower()
        capabilities = printer_info.get('capabilities', {})
        
        # Optimize based on printer type
        if 'laser' in printer_type:
            settings['quality'] = 'high'
            settings['color'] = capabilities.get('color', False)
        elif 'inkjet' in printer_type:
            settings['quality'] = 'normal'
            settings['color'] = capabilities.get('color', False)
        elif 'thermal' in printer_type:
            settings['paper_size'] = 'Custom'
            settings['quality'] = 'draft'
        
        # Optimize for Jathagam content
        if content_type == 'jathagam':
            settings['margins'] = {'top': 15, 'bottom': 15, 'left': 10, 'right': 10}
            settings['orientation'] = 'portrait'
            if capabilities.get('color', False):
                settings['color'] = True  # Use color for better chart visibility
        
        return settings

# Test function
if __name__ == "__main__":
    print("🖨️ Testing Printer Detection System...")
    
    manager = PrinterManager()
    printers = manager.detect_all_printers()
    
    print(f"\n📊 Detected {len(printers)} printer(s):")
    for i, printer in enumerate(printers, 1):
        print(f"\n{i}. {printer['name']}")
        print(f"   Type: {printer['type']}")
        print(f"   Status: {printer['status']}")
        print(f"   Online: {printer['online']}")
        print(f"   Default: {printer['default']}")
        
        # Check connectivity
        connectivity = manager.check_printer_connectivity(printer['name'])
        print(f"   Connected: {connectivity['connected']}")
        print(f"   Ready: {connectivity['ready']}")
        
        # Get optimal settings
        settings = manager.get_optimal_print_settings(printer, 'jathagam')
        print(f"   Optimal Settings: {settings['paper_size']}, {settings['quality']}")
    
    if manager.default_printer:
        print(f"\n🎯 Default Printer: {manager.default_printer['name']}")
    else:
        print("\n⚠️ No default printer found")
    
    print("\n✅ Printer detection test completed!")
