#!/usr/bin/env python3
"""
Test to verify that all scanner functions have been removed
from admin side, user side, and Python code
"""
import os
import sys
import requests

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_admin_dashboard_scanner_removal():
    """Test that scanner functions are removed from admin dashboard"""
    print("=== Testing Admin Dashboard Scanner Removal ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Access admin dashboard
        dashboard_response = session.get('http://localhost:5000/admin_dashboard')
        
        if dashboard_response.status_code == 200:
            dashboard_html = dashboard_response.text
            
            # Check that scanner functions are removed
            removed_functions = [
                'ஆவணம் ஸ்கேன் செய்',
                'ஸ்கேனர் பிரிண்டர்',
                'showAdvancedScannerModal',
                'showScannerPrinterModal',
                'client_side_scanner.js',
                'advanced_scanner_system.js',
                'scanner_printer_system.js',
                'Scanner Printer System',
                'Advanced Document Scanner'
            ]
            
            found_removed = []
            for function in removed_functions:
                if function in dashboard_html:
                    found_removed.append(function)
            
            print(f"❌ Scanner functions still found: {len(found_removed)}/{len(removed_functions)}")
            for function in found_removed:
                print(f"   ⚠️ Still present: {function}")
            
            # Check that only valid functions remain
            valid_functions = [
                'ஜாதகம் உருவாக்கு',
                'ஜாதக பட்டியல்',
                'showJathagamGenerationModal',
                'showJathagamListModal'
            ]
            
            found_valid = []
            for function in valid_functions:
                if function in dashboard_html:
                    found_valid.append(function)
            
            print(f"✅ Valid functions remaining: {len(found_valid)}/{len(valid_functions)}")
            for function in found_valid:
                print(f"   ✓ {function}")
            
            if len(found_removed) == 0 and len(found_valid) >= 3:
                print("✅ Admin dashboard scanner removal successful!")
                return True
            else:
                print("⚠️ Admin dashboard scanner removal incomplete")
                return False
        else:
            print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Admin dashboard test error: {e}")
        return False

def test_user_dashboard_scanner_removal():
    """Test that scanner functions are removed from user dashboard"""
    print("\n=== Testing User Dashboard Scanner Removal ===")
    
    try:
        session = requests.Session()
        
        # Login as user
        login_data = {
            'username': 'user',
            'password': 'user123'
        }
        session.post('http://localhost:5000/login', data=login_data)
        
        # Access user dashboard
        dashboard_response = session.get('http://localhost:5000/user_dashboard')
        
        if dashboard_response.status_code == 200:
            dashboard_html = dashboard_response.text
            
            # Check that scanner functions are removed
            removed_functions = [
                'ஜாதகம் ஸ்கேன் செய்',
                'showUserJathagamScannerModal',
                'startUserFileUploadScan',
                'uploadUserScannedDocument',
                'client_side_scanner.js',
                'user-jathagam-scanner-modal'
            ]
            
            found_removed = []
            for function in removed_functions:
                if function in dashboard_html:
                    found_removed.append(function)
            
            print(f"❌ Scanner functions still found: {len(found_removed)}/{len(removed_functions)}")
            for function in found_removed:
                print(f"   ⚠️ Still present: {function}")
            
            # Check that only valid functions remain
            valid_functions = [
                'ஜாதகம் உருவாக்கு',
                'showUserJathagamModal'
            ]
            
            found_valid = []
            for function in valid_functions:
                if function in dashboard_html:
                    found_valid.append(function)
            
            print(f"✅ Valid functions remaining: {len(found_valid)}/{len(valid_functions)}")
            for function in found_valid:
                print(f"   ✓ {function}")
            
            if len(found_removed) == 0 and len(found_valid) >= 2:
                print("✅ User dashboard scanner removal successful!")
                return True
            else:
                print("⚠️ User dashboard scanner removal incomplete")
                return False
        else:
            print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ User dashboard test error: {e}")
        return False

def test_javascript_files_removal():
    """Test that scanner JavaScript files are removed"""
    print("\n=== Testing JavaScript Files Removal ===")
    
    try:
        # Check that scanner JavaScript files are removed
        removed_files = [
            'static/js/client_side_scanner.js',
            'static/js/advanced_scanner_system.js',
            'static/js/scanner_printer_system.js'
        ]
        
        still_exist = []
        for file_path in removed_files:
            if os.path.exists(file_path):
                still_exist.append(file_path)
        
        print(f"❌ Scanner JS files still exist: {len(still_exist)}/{len(removed_files)}")
        for file_path in still_exist:
            print(f"   ⚠️ Still exists: {file_path}")
        
        # Check that valid JavaScript files remain
        valid_files = [
            'static/js/enhanced_print_system.js'
        ]
        
        still_valid = []
        for file_path in valid_files:
            if os.path.exists(file_path):
                still_valid.append(file_path)
        
        print(f"✅ Valid JS files remaining: {len(still_valid)}/{len(valid_files)}")
        for file_path in still_valid:
            print(f"   ✓ {file_path}")
        
        if len(still_exist) == 0 and len(still_valid) >= 1:
            print("✅ JavaScript files removal successful!")
            return True
        else:
            print("⚠️ JavaScript files removal incomplete")
            return False
            
    except Exception as e:
        print(f"❌ JavaScript files test error: {e}")
        return False

def test_test_files_removal():
    """Test that scanner test files are removed"""
    print("\n=== Testing Test Files Removal ===")
    
    try:
        # Check that scanner test files are removed
        removed_test_files = [
            'test_client_side_scanner_system.py',
            'test_advanced_scanner_system.py',
            'test_scanner_printer_system.py'
        ]
        
        still_exist = []
        for file_path in removed_test_files:
            if os.path.exists(file_path):
                still_exist.append(file_path)
        
        print(f"❌ Scanner test files still exist: {len(still_exist)}/{len(removed_test_files)}")
        for file_path in still_exist:
            print(f"   ⚠️ Still exists: {file_path}")
        
        if len(still_exist) == 0:
            print("✅ Test files removal successful!")
            return True
        else:
            print("⚠️ Test files removal incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Test files removal error: {e}")
        return False

def test_documentation_files_removal():
    """Test that scanner documentation files are removed"""
    print("\n=== Testing Documentation Files Removal ===")
    
    try:
        # Check that scanner documentation files are removed
        removed_doc_files = [
            'CLIENT_SIDE_SCANNER_FINAL_SUMMARY.md',
            'ADVANCED_SCANNER_SYSTEM_SUMMARY.md',
            'SCANNER_PRINTER_SYSTEM_SUMMARY.md'
        ]
        
        still_exist = []
        for file_path in removed_doc_files:
            if os.path.exists(file_path):
                still_exist.append(file_path)
        
        print(f"❌ Scanner doc files still exist: {len(still_exist)}/{len(removed_doc_files)}")
        for file_path in still_exist:
            print(f"   ⚠️ Still exists: {file_path}")
        
        if len(still_exist) == 0:
            print("✅ Documentation files removal successful!")
            return True
        else:
            print("⚠️ Documentation files removal incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Documentation files removal error: {e}")
        return False

def test_remaining_functionality():
    """Test that remaining functionality still works"""
    print("\n=== Testing Remaining Functionality ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        login_response = session.post('http://localhost:5000/login', data=login_data)
        
        if login_response.status_code == 200:
            # Access admin dashboard
            dashboard_response = session.get('http://localhost:5000/admin_dashboard')
            
            if dashboard_response.status_code == 200:
                dashboard_html = dashboard_response.text
                
                # Check that core functionality remains
                core_functions = [
                    'ஜாதகம் உருவாக்கு',
                    'ஜாதக பட்டியல்',
                    'enhanced_print_system.js'
                ]
                
                found_core = []
                for function in core_functions:
                    if function in dashboard_html:
                        found_core.append(function)
                
                print(f"✅ Core functions working: {len(found_core)}/{len(core_functions)}")
                for function in found_core:
                    print(f"   ✓ {function}")
                
                if len(found_core) >= 3:
                    print("✅ Remaining functionality works correctly!")
                    return True
                else:
                    print("⚠️ Some core functionality may be broken")
                    return False
            else:
                print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
                return False
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Remaining functionality test error: {e}")
        return False

def main():
    """Run all scanner removal tests"""
    print("🗑️ Testing Scanner Functions Removal...")
    print("Verifying removal from admin side, user side, and Python code\n")
    
    tests = [
        test_admin_dashboard_scanner_removal,
        test_user_dashboard_scanner_removal,
        test_javascript_files_removal,
        test_test_files_removal,
        test_documentation_files_removal,
        test_remaining_functionality
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print(f"\n📊 Scanner Removal Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All scanner functions successfully removed!")
        print("\n✅ Removal Summary:")
        print("  🗑️ Admin Dashboard: All scanner options removed")
        print("  🗑️ User Dashboard: All scanner options removed")
        print("  🗑️ JavaScript Files: All scanner JS files removed")
        print("  🗑️ Test Files: All scanner test files removed")
        print("  🗑️ Documentation: All scanner documentation removed")
        print("  ✅ Core Functionality: Jathagam generation and printing still work")
        print("\n🌟 System is now clean with only core Jathagam functionality!")
    else:
        print("⚠️ Some scanner removal tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
