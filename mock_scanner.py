import os
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime

def create_mock_scan():
    """Create a mock scanned document for testing"""
    # Create a simple image that looks like a scanned document
    width, height = 800, 1000
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    try:
        # Try to use a default font
        font = ImageFont.truetype("arial.ttf", 40)
        small_font = ImageFont.truetype("arial.ttf", 20)
    except:
        # Fallback to default font if arial is not available
        font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # Draw document content
    draw.text((50, 100), "MOCK SCANNED DOCUMENT", fill='black', font=font)
    draw.text((50, 200), "This is a simulated scan for testing", fill='black', font=small_font)
    draw.text((50, 250), "Date: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'), fill='black', font=small_font)
    
    # Add some lines to make it look more like a document
    for i in range(10):
        y = 350 + i * 50
        draw.line([(50, y), (750, y)], fill='lightgray', width=1)
    
    # Save the mock scan
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"scanned_{timestamp}.png"
    filepath = os.path.join('scanned', filename)
    
    # Ensure scanned directory exists
    os.makedirs('scanned', exist_ok=True)
    
    image.save(filepath)
    return filename

if __name__ == "__main__":
    filename = create_mock_scan()
    print(f"Mock scan created: {filename}")
