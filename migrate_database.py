#!/usr/bin/env python3
"""
Database Migration Script
Fixes relative file paths and cleans up missing files
"""
import os
import sys
from pathlib import Path

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import db, Document, User

def migrate_database():
    """Migrate database to fix file path issues"""
    with app.app_context():
        print("=== DATABASE MIGRATION STARTED ===")
        
        # Get all documents with issues
        all_docs = Document.query.all()
        migrated_count = 0
        deleted_count = 0
        
        for doc in all_docs:
            # Check if file path is relative
            if not os.path.isabs(doc.file_path):
                print(f"📝 Processing: {doc.filename}")
                
                # Try to convert to absolute path
                abs_path = os.path.abspath(doc.file_path)
                
                # Check if file exists
                if os.path.exists(abs_path):
                    print(f"   ✅ Converting to absolute path: {abs_path}")
                    doc.file_path = abs_path
                    migrated_count += 1
                else:
                    print(f"   ❌ File not found, removing database record: {doc.file_path}")
                    # Remove document record since file doesn't exist
                    db.session.delete(doc)
                    deleted_count += 1
            
            # For absolute paths, check if file still exists
            elif os.path.isabs(doc.file_path) and not os.path.exists(doc.file_path):
                print(f"📝 Checking absolute path: {doc.filename}")
                print(f"   ❌ File not found, removing database record: {doc.file_path}")
                db.session.delete(doc)
                deleted_count += 1
        
        # Commit changes
        try:
            db.session.commit()
            print(f"\n✅ MIGRATION COMPLETED:")
            print(f"   • {migrated_count} documents migrated to absolute paths")
            print(f"   • {deleted_count} orphaned records removed")
            
            # Show final statistics
            final_count = Document.query.count()
            uploaded_count = Document.query.filter_by(document_type='uploaded').count()
            scanned_count = Document.query.filter_by(document_type='scanned').count()
            
            print(f"\n📊 Final Statistics:")
            print(f"   • Total Documents: {final_count}")
            print(f"   • Uploaded Documents: {uploaded_count}")
            print(f"   • Scanned Documents: {scanned_count}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Migration failed: {e}")

if __name__ == "__main__":
    migrate_database()
