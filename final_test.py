#!/usr/bin/env python3
"""
Final test of upload functionality after migration
"""
import os
import sys
from werkzeug.datastructures import FileStorage
from io import BytesIO

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import db, User, Document

def final_upload_test():
    """Final test of the scanner-first workflow"""
    with app.app_context():
        print("=== FINAL SCANNER-FIRST WORKFLOW TEST ===")
        
        # Count before
        users_before = User.query.count()
        docs_before = Document.query.count()
        print(f"Before: {users_before} users, {docs_before} documents")
        
        # Test file content
        test_content = "David <PERSON>,Boston\nEmma Wilson,Portland\nFrank Miller,Denver"
        
        # Create a file-like object
        file_data = BytesIO(test_content.encode('utf-8'))
        file_storage = FileStorage(
            stream=file_data,
            filename='final_test.csv',
            content_type='text/csv'
        )
        
        # Test the new scanner-first workflow
        with app.test_client() as client:
            # Step 1: Upload document
            response = client.post('/upload_document', 
                                 data={'file': file_storage},
                                 content_type='multipart/form-data')
            
            print(f"Upload response: {response.status_code}")
            
            if response.status_code == 200:
                upload_data = response.get_json()
                print(f"Upload data: {upload_data}")
                
                if upload_data and upload_data.get('success'):
                    filename = upload_data.get('filename')
                    
                    # Step 2: Submit user details with document
                    user_response = client.post('/', data={
                        'name': 'Test Final User',
                        'city': 'Test Final City', 
                        'document_path': filename,
                        'document_type': 'uploaded'
                    })
                    print(f"User submission response: {user_response.status_code}")
        
        # Count after
        users_after = User.query.count()
        docs_after = Document.query.count()
        print(f"After: {users_after} users, {docs_after} documents")
        print(f"Added: {users_after - users_before} users, {docs_after - docs_before} documents")
        
        # Check the new documents
        new_docs = Document.query.filter_by(document_type='uploaded').order_by(Document.created_at.desc()).limit(3).all()
        print(f"\nNew uploaded documents:")
        for doc in new_docs:
            user_name = doc.user.name if doc.user else "No User"
            exists = os.path.exists(doc.file_path)
            is_absolute = os.path.isabs(doc.file_path)
            print(f"  • {doc.filename} -> {user_name}")
            print(f"    Path: {doc.file_path}")
            print(f"    Absolute: {is_absolute}, Exists: {exists}")
        
        print("\n✅ Final scanner-first workflow test completed!")

if __name__ == "__main__":
    final_upload_test()
