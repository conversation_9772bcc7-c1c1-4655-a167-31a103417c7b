#!/usr/bin/env python3
"""
Enhanced Tamil Nadu Jathagam Generator
Integrates Tamil calendar, Panchangam, and Swiss Ephemeris
Following the hybrid architecture: Tamil-specific + Global accuracy
"""
from tamil_nadu_jathagam_generator import TamilNaduJathagamGenerator
from tamil_calendar_integration import TamilCalendarIntegration
from datetime import datetime, date, time
from typing import Dict, List, Optional
import json

class EnhancedTamilJathagamGenerator:
    """
    Enhanced Tamil Jathagam generator with complete Tamil calendar integration
    Combines Swiss Ephemeris accuracy with Tamil traditional methods
    """
    
    def __init__(self):
        # Initialize core components
        self.jathagam_generator = TamilNaduJathagamGenerator()
        self.tamil_calendar = TamilCalendarIntegration()
        
        # Dasa system periods (Vimshottari Dasa in years)
        self.vimshottari_periods = {
            'கேது': 7,      # Ketu - 7 years
            'சுக்கிரன்': 20,  # Venus - 20 years
            'சூரியன்': 6,    # Sun - 6 years
            'சந்திரன்': 10,  # Moon - 10 years
            'செவ்வாய்': 7,   # Mars - 7 years
            'ராகு': 18,      # Rahu - 18 years
            'குரு': 16,      # Jupiter - 16 years
            'சனி': 19,       # Saturn - 19 years
            'புதன்': 17      # Mercury - 17 years
        }
        
        # Naks<PERSON>ra to <PERSON>a lord mapping
        self.nakshatra_dasa_lords = [
            'கேது', 'சுக்கிரன்', 'சூரியன்',     # Ashwini, Bharani, Krittika
            'சந்திரன்', 'செவ்வாய்', 'ராகு',      # Rohini, Mrigashira, Ardra
            'குரு', 'சனி', 'புதன்',             # Punarvasu, Pushya, Ashlesha
            'கேது', 'சுக்கிரன்', 'சூரியன்',     # Magha, Purva Phalguni, Uttara Phalguni
            'சந்திரன்', 'செவ்வாய்', 'ராகு',      # Hasta, Chitra, Swati
            'குரு', 'சனி', 'புதன்',             # Vishakha, Anuradha, Jyeshtha
            'கேது', 'சுக்கிரன்', 'சூரியன்',     # Mula, Purva Ashadha, Uttara Ashadha
            'சந்திரன்', 'செவ்வாய்', 'ராகு',      # Shravana, Dhanishta, Shatabhisha
            'குரு', 'சனி', 'புதன்'              # Purva Bhadrapada, Uttara Bhadrapada, Revati
        ]
    
    def generate_complete_enhanced_jathagam(self, name: str, birth_date: date, birth_time: time,
                                          birth_place: str, gender: str = 'Male') -> Dict:
        """
        Generate complete enhanced Tamil Jathagam with Panchangam and Dasa
        """
        try:
            # Step 1: Generate basic Jathagam using Swiss Ephemeris
            basic_jathagam = self.jathagam_generator.generate_complete_jathagam(
                name, birth_date, birth_time, birth_place, gender
            )
            
            if not basic_jathagam:
                return None
            
            # Step 2: Get coordinates for Panchangam calculation
            personal_details = basic_jathagam.get('personal_details', {})
            latitude = personal_details.get('latitude', 13.0827)
            longitude = personal_details.get('longitude', 80.2707)
            
            # Step 3: Generate Tamil Panchangam
            panchangam_data = self.tamil_calendar.generate_complete_panchangam(
                birth_date, birth_time, latitude, longitude, birth_place
            )
            
            # Step 4: Calculate Vimshottari Dasa
            dasa_data = self.calculate_vimshottari_dasa(basic_jathagam, birth_date)
            
            # Step 5: Get Muhurtham times
            muhurtham_times = self.tamil_calendar.get_muhurtham_times(
                birth_date, latitude, longitude
            )
            
            # Step 6: Generate planet table
            planet_table = self.jathagam_generator.get_planet_table_data(
                basic_jathagam.get('planetary_positions', {})
            )
            
            # Step 7: Combine all data
            enhanced_jathagam = {
                **basic_jathagam,  # Include all basic Jathagam data
                'panchangam_details': panchangam_data,
                'dasa_system': dasa_data,
                'muhurtham_times': muhurtham_times,
                'planet_table': planet_table,
                'enhanced_features': {
                    'tamil_calendar_integration': True,
                    'panchangam_included': True,
                    'dasa_system_included': True,
                    'muhurtham_calculation': True,
                    'festival_information': len(panchangam_data.get('festivals', [])) > 0
                },
                'software_info': 'Enhanced Tamil Nadu Jathagam Generator - Complete Tamil Calendar Integration'
            }
            
            return enhanced_jathagam
            
        except Exception as e:
            print(f"Enhanced Jathagam generation error: {e}")
            return basic_jathagam if 'basic_jathagam' in locals() else None
    
    def calculate_vimshottari_dasa(self, jathagam_data: Dict, birth_date: date) -> Dict:
        """
        Calculate Vimshottari Dasa system based on Moon's Nakshatra
        """
        try:
            # Get Moon's nakshatra from planetary positions
            moon_data = jathagam_data.get('planetary_positions', {}).get('Moon', {})
            nakshatra_number = moon_data.get('nakshatra_number', 1)
            
            # Get Dasa lord for Moon's nakshatra
            dasa_lord_index = (nakshatra_number - 1) % 9
            current_dasa_lord = self.nakshatra_dasa_lords[dasa_lord_index]
            
            # Calculate remaining period in current Dasa
            # This is a simplified calculation - in practice, you'd need more precise timing
            nakshatra_progress = moon_data.get('degrees_in_sign', 0) % (360/27)
            nakshatra_completion = nakshatra_progress / (360/27)
            
            current_dasa_period = self.vimshottari_periods[current_dasa_lord]
            remaining_years = current_dasa_period * (1 - nakshatra_completion)
            
            # Generate Dasa sequence
            dasa_sequence = self._generate_dasa_sequence(current_dasa_lord, birth_date, remaining_years)
            
            dasa_data = {
                'current_dasa_lord': current_dasa_lord,
                'remaining_years': round(remaining_years, 2),
                'moon_nakshatra': moon_data.get('nakshatra_name', 'அசுவினி'),
                'dasa_sequence': dasa_sequence,
                'calculation_method': 'Vimshottari Dasa (120 years cycle)',
                'note': 'தசா கணக்கீடு சந்திர நட்சத்திர அடிப்படையில்'
            }
            
            return dasa_data
            
        except Exception as e:
            print(f"Dasa calculation error: {e}")
            return {
                'current_dasa_lord': 'குரு',
                'remaining_years': 16,
                'moon_nakshatra': 'அசுவினி',
                'dasa_sequence': [],
                'calculation_method': 'Default (calculation error)',
                'note': 'தசா கணக்கீட்டில் பிழை'
            }
    
    def _generate_dasa_sequence(self, starting_lord: str, birth_date: date, remaining_years: float) -> List[Dict]:
        """Generate Dasa sequence starting from current lord"""
        dasa_sequence = []
        current_date = birth_date
        
        # Get the order of planets in Vimshottari system
        planet_order = list(self.vimshottari_periods.keys())
        start_index = planet_order.index(starting_lord)
        
        # Add current Dasa (remaining period)
        if remaining_years > 0:
            end_year = current_date.year + int(remaining_years)
            end_month = current_date.month + int((remaining_years % 1) * 12)
            if end_month > 12:
                end_year += 1
                end_month -= 12
            
            dasa_sequence.append({
                'lord': starting_lord,
                'period_years': round(remaining_years, 1),
                'start_date': current_date.strftime('%Y-%m-%d'),
                'end_date': f"{end_year}-{end_month:02d}-{current_date.day:02d}",
                'status': 'current'
            })
            
            current_date = date(end_year, end_month, current_date.day)
        
        # Add next 5 Dasa periods
        for i in range(1, 6):
            planet_index = (start_index + i) % len(planet_order)
            planet = planet_order[planet_index]
            period_years = self.vimshottari_periods[planet]
            
            end_year = current_date.year + period_years
            
            dasa_sequence.append({
                'lord': planet,
                'period_years': period_years,
                'start_date': current_date.strftime('%Y-%m-%d'),
                'end_date': f"{end_year}-{current_date.month:02d}-{current_date.day:02d}",
                'status': 'future'
            })
            
            current_date = date(end_year, current_date.month, current_date.day)
        
        return dasa_sequence
    
    def format_enhanced_jathagam_for_display(self, enhanced_jathagam: Dict) -> str:
        """
        Format enhanced Jathagam data for display
        """
        if not enhanced_jathagam:
            return "மேம்பட்ட ஜாதக தகவல் கிடைக்கவில்லை"
        
        # Get basic Jathagam formatted text
        basic_formatted = self.jathagam_generator.format_for_display(enhanced_jathagam)
        
        # Add Panchangam information
        panchangam_data = enhanced_jathagam.get('panchangam_details', {})
        panchangam_formatted = self.tamil_calendar.format_panchangam_for_display(panchangam_data)
        
        # Add Dasa information
        dasa_data = enhanced_jathagam.get('dasa_system', {})
        dasa_formatted = self._format_dasa_for_display(dasa_data)
        
        # Combine all information
        enhanced_formatted = f"""
{basic_formatted}

{panchangam_formatted}

{dasa_formatted}

🌟 மேம்பட்ட அம்சங்கள்:
• தமிழ் நாட்காட்டி ஒருங்கிணைப்பு: ✅
• பஞ்சாங்க விவரங்கள்: ✅
• விம்சோத்தரி தசா முறை: ✅
• முகூர்த்த கணக்கீடு: ✅
• பண்டிகை தகவல்கள்: ✅

📊 இந்த மேம்பட்ட ஜாதகம் Swiss Ephemeris துல்லியத்துடன் தமிழ் பாரம்பரிய முறைகளை ஒருங்கிணைக்கிறது.
"""
        
        return enhanced_formatted
    
    def _format_dasa_for_display(self, dasa_data: Dict) -> str:
        """Format Dasa data for display"""
        if not dasa_data:
            return "📊 தசா தகவல்கள்: கிடைக்கவில்லை"
        
        current_lord = dasa_data.get('current_dasa_lord', 'தெரியவில்லை')
        remaining_years = dasa_data.get('remaining_years', 0)
        moon_nakshatra = dasa_data.get('moon_nakshatra', 'தெரியவில்லை')
        
        dasa_text = f"""
📊 விம்சோத்தரி தசா முறை:
• தற்போதைய தசா: {current_lord} ({remaining_years} ஆண்டுகள் மீதம்)
• சந்திர நட்சத்திரம்: {moon_nakshatra}
• கணக்கீட்டு முறை: {dasa_data.get('calculation_method', 'Vimshottari')}

🔮 அடுத்த தசா வரிசை:"""
        
        dasa_sequence = dasa_data.get('dasa_sequence', [])
        for i, dasa in enumerate(dasa_sequence[:3]):  # Show first 3
            status_icon = "🔴" if dasa['status'] == 'current' else "🔵"
            dasa_text += f"\n• {status_icon} {dasa['lord']}: {dasa['start_date']} முதல் {dasa['end_date']} வரை ({dasa['period_years']} ஆண்டுகள்)"
        
        return dasa_text

# Test function
if __name__ == "__main__":
    print("🌟 Testing Enhanced Tamil Jathagam Generator...")
    print("Integrating Tamil calendar, Panchangam, and Dasa systems\n")
    
    # Test data
    test_name = "மேம்பட்ட தமிழ் சோதனை"
    test_date = date(1990, 8, 15)
    test_time = time(10, 30)
    test_place = "சென்னை"
    test_gender = "Male"
    
    # Generate enhanced Jathagam
    generator = EnhancedTamilJathagamGenerator()
    enhanced_jathagam = generator.generate_complete_enhanced_jathagam(
        test_name, test_date, test_time, test_place, test_gender
    )
    
    if enhanced_jathagam:
        print("✅ Enhanced Tamil Jathagam generation successful!")
        
        # Display key information
        personal = enhanced_jathagam.get('personal_details', {})
        panchangam = enhanced_jathagam.get('panchangam_details', {})
        dasa = enhanced_jathagam.get('dasa_system', {})
        
        print(f"   Name: {personal.get('name')}")
        print(f"   Tamil Date: {panchangam.get('date_info', {}).get('tamil_date')}")
        print(f"   Current Dasa: {dasa.get('current_dasa_lord')}")
        print(f"   Festivals: {len(panchangam.get('festivals', []))}")
        
        # Display formatted text
        formatted_text = generator.format_enhanced_jathagam_for_display(enhanced_jathagam)
        print("\n" + "="*60)
        print(formatted_text)
        
    else:
        print("❌ Enhanced Jathagam generation failed")
    
    print("\n🎉 Enhanced Tamil Jathagam Generator test completed!")
