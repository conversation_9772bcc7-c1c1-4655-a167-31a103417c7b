<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ user.name }}'s Documents - User Data Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header h1 {
            color: #333;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .user-info {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .user-info h2 {
            margin-bottom: 10px;
        }

        .nav-buttons {
            margin-top: 20px;
        }

        .nav-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            margin: 0 10px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }

        .document-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .document-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .document-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 15px;
            border: 2px solid #e1e5e9;
        }

        .document-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            border: 2px solid #e1e5e9;
        }

        .file-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }

        .file-type {
            font-size: 14px;
            font-weight: 600;
            color: #666;
        }

        .document-info {
            margin-bottom: 15px;
        }

        .document-filename {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            margin: 5px 0;
            display: inline-block;
        }

        .document-date {
            color: #999;
            font-size: 12px;
            margin: 5px 0;
        }

        .document-type {
            background: #e8f4fd;
            color: #1976d2;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-top: 8px;
            display: inline-block;
        }

        .scanned-doc .document-type {
            background: #e8f5e8;
            color: #2e7d2e;
        }

        .uploaded-doc .document-type {
            background: #fff3e0;
            color: #f57c00;
        }

        .document-actions {
            padding: 15px;
            text-align: center;
            border-top: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 0 0 15px 15px;
            margin-top: 10px;
        }

        .view-btn {
            background: linear-gradient(45deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(72, 187, 120, 0.3);
        }

        .view-btn:hover {
            background: linear-gradient(45deg, #38a169, #2f855a);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
        }

        .empty-state {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .stats {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header, .stats {
                padding: 20px;
            }
            
            .documents-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .nav-btn {
                display: block;
                margin: 5px 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 {{ user.name }}'s Documents</h1>
            <div class="user-info">
                <h2>👤 {{ user.name }}</h2>
                <p>📍 {{ user.city }}</p>
                <p>📅 Added: {{ user.added_at }}</p>
                <p>📋 Source: {{ user.source }}</p>
            </div>
            <div class="nav-buttons">
                <a href="/" class="nav-btn">🏠 Home</a>
                <a href="/documents" class="nav-btn">📄 All Documents</a>
                <a href="/api/users" class="nav-btn">👥 All Users</a>
            </div>
        </div>

        <div class="stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">{{ documents|length }}</span>
                    <span class="stat-label">Total Documents</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ documents|selectattr('document_type', 'equalto', 'scanned')|list|length }}</span>
                    <span class="stat-label">Scanned Documents</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ documents|selectattr('document_type', 'equalto', 'uploaded')|list|length }}</span>
                    <span class="stat-label">Uploaded Documents</span>
                </div>
            </div>
        </div>

        <div class="documents-grid">
            {% if documents %}
                {% for doc in documents %}
                    <div class="document-card {{ doc.document_type }}-doc">
                        {% if doc.document_type == 'scanned' %}
                            <img src="/static/scanned/{{ doc.filename }}" 
                                 alt="Scanned Document" 
                                 class="document-image"
                                 onerror="this.style.display='none'">
                        {% elif doc.file_type in ['.png', '.jpg', '.jpeg'] %}
                            <img src="/static/uploads/{{ doc.filename }}" 
                                 alt="Uploaded Document" 
                                 class="document-image"
                                 onerror="this.style.display='none'">
                        {% else %}
                            <div class="document-placeholder">
                                <div class="file-icon">
                                    {% if doc.file_type == '.pdf' %}📄
                                    {% elif doc.file_type in ['.txt', '.csv'] %}📝
                                    {% elif doc.file_type == '.json' %}⚙️
                                    {% elif doc.file_type == '.xml' %}🗂️
                                    {% else %}📋{% endif %}
                                </div>
                                <div class="file-type">{{ doc.file_type.upper() }} File</div>
                            </div>
                        {% endif %}
                        
                        <div class="document-info">
                            <div class="document-filename">📄 {{ doc.original_filename or doc.filename }}</div>
                            <div class="document-date">📅 {{ doc.document_type|title }}: {{ doc.created_at }}</div>
                            {% if doc.file_size %}
                                <div class="document-date">📏 {{ "%.1f"|format(doc.file_size/1024) }} KB</div>
                            {% endif %}
                            <div class="document-type">
                                {% if doc.document_type == 'scanned' %}📷 Scanned Document
                                {% else %}📁 Uploaded Document{% endif %}
                            </div>
                        </div>
                        
                        <div class="document-actions">
                            {% if doc.document_type == 'scanned' %}
                                <a href="/static/scanned/{{ doc.filename }}" target="_blank" class="view-btn">👁️ View Document</a>
                            {% else %}
                                <a href="/static/uploads/{{ doc.filename }}" target="_blank" class="view-btn">👁️ View Document</a>
                                <a href="/static/uploads/{{ doc.filename }}" download="{{ doc.original_filename }}" class="view-btn">⬇️ Download</a>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <h3>No Documents Found</h3>
                    <p>{{ user.name }} has no documents yet.</p>
                    <p>Go to the <a href="/" style="color: #667eea;">home page</a> to scan or upload documents.</p>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
