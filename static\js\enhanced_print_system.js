/**
 * 100% CLIENT-SIDE ONLY Print System for Jathagam
 * ZERO SERVER COMMUNICATION - Only detects user's local printers
 * NO server-side printer detection, NO server API calls
 */

class ClientSideOnlyPrintSystem {
    constructor() {
        this.userLocalPrinters = [];
        this.defaultLocalPrinter = null;
        this.clientPrintSettings = {};
        this.isClientInitialized = false;
        console.log('🖥️ Initializing CLIENT-SIDE ONLY Print System...');
        console.log('🚫 NO SERVER COMMUNICATION - User printers only');
    }

    /**
     * Initialize the CLIENT-SIDE ONLY print system
     * ZERO server communication
     */
    async initialize() {
        try {
            console.log('🔍 Starting CLIENT-SIDE printer detection...');
            await this.detectUserLocalPrintersOnly();
            this.setupClientSidePrintSettings();
            this.isClientInitialized = true;
            console.log('✅ CLIENT-SIDE Print System initialized - NO server communication');
            console.log(`🖥️ Found ${this.userLocalPrinters.length} user local printers`);
        } catch (error) {
            console.error('❌ CLIENT-SIDE print system initialization error:', error);
        }
    }

    /**
     * Detect USER'S LOCAL PRINTERS ONLY - 100% CLIENT-SIDE
     * ABSOLUTELY NO SERVER COMMUNICATION
     * Only detects printers connected to user's computer
     */
    async detectUserLocalPrintersOnly() {
        this.userLocalPrinters = [];
        console.log('🖥️ Detecting USER LOCAL PRINTERS ONLY - NO SERVER ACCESS...');
        console.log('🚫 ZERO server communication - Pure client-side detection');

        try {
            // Method 1: Browser's native print system detection
            await this.detectBrowserNativePrinters();

            // Method 2: Web Print API (if supported by browser)
            await this.tryWebPrintAPI();

            // Method 3: Browser-specific printer APIs
            await this.detectBrowserSpecificPrinters();

            // Method 4: Print media query capabilities
            await this.detectPrintMediaCapabilities();

        } catch (error) {
            console.error('❌ User local printer detection error:', error);
        }

        // Ensure we have at least one local printer
        if (this.userLocalPrinters.length === 0) {
            console.log('🖥️ Adding default user local printer...');
            this.userLocalPrinters = [{
                name: 'User Default Printer',
                type: 'Local System Default',
                capabilities: { color: true, duplex: false },
                online: true,
                default: true,
                source: 'User Computer Default',
                location: 'Local Computer Only'
            }];
        }

        // Set default local printer
        this.defaultLocalPrinter = this.userLocalPrinters.find(p => p.default) || this.userLocalPrinters[0];
        console.log('✅ USER LOCAL printer detection completed:', this.userLocalPrinters.length, 'local printers');
        console.log('🔒 NO server printers detected - User privacy protected');
    }

    /**
     * Detect browser's native print system (CLIENT-SIDE ONLY)
     */
    async detectBrowserNativePrinters() {
        console.log('🌐 Detecting browser native print system...');

        try {
            // Check if browser supports printing
            if (typeof window.print === 'function') {
                console.log('✅ Browser supports native printing');

                // Add browser's default print system
                this.userLocalPrinters.push({
                    name: 'Browser Print System',
                    type: 'Browser Native',
                    capabilities: {
                        color: this.detectColorPrintCapability(),
                        duplex: false
                    },
                    online: true,
                    default: true,
                    source: 'Browser Native Print',
                    location: 'User Browser'
                });
            }
        } catch (error) {
            console.log('⚠️ Browser native print detection failed:', error.message);
        }
    }

    /**
     * Try Web Print API (CLIENT-SIDE ONLY)
     */
    async tryWebPrintAPI() {
        console.log('🔍 Trying Web Print API...');

        try {
            if ('navigator' in window && 'printing' in navigator) {
                const printers = await navigator.printing.getPrinters();

                printers.forEach(printer => {
                    this.userLocalPrinters.push({
                        name: printer.name || 'Web API Printer',
                        type: this.determinePrinterTypeFromName(printer.name),
                        capabilities: printer.capabilities || { color: true },
                        online: printer.status === 'idle',
                        default: printer.isDefault || false,
                        source: 'Web Print API',
                        location: 'User Computer'
                    });
                });

                console.log('✅ Web Print API found:', printers.length, 'printers');
            } else {
                console.log('⚠️ Web Print API not supported in this browser');
            }
        } catch (error) {
            console.log('⚠️ Web Print API error:', error.message);
        }
    }

    /**
     * Detect browser-specific printer APIs (CLIENT-SIDE ONLY)
     */
    async detectBrowserSpecificPrinters() {
        console.log('🔍 Detecting browser-specific printer APIs...');

        try {
            // Chrome-specific print API
            if (window.chrome && window.chrome.printing) {
                console.log('🔍 Chrome print API detected');
                try {
                    const destinations = await window.chrome.printing.getPrinters();
                    destinations.forEach(dest => {
                        this.userLocalPrinters.push({
                            name: dest.displayName || dest.id,
                            type: this.determinePrinterTypeFromName(dest.displayName),
                            capabilities: dest.capabilities || { color: true },
                            online: dest.connectionStatus === 'ONLINE',
                            default: dest.isDefault || false,
                            source: 'Chrome Print API',
                            location: 'User Computer'
                        });
                    });
                    console.log('✅ Chrome API found:', destinations.length, 'printers');
                } catch (error) {
                    console.log('⚠️ Chrome print API error:', error.message);
                }
            }

            // Firefox-specific detection
            if (navigator.userAgent.includes('Firefox')) {
                console.log('🔍 Firefox browser detected');
                this.userLocalPrinters.push({
                    name: 'Firefox Default Printer',
                    type: 'Browser Default',
                    capabilities: { color: true, duplex: false },
                    online: true,
                    default: true,
                    source: 'Firefox Browser',
                    location: 'User Computer'
                });
            }

            // Safari-specific detection
            if (navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome')) {
                console.log('🔍 Safari browser detected');
                this.userLocalPrinters.push({
                    name: 'Safari Default Printer',
                    type: 'Browser Default',
                    capabilities: { color: true, duplex: false },
                    online: true,
                    default: true,
                    source: 'Safari Browser',
                    location: 'User Computer'
                });
            }

        } catch (error) {
            console.log('⚠️ Browser-specific detection error:', error.message);
        }
    }

    /**
     * Detect print media capabilities (CLIENT-SIDE ONLY)
     */
    async detectPrintMediaCapabilities() {
        console.log('🔍 Detecting print media capabilities...');

        try {
            // Test print media support
            const printMediaQuery = window.matchMedia('print');
            if (printMediaQuery) {
                console.log('✅ Print media queries supported');

                // Test color capability
                const colorQuery = window.matchMedia('(color)');
                const hasColor = colorQuery.matches;

                // Test different paper sizes
                const a4Query = window.matchMedia('print and (width: 210mm)');
                const letterQuery = window.matchMedia('print and (width: 8.5in)');

                this.userLocalPrinters.push({
                    name: 'Media Query Printer',
                    type: 'Media Query Detection',
                    capabilities: {
                        color: hasColor,
                        paperSizes: {
                            a4: a4Query.matches,
                            letter: letterQuery.matches
                        }
                    },
                    online: true,
                    default: false,
                    source: 'CSS Media Query',
                    location: 'User Browser'
                });

                console.log('✅ Media query capabilities detected');
            }
        } catch (error) {
            console.log('⚠️ Media query detection error:', error.message);
        }
    }

    /**
     * Detect color print capability (CLIENT-SIDE ONLY)
     */
    detectColorPrintCapability() {
        try {
            const colorQuery = window.matchMedia('(color)');
            return colorQuery.matches;
        } catch (error) {
            console.log('⚠️ Color capability detection failed:', error.message);
            return true; // Assume color support
        }
    }

    /**
     * Detect printers using CLIENT-SIDE ONLY methods
     * NO SERVER COMMUNICATION - only user's local printers
     */
    async detectClientSidePrintersOnly() {
        console.log('🔍 Using client-side only printer detection...');

        try {
            // Method 1: Use browser's print dialog capabilities
            const printers = await this.detectPrintersViaBrowserAPI();

            // Method 2: Use media queries to detect print capabilities
            const printCapabilities = this.detectPrintCapabilitiesViaMediaQuery();

            // Combine results
            this.detectedPrinters = [
                ...printers,
                ...printCapabilities
            ];

            // Remove duplicates
            this.detectedPrinters = this.removeDuplicatePrinters(this.detectedPrinters);

            console.log('✅ Client-side detection found:', this.detectedPrinters.length, 'printers');

        } catch (error) {
            console.error('❌ Client-side detection failed:', error);

            // Fallback: Assume user has at least one local printer
            this.detectedPrinters = [{
                name: 'Local Default Printer',
                type: 'System Default',
                capabilities: { color: true, duplex: false },
                online: true,
                default: true,
                source: 'Client Fallback'
            }];
        }
    }

    /**
     * Detect printers via browser's print API (CLIENT-SIDE)
     */
    async detectPrintersViaBrowserAPI() {
        const printers = [];

        try {
            // Check if browser supports print destination API
            if (window.chrome && window.chrome.printing) {
                // Chrome's printing API
                const destinations = await window.chrome.printing.getPrinters();
                destinations.forEach(dest => {
                    printers.push({
                        name: dest.displayName || dest.id,
                        type: this.determinePrinterTypeFromName(dest.displayName),
                        capabilities: dest.capabilities || {},
                        online: dest.connectionStatus === 'ONLINE',
                        default: dest.isDefault || false,
                        source: 'Chrome Print API'
                    });
                });
            }

            // Check for other browser-specific APIs
            if (navigator.userAgent.includes('Firefox')) {
                // Firefox doesn't expose printer list, but we can detect print capability
                printers.push({
                    name: 'Firefox Default Printer',
                    type: 'Browser Default',
                    capabilities: { color: true },
                    online: true,
                    default: true,
                    source: 'Firefox Browser'
                });
            }

            if (navigator.userAgent.includes('Safari')) {
                // Safari print detection
                printers.push({
                    name: 'Safari Default Printer',
                    type: 'Browser Default',
                    capabilities: { color: true },
                    online: true,
                    default: true,
                    source: 'Safari Browser'
                });
            }

        } catch (error) {
            console.log('Browser API printer detection not available:', error.message);
        }

        return printers;
    }

    /**
     * Detect print capabilities via CSS media queries (CLIENT-SIDE)
     */
    detectPrintCapabilitiesViaMediaQuery() {
        const capabilities = [];

        try {
            // Test if browser supports print media
            const printMediaQuery = window.matchMedia('print');
            if (printMediaQuery) {
                console.log('✅ Browser supports print media queries');

                // Test color printing capability
                const colorQuery = window.matchMedia('(color)');
                const hasColor = colorQuery.matches;

                // Test different paper sizes
                const a4Query = window.matchMedia('print and (width: 210mm)');
                const letterQuery = window.matchMedia('print and (width: 8.5in)');

                capabilities.push({
                    name: 'Browser Print System',
                    type: 'System Integration',
                    capabilities: {
                        color: hasColor,
                        paperSizes: {
                            a4: a4Query.matches,
                            letter: letterQuery.matches
                        }
                    },
                    online: true,
                    default: true,
                    source: 'Media Query Detection'
                });
            }

        } catch (error) {
            console.log('Media query detection failed:', error.message);
        }

        return capabilities;
    }

    /**
     * Remove duplicate printers from the list
     */
    removeDuplicatePrinters(printers) {
        const seen = new Set();
        return printers.filter(printer => {
            const key = printer.name.toLowerCase();
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    /**
     * Determine printer type from name only (CLIENT-SIDE ONLY)
     */
    determinePrinterTypeFromName(name) {
        if (!name) return 'User Local Printer';

        const lowerName = name.toLowerCase();

        if (lowerName.includes('laser') || lowerName.includes('hp') || lowerName.includes('brother')) {
            return 'Local Laser Printer';
        } else if (lowerName.includes('inkjet') || lowerName.includes('canon') || lowerName.includes('epson')) {
            return 'Local Inkjet Printer';
        } else if (lowerName.includes('thermal') || lowerName.includes('receipt')) {
            return 'Local Thermal Printer';
        } else if (lowerName.includes('pdf') || lowerName.includes('xps') || lowerName.includes('print to') || lowerName.includes('save')) {
            return 'Virtual Printer';
        } else if (lowerName.includes('default') || lowerName.includes('system')) {
            return 'System Default Printer';
        } else {
            return 'User Local Printer';
        }
    }

    /**
     * Setup CLIENT-SIDE print settings (NO SERVER)
     */
    setupClientSidePrintSettings() {
        this.clientPrintSettings = {
            paperSize: 'A4',
            orientation: 'portrait',
            margins: 'normal',
            quality: 'high',
            color: true,
            duplex: false,
            copies: 1,
            source: 'CLIENT_SIDE_ONLY'
        };
        console.log('✅ Client-side print settings configured');
    }

    /**
     * Check printer connectivity (CLIENT-SIDE ONLY)
     * No server communication - only browser-based checks
     */
    async checkPrinterConnectivity() {
        console.log('🔍 Checking client-side printer connectivity...');

        for (let printer of this.detectedPrinters) {
            try {
                const connectivity = await this.checkClientSidePrinterConnectivity(printer);
                printer.connected = connectivity.connected;
                printer.ready = connectivity.ready;
                printer.lastChecked = new Date().toISOString();
            } catch (error) {
                printer.connected = false;
                printer.ready = false;
                printer.error = error.message;
            }
        }

        console.log('✅ Client-side connectivity check completed');
    }

    /**
     * Check single printer connectivity (CLIENT-SIDE ONLY)
     */
    async checkClientSidePrinterConnectivity(printer) {
        try {
            // For client-side detection, we assume printers are available
            // if they were detected by the browser

            // Method 1: Check if printer source indicates availability
            if (printer.source === 'Web Print API' || printer.source === 'Chrome Print API') {
                // These APIs only return available printers
                return { connected: true, ready: true };
            }

            // Method 2: Test print capability
            const canPrint = this.testBrowserPrintCapability();

            // Method 3: Check if this is a virtual printer
            if (printer.type === 'Virtual') {
                return { connected: true, ready: true };
            }

            // Default: Assume available if detected by browser
            return {
                connected: canPrint,
                ready: canPrint,
                method: 'Client-side assumption'
            };

        } catch (error) {
            console.error(`Client-side connectivity check failed for ${printer.name}:`, error);
            return { connected: false, ready: false };
        }
    }

    /**
     * Test browser's print capability (CLIENT-SIDE)
     */
    testBrowserPrintCapability() {
        try {
            // Check if window.print is available
            if (typeof window.print === 'function') {
                // Check if print media is supported
                const printMedia = window.matchMedia('print');
                return printMedia !== null;
            }
            return false;
        } catch (error) {
            console.error('Print capability test failed:', error);
            return false;
        }
    }

    /**
     * Determine printer type from name/capabilities
     */
    determinePrinterType(printer) {
        const name = printer.name.toLowerCase();
        
        if (name.includes('laser') || name.includes('hp')) {
            return 'Laser';
        } else if (name.includes('inkjet') || name.includes('canon') || name.includes('epson')) {
            return 'Inkjet';
        } else if (name.includes('thermal')) {
            return 'Thermal';
        } else if (name.includes('pdf') || name.includes('xps')) {
            return 'Virtual';
        } else {
            return 'Generic';
        }
    }

    /**
     * Setup optimal print settings
     */
    setupPrintSettings() {
        this.printSettings = {
            paperSize: 'A4',
            orientation: 'portrait',
            margins: { top: 15, bottom: 15, left: 10, right: 10 },
            color: this.defaultPrinter?.capabilities?.color || false,
            quality: 'normal',
            duplex: false,
            scale: 100
        };
    }

    /**
     * Show printer selection dialog
     */
    showPrinterDialog() {
        const modal = document.createElement('div');
        modal.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 1rem; color: #333;">🖨️ அச்சுப்பொறி தேர்வு</h3>
                    
                    <div id="printer-status" style="background: #e8f5e8; padding: 1rem; border-radius: 5px; margin-bottom: 1rem; border-left: 4px solid #28a745;">
                        <p style="margin: 0; color: #155724;">
                            <strong>🖥️ உங்கள் கணினியில் கண்டறியப்பட்ட அச்சுப்பொறிகள்:</strong> ${this.detectedPrinters.length}
                        </p>
                        <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; color: #155724;">
                            ✅ சர்வர் அச்சுப்பொறிகள் அல்ல - உங்கள் உள்ளூர் அச்சுப்பொறிகள் மட்டும்
                        </p>
                    </div>
                    
                    <div id="printer-list" style="margin-bottom: 1.5rem;">
                        ${this.generatePrinterList()}
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-bottom: 1.5rem;">
                        <h4 style="margin: 0 0 0.5rem 0; color: #495057;">அச்சு அமைப்புகள்:</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">காகித அளவு:</label>
                                <select id="paper-size" style="width: 100%; padding: 0.5rem;">
                                    <option value="A4">A4</option>
                                    <option value="Letter">Letter</option>
                                    <option value="A3">A3</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">தரம்:</label>
                                <select id="print-quality" style="width: 100%; padding: 0.5rem;">
                                    <option value="draft">வரைவு</option>
                                    <option value="normal" selected>சாதாரண</option>
                                    <option value="high">உயர்</option>
                                </select>
                            </div>
                        </div>
                        <div style="margin-top: 1rem;">
                            <label style="display: flex; align-items: center;">
                                <input type="checkbox" id="use-color" ${this.printSettings.color ? 'checked' : ''}>
                                <span style="margin-left: 0.5rem;">வண்ண அச்சு</span>
                            </label>
                        </div>
                    </div>
                    
                    <div style="text-align: center;">
                        <button onclick="enhancedPrintSystem.printWithSelectedSettings()" style="background: #28a745; color: white; border: none; padding: 0.8rem 2rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">🖨️ அச்சிடு</button>
                        <button onclick="enhancedPrintSystem.showPrintPreview()" style="background: #007bff; color: white; border: none; padding: 0.8rem 2rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">👁️ முன்னோட்டம்</button>
                        <button onclick="enhancedPrintSystem.closePrinterDialog()" style="background: #6c757d; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer;">ரத்து செய்</button>
                    </div>
                </div>
            </div>
        `;
        modal.id = 'printer-dialog';
        document.body.appendChild(modal);
    }

    /**
     * Generate printer list HTML
     */
    generatePrinterList() {
        if (this.detectedPrinters.length === 0) {
            return '<p style="text-align: center; color: #666;">அச்சுப்பொறி கண்டறியப்படவில்லை</p>';
        }

        return this.detectedPrinters.map((printer, index) => `
            <div style="border: 1px solid #ddd; padding: 1rem; border-radius: 5px; margin-bottom: 0.5rem; ${printer.default ? 'background: #e8f5e8; border-color: #28a745;' : ''}">
                <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="radio" name="selected-printer" value="${index}" ${printer.default ? 'checked' : ''}>
                    <div style="margin-left: 0.5rem; flex: 1;">
                        <div style="font-weight: bold; color: #333;">
                            🖥️ ${printer.name} ${printer.default ? '(இயல்புநிலை)' : ''}
                        </div>
                        <div style="font-size: 0.9em; color: #666;">
                            வகை: ${printer.type} |
                            நிலை: ${printer.online ? '🟢 ஆன்லைன்' : '🔴 ஆஃப்லைன்'} |
                            ${printer.capabilities?.color ? '🎨 வண்ணம்' : '⚫ கருப்பு-வெள்ளை'}
                        </div>
                        <div style="font-size: 0.8em; color: #28a745; margin-top: 0.2rem;">
                            📍 மூலம்: ${printer.source || 'உள்ளூர் கணினி'} (சர்வர் அல்ல)
                        </div>
                    </div>
                </label>
            </div>
        `).join('');
    }

    /**
     * Print with selected settings
     */
    async printWithSelectedSettings() {
        try {
            const selectedPrinterIndex = document.querySelector('input[name="selected-printer"]:checked')?.value;
            const selectedPrinter = this.detectedPrinters[selectedPrinterIndex] || this.defaultPrinter;
            
            // Update print settings
            this.printSettings.paperSize = document.getElementById('paper-size').value;
            this.printSettings.quality = document.getElementById('print-quality').value;
            this.printSettings.color = document.getElementById('use-color').checked;

            // Get current Jathagam data
            const jathagamData = this.getCurrentJathagamData();
            
            if (!jathagamData) {
                alert('❌ ஜாதக தகவல் கிடைக்கவில்லை');
                return;
            }

            // Format for printing
            const formattedHTML = await this.formatJathagamForPrint(jathagamData, selectedPrinter);
            
            // Create print window
            this.createPrintWindow(formattedHTML);
            
            this.closePrinterDialog();
            
        } catch (error) {
            console.error('Print error:', error);
            alert('❌ அச்சு பிழை: ' + error.message);
        }
    }

    /**
     * Show print preview (CLIENT-SIDE ONLY)
     */
    async showPrintPreview() {
        try {
            const selectedPrinterIndex = document.querySelector('input[name="selected-printer"]:checked')?.value;
            const selectedPrinter = this.detectedPrinters[selectedPrinterIndex] || this.defaultPrinter;

            const jathagamData = this.getCurrentJathagamData();

            if (!jathagamData) {
                alert('❌ ஜாதக தகவல் கிடைக்கவில்லை');
                return;
            }

            // Generate preview using CLIENT-SIDE formatting only
            const previewHTML = this.formatJathagamClientSide(jathagamData, selectedPrinter);
            this.showPreviewWindow(previewHTML);

        } catch (error) {
            console.error('Preview error:', error);
            alert('❌ முன்னோட்டம் பிழை: ' + error.message);
        }
    }

    /**
     * Get current Jathagam data from the page
     */
    getCurrentJathagamData() {
        // Try to get data from various possible sources
        if (window.currentJathagamData) {
            return window.currentJathagamData;
        }
        
        // Try to extract from modal
        const modal = document.getElementById('accurate-jathagam-modal') || 
                     document.getElementById('user-jathagam-result-modal');
        
        if (modal) {
            // Extract data from modal content
            return this.extractJathagamDataFromModal(modal);
        }
        
        return null;
    }

    /**
     * Extract Jathagam data from modal content
     */
    extractJathagamDataFromModal(modal) {
        // This is a simplified extraction - in practice, you'd store the data
        // when the modal is created
        const title = modal.querySelector('h3')?.textContent || '';
        const name = title.replace('🌟', '').replace('அவர்களின் ஜாதகம்', '').trim();
        
        return {
            personal_details: {
                name: name,
                // Other details would be extracted similarly
            },
            // This would be populated with actual data
            extracted_from_modal: true
        };
    }

    /**
     * Format Jathagam for printing (CLIENT-SIDE ONLY)
     * No server communication - all formatting done in browser
     */
    async formatJathagamForPrint(jathagamData, printerInfo) {
        console.log('🖨️ Formatting Jathagam using CLIENT-SIDE only...');

        try {
            // Use only client-side formatting
            return this.formatJathagamClientSide(jathagamData, printerInfo);
        } catch (error) {
            console.error('Client-side formatting error:', error);
            throw new Error('Client-side formatting failed: ' + error.message);
        }
    }

    /**
     * Enhanced client-side formatting with complete Jathagam data
     */
    formatJathagamClientSide(jathagamData, printerInfo) {
        const personal = jathagamData.personal_details || {};
        const astro = jathagamData.astrological_details || {};
        const planets = jathagamData.planetary_positions || {};

        return `
        <!DOCTYPE html>
        <html lang="ta">
        <head>
            <meta charset="UTF-8">
            <title>ஜாதகம் - ${personal.name || 'தெரியவில்லை'}</title>
            <style>
                body {
                    font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', sans-serif;
                    margin: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 2rem;
                    border-bottom: 2px solid #333;
                    padding-bottom: 1rem;
                }
                .section {
                    margin-bottom: 1.5rem;
                    padding: 1rem;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
                .section h3 {
                    color: #2c3e50;
                    margin-bottom: 1rem;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 0.5rem;
                }
                .info-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                }
                .planet-row {
                    display: flex;
                    justify-content: space-between;
                    padding: 0.3rem 0;
                    border-bottom: 1px dotted #ccc;
                }
                .printer-info {
                    background: #e8f5e8;
                    padding: 0.5rem;
                    border-radius: 3px;
                    font-size: 0.9rem;
                    color: #155724;
                    margin-bottom: 1rem;
                }
                @media print {
                    body { margin: 15px; }
                    .no-print { display: none; }
                    .section { break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🌟 தமிழ் ஜாதகம் 🌟</h1>
                <h2>${personal.name || 'தெரியவில்லை'} அவர்களின் ஜாதகம்</h2>
            </div>

            <div class="printer-info">
                🖥️ <strong>உள்ளூர் அச்சுப்பொறி:</strong> ${printerInfo.name} (${printerInfo.source || 'உங்கள் கணினி'})
                <br>📅 <strong>அச்சிடப்பட்ட நேரம்:</strong> ${new Date().toLocaleString('ta-IN')}
            </div>

            <div class="section">
                <h3>📋 பிறப்பு விவரங்கள்</h3>
                <div class="info-grid">
                    <div><strong>பிறந்த தேதி:</strong> ${personal.birth_date || 'தெரியவில்லை'}</div>
                    <div><strong>பிறந்த நேரம்:</strong> ${personal.birth_time || 'தெரியவில்லை'}</div>
                    <div><strong>பிறந்த இடம்:</strong> ${personal.birth_place || 'தெரியவில்லை'}</div>
                    <div><strong>பாலினம்:</strong> ${personal.gender || 'தெரியவில்லை'}</div>
                </div>
            </div>

            <div class="section">
                <h3>🌙 முக்கிய ஜோதிட தகவல்கள்</h3>
                <div class="info-grid">
                    <div><strong>லக்ன ராசி:</strong> ${astro.lagna_rasi || astro.lagna_raasi || 'தெரியவில்லை'}</div>
                    <div><strong>சந்திர ராசி:</strong> ${astro.moon_rasi || astro.moon_raasi || 'தெரியவில்லை'}</div>
                    <div><strong>சந்திர நட்சத்திரம்:</strong> ${astro.moon_nakshatra || 'தெரியவில்லை'}</div>
                    <div><strong>சந்திர பாதம்:</strong> ${astro.moon_pada || 'தெரியவில்லை'}</div>
                </div>
            </div>

            <div class="section">
                <h3>🪐 கிரக நிலைகள் (Swiss Ephemeris கணக்கீடு)</h3>
                ${this.formatPlanetaryPositions(planets)}
            </div>

            <div class="section">
                <h3>📊 கணக்கீட்டு தகவல்கள்</h3>
                <div>
                    <strong>கணக்கீட்டு முறை:</strong> ${jathagamData.calculation_method || 'Swiss Ephemeris with Lahiri Ayanamsa'}<br>
                    <strong>Architecture:</strong> ${jathagamData.architecture_steps || 'STEP 1-5 Implementation'}<br>
                    <strong>உருவாக்கப்பட்ட நேரம்:</strong> ${personal.generated_at || 'தெரியவில்லை'}
                </div>
            </div>

            <div style="text-align: center; margin-top: 2rem; font-size: 0.9rem; color: #666;">
                இந்த ஜாதகம் துல்லியமான Swiss Ephemeris கணக்கீட்டுடன் உருவாக்கப்பட்டது
                <br>🖥️ உங்கள் உள்ளூர் கணினியில் இருந்து அச்சிடப்பட்டது (சர்வர் அல்ல)
            </div>
        </body>
        </html>
        `;
    }

    /**
     * Format planetary positions for printing
     */
    formatPlanetaryPositions(planets) {
        if (!planets || Object.keys(planets).length === 0) {
            return '<p>கிரக நிலைகள் கிடைக்கவில்லை</p>';
        }

        let html = '';
        Object.entries(planets).forEach(([planetName, planetData]) => {
            const tamilName = planetData.tamil_name || planetName;
            const rasiName = planetData.rasi_name || 'தெரியவில்லை';
            const nakshatra = planetData.nakshatra_name || 'தெரியவில்லை';
            const pada = planetData.pada || 1;
            const degrees = planetData.degrees_in_sign || 0;

            html += `
                <div class="planet-row">
                    <span><strong>${tamilName}:</strong></span>
                    <span>${rasiName} - ${nakshatra} ${pada}ம் பாதம் (${degrees.toFixed(2)}°)</span>
                </div>
            `;
        });

        return html;
    }

    /**
     * Create print window
     */
    createPrintWindow(htmlContent) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(htmlContent);
        printWindow.document.close();
        printWindow.focus();
        
        // Auto-print after a short delay
        setTimeout(() => {
            printWindow.print();
        }, 500);
    }

    /**
     * Show preview window
     */
    showPreviewWindow(htmlContent) {
        const previewWindow = window.open('', '_blank');
        previewWindow.document.write(htmlContent);
        previewWindow.document.close();
        previewWindow.focus();
    }

    /**
     * Close printer dialog
     */
    closePrinterDialog() {
        const dialog = document.getElementById('printer-dialog');
        if (dialog) {
            dialog.remove();
        }
    }

    /**
     * CLIENT-SIDE ONLY enhanced print function for Jathagam
     * ABSOLUTELY NO SERVER COMMUNICATION
     */
    async enhancedPrintJathagam() {
        console.log('🖨️ Starting CLIENT-SIDE ONLY enhanced print...');
        console.log('🚫 ZERO server communication - User local printers only');

        if (!this.isClientInitialized) {
            await this.initialize();
        }

        // Check if any USER LOCAL printers are available
        if (this.userLocalPrinters.length === 0) {
            alert('❌ உங்கள் கணினியில் அச்சுப்பொறி கண்டறியப்படவில்லை\n\n🖥️ உங்கள் உள்ளூர் அச்சுப்பொறி இணைக்கப்பட்டுள்ளதா என்பதை சரிபார்க்கவும்.\n🚫 சர்வர் அச்சுப்பொறிகள் காட்டப்படாது.');
            return;
        }

        console.log(`✅ Found ${this.userLocalPrinters.length} user local printers`);

        // Check if default LOCAL printer is ready
        if (this.defaultLocalPrinter && !this.defaultLocalPrinter.online) {
            const proceed = confirm('⚠️ இயல்புநிலை உள்ளூர் அச்சுப்பொறி ஆஃப்லைனில் உள்ளது\n\n🖥️ வேறு உள்ளூர் அச்சுப்பொறியை தேர்ந்தெடுக்க விரும்புகிறீர்களா?');
            if (proceed) {
                this.showUserLocalPrinterDialog();
                return;
            }
        }

        // Show USER LOCAL printer selection dialog
        this.showUserLocalPrinterDialog();
    }

    /**
     * Show USER LOCAL printer selection dialog (CLIENT-SIDE ONLY)
     */
    showUserLocalPrinterDialog() {
        console.log('🖥️ Showing USER LOCAL printer selection dialog...');

        const jathagamData = this.getCurrentJathagamData();
        if (!jathagamData) {
            alert('❌ ஜாதக தகவல் கிடைக்கவில்லை');
            return;
        }

        // Create modal for USER LOCAL printer selection
        const modal = document.createElement('div');
        modal.id = 'user-local-printer-modal';
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
            align-items: center; justify-content: center;
        `;

        modal.innerHTML = `
            <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                <h3 style="color: #2c3e50; margin-bottom: 1rem; text-align: center;">
                    🖥️ உங்கள் உள்ளூர் அச்சுப்பொறிகள்
                </h3>

                <div style="background: #e8f5e8; padding: 1rem; border-radius: 5px; margin-bottom: 1rem; border-left: 4px solid #28a745;">
                    <p style="margin: 0; color: #155724; font-weight: bold;">
                        🔒 முழு தனியுரிமை பாதுகாப்பு
                    </p>
                    <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; color: #155724;">
                        ✅ உங்கள் கணினியில் மட்டும் கண்டறியப்பட்ட அச்சுப்பொறிகள்<br>
                        🚫 சர்வர் அச்சுப்பொறிகள் காட்டப்படாது<br>
                        🖥️ முழுவதும் உங்கள் உள்ளூர் கணினியில் இருந்து மட்டும்
                    </p>
                </div>

                <div id="user-local-printer-list">
                    ${this.generateUserLocalPrinterList()}
                </div>

                <div style="text-align: center; margin-top: 1.5rem; display: flex; gap: 1rem; justify-content: center;">
                    <button onclick="clientSidePrintSystem.printWithSelectedUserLocalPrinter()"
                            style="background: #28a745; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer; font-weight: bold;">
                        🖨️ உள்ளூர் அச்சுப்பொறியில் அச்சிடு
                    </button>
                    <button onclick="clientSidePrintSystem.closeUserLocalPrinterDialog()"
                            style="background: #6c757d; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer;">
                        மூடு
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * Generate USER LOCAL printer list HTML
     */
    generateUserLocalPrinterList() {
        return this.userLocalPrinters.map((printer, index) => `
            <div style="border: 1px solid #ddd; padding: 1rem; border-radius: 5px; margin-bottom: 0.5rem; ${printer.default ? 'background: #e8f5e8; border-color: #28a745;' : ''}">
                <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="radio" name="selected-user-local-printer" value="${index}" ${printer.default ? 'checked' : ''}>
                    <div style="margin-left: 0.5rem; flex: 1;">
                        <div style="font-weight: bold; color: #333;">
                            🖥️ ${printer.name} ${printer.default ? '(இயல்புநிலை)' : ''}
                        </div>
                        <div style="font-size: 0.9em; color: #666;">
                            வகை: ${printer.type} |
                            நிலை: ${printer.online ? '🟢 ஆன்லைன்' : '🔴 ஆஃப்லைன்'} |
                            ${printer.capabilities?.color ? '🎨 வண்ணம்' : '⚫ கருப்பு-வெள்ளை'}
                        </div>
                        <div style="font-size: 0.8em; color: #28a745; margin-top: 0.2rem;">
                            📍 இடம்: ${printer.location || 'உங்கள் கணினி'} | மூலம்: ${printer.source}
                        </div>
                        <div style="font-size: 0.8em; color: #007bff; margin-top: 0.2rem;">
                            🔒 தனியுரிமை: சர்வருக்கு தகவல் அனுப்பப்படாது
                        </div>
                    </div>
                </label>
            </div>
        `).join('');
    }

    /**
     * Print with selected USER LOCAL printer (CLIENT-SIDE ONLY)
     */
    async printWithSelectedUserLocalPrinter() {
        try {
            const selectedIndex = document.querySelector('input[name="selected-user-local-printer"]:checked')?.value;
            const selectedPrinter = this.userLocalPrinters[selectedIndex] || this.defaultLocalPrinter;

            console.log('🖨️ Printing with USER LOCAL printer:', selectedPrinter.name);
            console.log('🚫 NO server communication - Pure client-side printing');

            const jathagamData = this.getCurrentJathagamData();
            if (!jathagamData) {
                alert('❌ ஜாதக தகவல் கிடைக்கவில்லை');
                return;
            }

            // Format and print using CLIENT-SIDE ONLY
            const printHTML = this.formatJathagamForUserLocalPrint(jathagamData, selectedPrinter);
            this.executeUserLocalPrint(printHTML);

            this.closeUserLocalPrinterDialog();

        } catch (error) {
            console.error('USER LOCAL print error:', error);
            alert('❌ உள்ளூர் அச்சு பிழை: ' + error.message);
        }
    }

    /**
     * Format Jathagam for USER LOCAL printing (CLIENT-SIDE ONLY)
     */
    formatJathagamForUserLocalPrint(jathagamData, printerInfo) {
        console.log('🖨️ Formatting Jathagam for USER LOCAL printer - NO server communication');

        const personal = jathagamData.personal_details || {};
        const astro = jathagamData.astrological_details || {};
        const planets = jathagamData.planetary_positions || {};

        return `
        <!DOCTYPE html>
        <html lang="ta">
        <head>
            <meta charset="UTF-8">
            <title>ஜாதகம் - ${personal.name || 'தெரியவில்லை'}</title>
            <style>
                body {
                    font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', sans-serif;
                    margin: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 2rem;
                    border-bottom: 2px solid #333;
                    padding-bottom: 1rem;
                }
                .privacy-notice {
                    background: #e8f5e8;
                    padding: 1rem;
                    border-radius: 5px;
                    border-left: 4px solid #28a745;
                    margin-bottom: 1rem;
                    font-size: 0.9rem;
                    color: #155724;
                }
                .section {
                    margin-bottom: 1.5rem;
                    padding: 1rem;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
                .section h3 {
                    color: #2c3e50;
                    margin-bottom: 1rem;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 0.5rem;
                }
                .info-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                }
                .planet-row {
                    display: flex;
                    justify-content: space-between;
                    padding: 0.3rem 0;
                    border-bottom: 1px dotted #ccc;
                }
                @media print {
                    body { margin: 15px; }
                    .no-print { display: none; }
                    .section { break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🌟 தமிழ் ஜாதகம் 🌟</h1>
                <h2>${personal.name || 'தெரியவில்லை'} அவர்களின் ஜாதகம்</h2>
            </div>

            <div class="privacy-notice">
                <strong>🔒 முழு தனியுரிமை பாதுகாப்பு:</strong><br>
                🖥️ <strong>உங்கள் உள்ளூர் அச்சுப்பொறி:</strong> ${printerInfo.name}<br>
                📍 <strong>அச்சுப்பொறி இடம்:</strong> ${printerInfo.location || 'உங்கள் கணினி'}<br>
                🚫 <strong>சர்வர் தகவல்:</strong> எந்த தகவலும் சர்வருக்கு அனுப்பப்படவில்லை<br>
                📅 <strong>அச்சிடப்பட்ட நேரம்:</strong> ${new Date().toLocaleString('ta-IN')}
            </div>

            <div class="section">
                <h3>📋 பிறப்பு விவரங்கள்</h3>
                <div class="info-grid">
                    <div><strong>பிறந்த தேதி:</strong> ${personal.birth_date || 'தெரியவில்லை'}</div>
                    <div><strong>பிறந்த நேரம்:</strong> ${personal.birth_time || 'தெரியவில்லை'}</div>
                    <div><strong>பிறந்த இடம்:</strong> ${personal.birth_place || 'தெரியவில்லை'}</div>
                    <div><strong>பாலினம்:</strong> ${personal.gender || 'தெரியவில்லை'}</div>
                </div>
            </div>

            <div class="section">
                <h3>🌙 முக்கிய ஜோதிட தகவல்கள்</h3>
                <div class="info-grid">
                    <div><strong>லக்ன ராசி:</strong> ${astro.lagna_rasi || astro.lagna_raasi || 'தெரியவில்லை'}</div>
                    <div><strong>சந்திர ராசி:</strong> ${astro.moon_rasi || astro.moon_raasi || 'தெரியவில்லை'}</div>
                    <div><strong>சந்திர நட்சத்திரம்:</strong> ${astro.moon_nakshatra || 'தெரியவில்லை'}</div>
                    <div><strong>சந்திர பாதம்:</strong> ${astro.moon_pada || 'தெரியவில்லை'}</div>
                </div>
            </div>

            <div class="section">
                <h3>🪐 கிரக நிலைகள் (Swiss Ephemeris கணக்கீடு)</h3>
                ${this.formatPlanetaryPositionsForUserLocalPrint(planets)}
            </div>

            <div class="section">
                <h3>📊 கணக்கீட்டு தகவல்கள்</h3>
                <div>
                    <strong>கணக்கீட்டு முறை:</strong> ${jathagamData.calculation_method || 'Swiss Ephemeris with Lahiri Ayanamsa'}<br>
                    <strong>Architecture:</strong> ${jathagamData.architecture_steps || 'STEP 1-5 Implementation'}<br>
                    <strong>உருவாக்கப்பட்ட நேரம்:</strong> ${personal.generated_at || 'தெரியவில்லை'}
                </div>
            </div>

            <div style="text-align: center; margin-top: 2rem; font-size: 0.9rem; color: #666; border-top: 1px solid #ddd; padding-top: 1rem;">
                <strong>🔒 முழு தனியுரிமை பாதுகாப்பு:</strong><br>
                இந்த ஜாதகம் துல்லியமான Swiss Ephemeris கணக்கீட்டுடன் உருவாக்கப்பட்டது<br>
                🖥️ உங்கள் உள்ளூர் கணினியில் இருந்து மட்டும் அச்சிடப்பட்டது<br>
                🚫 எந்த தகவலும் சர்வருக்கு அனுப்பப்படவில்லை
            </div>
        </body>
        </html>
        `;
    }

    /**
     * Format planetary positions for USER LOCAL printing
     */
    formatPlanetaryPositionsForUserLocalPrint(planets) {
        if (!planets || Object.keys(planets).length === 0) {
            return '<p>கிரக நிலைகள் கிடைக்கவில்லை</p>';
        }

        let html = '';
        Object.entries(planets).forEach(([planetName, planetData]) => {
            const tamilName = planetData.tamil_name || planetName;
            const rasiName = planetData.rasi_name || 'தெரியவில்லை';
            const nakshatra = planetData.nakshatra_name || 'தெரியவில்லை';
            const pada = planetData.pada || 1;
            const degrees = planetData.degrees_in_sign || 0;

            html += `
                <div class="planet-row">
                    <span><strong>${tamilName}:</strong></span>
                    <span>${rasiName} - ${nakshatra} ${pada}ம் பாதம் (${degrees.toFixed(2)}°)</span>
                </div>
            `;
        });

        return html;
    }

    /**
     * Execute USER LOCAL print (CLIENT-SIDE ONLY)
     */
    executeUserLocalPrint(printHTML) {
        console.log('🖨️ Executing USER LOCAL print - NO server involved');

        // Create print window for USER LOCAL printing
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        printWindow.document.write(printHTML);
        printWindow.document.close();
        printWindow.focus();

        // Wait for content to load then print
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };

        console.log('✅ USER LOCAL print executed successfully');
    }

    /**
     * Close USER LOCAL printer dialog
     */
    closeUserLocalPrinterDialog() {
        const modal = document.getElementById('user-local-printer-modal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * Get current Jathagam data (CLIENT-SIDE ONLY)
     */
    getCurrentJathagamData() {
        return window.currentJathagamData || null;
    }
}

// Initialize global CLIENT-SIDE ONLY instance
const clientSidePrintSystem = new ClientSideOnlyPrintSystem();

// For backward compatibility
const enhancedPrintSystem = clientSidePrintSystem;

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        clientSidePrintSystem.initialize();
    });
} else {
    clientSidePrintSystem.initialize();
}
