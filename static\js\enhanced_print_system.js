/**
 * Enhanced Print System for Jathagam
 * Automatically detects printers, checks connectivity, and optimizes formatting
 */

class EnhancedPrintSystem {
    constructor() {
        this.detectedPrinters = [];
        this.defaultPrinter = null;
        this.printSettings = {};
        this.isInitialized = false;
    }

    /**
     * Initialize the print system
     */
    async initialize() {
        try {
            await this.detectPrinters();
            await this.checkPrinterConnectivity();
            this.setupPrintSettings();
            this.isInitialized = true;
            console.log('✅ Enhanced Print System initialized');
        } catch (error) {
            console.error('❌ Print system initialization error:', error);
        }
    }

    /**
     * Detect available printers using Web APIs
     */
    async detectPrinters() {
        this.detectedPrinters = [];

        try {
            // Check if Web Print API is available
            if ('navigator' in window && 'printing' in navigator) {
                const printers = await navigator.printing.getPrinters();
                this.detectedPrinters = printers.map(printer => ({
                    name: printer.name,
                    type: this.determinePrinterType(printer),
                    capabilities: printer.capabilities || {},
                    online: printer.status === 'idle',
                    default: printer.isDefault || false
                }));
            }
        } catch (error) {
            console.log('Web Print API not available, using fallback detection');
        }

        // Fallback: Use media queries and browser capabilities
        if (this.detectedPrinters.length === 0) {
            this.detectedPrinters = await this.detectPrintersViaServer();
        }

        // Set default printer
        this.defaultPrinter = this.detectedPrinters.find(p => p.default) || this.detectedPrinters[0];
    }

    /**
     * Detect printers via server-side API
     */
    async detectPrintersViaServer() {
        try {
            const response = await fetch('/api/detect_printers', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                return data.printers || [];
            }
        } catch (error) {
            console.error('Server printer detection failed:', error);
        }

        // Ultimate fallback
        return [{
            name: 'Default Printer',
            type: 'Generic',
            capabilities: { color: true, duplex: false },
            online: true,
            default: true
        }];
    }

    /**
     * Check printer connectivity
     */
    async checkPrinterConnectivity() {
        for (let printer of this.detectedPrinters) {
            try {
                const connectivity = await this.checkSinglePrinterConnectivity(printer.name);
                printer.connected = connectivity.connected;
                printer.ready = connectivity.ready;
                printer.lastChecked = new Date().toISOString();
            } catch (error) {
                printer.connected = false;
                printer.ready = false;
                printer.error = error.message;
            }
        }
    }

    /**
     * Check single printer connectivity via server
     */
    async checkSinglePrinterConnectivity(printerName) {
        try {
            const response = await fetch('/api/check_printer_connectivity', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ printer_name: printerName })
            });

            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.error(`Connectivity check failed for ${printerName}:`, error);
        }

        return { connected: false, ready: false };
    }

    /**
     * Determine printer type from name/capabilities
     */
    determinePrinterType(printer) {
        const name = printer.name.toLowerCase();
        
        if (name.includes('laser') || name.includes('hp')) {
            return 'Laser';
        } else if (name.includes('inkjet') || name.includes('canon') || name.includes('epson')) {
            return 'Inkjet';
        } else if (name.includes('thermal')) {
            return 'Thermal';
        } else if (name.includes('pdf') || name.includes('xps')) {
            return 'Virtual';
        } else {
            return 'Generic';
        }
    }

    /**
     * Setup optimal print settings
     */
    setupPrintSettings() {
        this.printSettings = {
            paperSize: 'A4',
            orientation: 'portrait',
            margins: { top: 15, bottom: 15, left: 10, right: 10 },
            color: this.defaultPrinter?.capabilities?.color || false,
            quality: 'normal',
            duplex: false,
            scale: 100
        };
    }

    /**
     * Show printer selection dialog
     */
    showPrinterDialog() {
        const modal = document.createElement('div');
        modal.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 1rem; color: #333;">🖨️ அச்சுப்பொறி தேர்வு</h3>
                    
                    <div id="printer-status" style="background: #e3f2fd; padding: 1rem; border-radius: 5px; margin-bottom: 1rem;">
                        <p style="margin: 0; color: #1976d2;">
                            <strong>கண்டறியப்பட்ட அச்சுப்பொறிகள்:</strong> ${this.detectedPrinters.length}
                        </p>
                    </div>
                    
                    <div id="printer-list" style="margin-bottom: 1.5rem;">
                        ${this.generatePrinterList()}
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-bottom: 1.5rem;">
                        <h4 style="margin: 0 0 0.5rem 0; color: #495057;">அச்சு அமைப்புகள்:</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">காகித அளவு:</label>
                                <select id="paper-size" style="width: 100%; padding: 0.5rem;">
                                    <option value="A4">A4</option>
                                    <option value="Letter">Letter</option>
                                    <option value="A3">A3</option>
                                </select>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem;">தரம்:</label>
                                <select id="print-quality" style="width: 100%; padding: 0.5rem;">
                                    <option value="draft">வரைவு</option>
                                    <option value="normal" selected>சாதாரண</option>
                                    <option value="high">உயர்</option>
                                </select>
                            </div>
                        </div>
                        <div style="margin-top: 1rem;">
                            <label style="display: flex; align-items: center;">
                                <input type="checkbox" id="use-color" ${this.printSettings.color ? 'checked' : ''}>
                                <span style="margin-left: 0.5rem;">வண்ண அச்சு</span>
                            </label>
                        </div>
                    </div>
                    
                    <div style="text-align: center;">
                        <button onclick="enhancedPrintSystem.printWithSelectedSettings()" style="background: #28a745; color: white; border: none; padding: 0.8rem 2rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">🖨️ அச்சிடு</button>
                        <button onclick="enhancedPrintSystem.showPrintPreview()" style="background: #007bff; color: white; border: none; padding: 0.8rem 2rem; border-radius: 5px; margin-right: 0.5rem; cursor: pointer;">👁️ முன்னோட்டம்</button>
                        <button onclick="enhancedPrintSystem.closePrinterDialog()" style="background: #6c757d; color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 5px; cursor: pointer;">ரத்து செய்</button>
                    </div>
                </div>
            </div>
        `;
        modal.id = 'printer-dialog';
        document.body.appendChild(modal);
    }

    /**
     * Generate printer list HTML
     */
    generatePrinterList() {
        if (this.detectedPrinters.length === 0) {
            return '<p style="text-align: center; color: #666;">அச்சுப்பொறி கண்டறியப்படவில்லை</p>';
        }

        return this.detectedPrinters.map((printer, index) => `
            <div style="border: 1px solid #ddd; padding: 1rem; border-radius: 5px; margin-bottom: 0.5rem; ${printer.default ? 'background: #e8f5e8;' : ''}">
                <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="radio" name="selected-printer" value="${index}" ${printer.default ? 'checked' : ''}>
                    <div style="margin-left: 0.5rem; flex: 1;">
                        <div style="font-weight: bold; color: #333;">
                            ${printer.name} ${printer.default ? '(இயல்புநிலை)' : ''}
                        </div>
                        <div style="font-size: 0.9em; color: #666;">
                            வகை: ${printer.type} | 
                            நிலை: ${printer.online ? '🟢 ஆன்லைன்' : '🔴 ஆஃப்லைன்'} |
                            ${printer.capabilities?.color ? '🎨 வண்ணம்' : '⚫ கருப்பு-வெள்ளை'}
                        </div>
                    </div>
                </label>
            </div>
        `).join('');
    }

    /**
     * Print with selected settings
     */
    async printWithSelectedSettings() {
        try {
            const selectedPrinterIndex = document.querySelector('input[name="selected-printer"]:checked')?.value;
            const selectedPrinter = this.detectedPrinters[selectedPrinterIndex] || this.defaultPrinter;
            
            // Update print settings
            this.printSettings.paperSize = document.getElementById('paper-size').value;
            this.printSettings.quality = document.getElementById('print-quality').value;
            this.printSettings.color = document.getElementById('use-color').checked;

            // Get current Jathagam data
            const jathagamData = this.getCurrentJathagamData();
            
            if (!jathagamData) {
                alert('❌ ஜாதக தகவல் கிடைக்கவில்லை');
                return;
            }

            // Format for printing
            const formattedHTML = await this.formatJathagamForPrint(jathagamData, selectedPrinter);
            
            // Create print window
            this.createPrintWindow(formattedHTML);
            
            this.closePrinterDialog();
            
        } catch (error) {
            console.error('Print error:', error);
            alert('❌ அச்சு பிழை: ' + error.message);
        }
    }

    /**
     * Show print preview
     */
    async showPrintPreview() {
        try {
            const selectedPrinterIndex = document.querySelector('input[name="selected-printer"]:checked')?.value;
            const selectedPrinter = this.detectedPrinters[selectedPrinterIndex] || this.defaultPrinter;
            
            const jathagamData = this.getCurrentJathagamData();
            
            if (!jathagamData) {
                alert('❌ ஜாதக தகவல் கிடைக்கவில்லை');
                return;
            }

            // Get print preview from server
            const response = await fetch('/api/generate_print_preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    jathagam_data: jathagamData,
                    printer_info: selectedPrinter,
                    print_settings: this.printSettings
                })
            });

            if (response.ok) {
                const previewHTML = await response.text();
                this.showPreviewWindow(previewHTML);
            } else {
                alert('❌ முன்னோட்டம் உருவாக்க முடியவில்லை');
            }

        } catch (error) {
            console.error('Preview error:', error);
            alert('❌ முன்னோட்டம் பிழை: ' + error.message);
        }
    }

    /**
     * Get current Jathagam data from the page
     */
    getCurrentJathagamData() {
        // Try to get data from various possible sources
        if (window.currentJathagamData) {
            return window.currentJathagamData;
        }
        
        // Try to extract from modal
        const modal = document.getElementById('accurate-jathagam-modal') || 
                     document.getElementById('user-jathagam-result-modal');
        
        if (modal) {
            // Extract data from modal content
            return this.extractJathagamDataFromModal(modal);
        }
        
        return null;
    }

    /**
     * Extract Jathagam data from modal content
     */
    extractJathagamDataFromModal(modal) {
        // This is a simplified extraction - in practice, you'd store the data
        // when the modal is created
        const title = modal.querySelector('h3')?.textContent || '';
        const name = title.replace('🌟', '').replace('அவர்களின் ஜாதகம்', '').trim();
        
        return {
            personal_details: {
                name: name,
                // Other details would be extracted similarly
            },
            // This would be populated with actual data
            extracted_from_modal: true
        };
    }

    /**
     * Format Jathagam for printing via server
     */
    async formatJathagamForPrint(jathagamData, printerInfo) {
        try {
            const response = await fetch('/api/format_jathagam_for_print', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    jathagam_data: jathagamData,
                    printer_info: printerInfo,
                    print_settings: this.printSettings
                })
            });

            if (response.ok) {
                return await response.text();
            } else {
                throw new Error('Server formatting failed');
            }
        } catch (error) {
            console.error('Formatting error:', error);
            // Fallback to client-side formatting
            return this.formatJathagamClientSide(jathagamData, printerInfo);
        }
    }

    /**
     * Client-side fallback formatting
     */
    formatJathagamClientSide(jathagamData, printerInfo) {
        return `
        <!DOCTYPE html>
        <html lang="ta">
        <head>
            <meta charset="UTF-8">
            <title>ஜாதகம் - ${jathagamData.personal_details?.name || 'தெரியவில்லை'}</title>
            <style>
                @media print {
                    body { font-family: 'Latha', 'Tamil Sangam MN', sans-serif; margin: 20px; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <h1>🌟 தமிழ் ஜாதகம் 🌟</h1>
            <h2>${jathagamData.personal_details?.name || 'தெரியவில்லை'} அவர்களின் ஜாதகம்</h2>
            <p>அச்சுப்பொறி: ${printerInfo.name}</p>
            <p>இந்த ஜாதகம் துல்லியமான கணக்கீட்டுடன் உருவாக்கப்பட்டது.</p>
        </body>
        </html>
        `;
    }

    /**
     * Create print window
     */
    createPrintWindow(htmlContent) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(htmlContent);
        printWindow.document.close();
        printWindow.focus();
        
        // Auto-print after a short delay
        setTimeout(() => {
            printWindow.print();
        }, 500);
    }

    /**
     * Show preview window
     */
    showPreviewWindow(htmlContent) {
        const previewWindow = window.open('', '_blank');
        previewWindow.document.write(htmlContent);
        previewWindow.document.close();
        previewWindow.focus();
    }

    /**
     * Close printer dialog
     */
    closePrinterDialog() {
        const dialog = document.getElementById('printer-dialog');
        if (dialog) {
            dialog.remove();
        }
    }

    /**
     * Enhanced print function for Jathagam
     */
    async enhancedPrintJathagam() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Check if any printers are available
        if (this.detectedPrinters.length === 0) {
            alert('❌ அச்சுப்பொறி கண்டறியப்படவில்லை\n\nதயவுசெய்து அச்சுப்பொறி இணைக்கப்பட்டுள்ளதா என்பதை சரிபார்க்கவும்.');
            return;
        }

        // Check if default printer is ready
        if (this.defaultPrinter && !this.defaultPrinter.online) {
            const proceed = confirm('⚠️ இயல்புநிலை அச்சுப்பொறி ஆஃப்லைனில் உள்ளது\n\nவேறு அச்சுப்பொறியை தேர்ந்தெடுக்க விரும்புகிறீர்களா?');
            if (proceed) {
                this.showPrinterDialog();
                return;
            }
        }

        // Show printer selection dialog
        this.showPrinterDialog();
    }
}

// Initialize global instance
const enhancedPrintSystem = new EnhancedPrintSystem();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        enhancedPrintSystem.initialize();
    });
} else {
    enhancedPrintSystem.initialize();
}
