#!/usr/bin/env python3
"""
Corrected Chart Visualizer with proper South Indian layout
Fixed box arrangement following traditional Tamil Nadu style
"""
from typing import Dict, List
import json

class CorrectedChartVisualizer:
    """
    Corrected Tamil chart visualizer with proper South Indian box arrangement
    Following traditional Tamil Nadu Jathagam layout
    """
    
    def __init__(self):
        # Corrected South Indian chart layout (4x4 grid)
        # Fixed positions for each house number - Traditional Tamil arrangement
        self.south_indian_layout = {
            # Top row: Houses 5, 4, 3, 2
            5: (0, 0),   # புத்திர - Top left
            4: (0, 1),   # மாதா - Top middle-left  
            3: (0, 2),   # சகோதர - Top middle-right
            2: (0, 3),   # தன - Top right
            
            # Second row: Houses 6, [empty], [empty], 1
            6: (1, 0),   # ரிபு - Left middle
            1: (1, 3),   # லக்னம் - Right middle
            
            # Third row: Houses 7, [empty], [empty], 12
            7: (2, 0),   # கலத்திர - Left bottom
            12: (2, 3),  # வ்யய - Right top
            
            # Bottom row: Houses 8, 9, 10, 11
            8: (3, 0),   # ஆயுள் - Bottom left
            9: (3, 1),   # பாக்கிய - Bottom middle-left
            10: (3, 2),  # கர்ம - Bottom middle-right
            11: (3, 3)   # லாப - Bottom right
        }
        
        # Tamil house names
        self.tamil_house_names = [
            'லக்னம்', 'தன', 'சகோதர', 'மாதா', 'புத்திர', 'ரிபு',
            'கலத்திர', 'ஆயுள்', 'பாக்கிய', 'கர்ம', 'லாப', 'வ்யய'
        ]
        
        # Planet abbreviations for chart display
        self.planet_abbreviations = {
            'சூரியன்': 'சூ',
            'சந்திரன்': 'ச',
            'செவ்வாய்': 'செ',
            'புதன்': 'பு',
            'குரு': 'கு',
            'சுக்கிரன்': 'சு',
            'சனி': 'சனி',
            'ராகு': 'ரா',
            'கேது': 'கே'
        }
    
    def generate_corrected_chart_html(self, chart_data: Dict, chart_type: str = "rasi") -> str:
        """
        Generate HTML for corrected Tamil chart in proper South Indian style
        """
        if chart_type == "rasi":
            chart = chart_data.get('rasi_chart', {})
            title = "ராசி சக்கரம் (D1)"
            chart_class = "corrected-rasi-chart"
        else:
            chart = chart_data.get('navamsa_chart', {})
            title = "நவாம்ச சக்கரம் (D9)"
            chart_class = "corrected-navamsa-chart"
        
        chart_html = f"""
        <div class="corrected-tamil-chart-container">
            <h3 class="corrected-tamil-chart-title">{title}</h3>
            <div class="{chart_class} corrected-tamil-south-indian-chart">
        """
        
        # Create 4x4 grid with corrected layout
        for row in range(4):
            chart_html += '<div class="corrected-tamil-chart-row">'
            for col in range(4):
                # Find which house is at this position
                house_at_position = None
                for house_num in range(1, 13):
                    if self.south_indian_layout[house_num] == (row, col):
                        house_at_position = house_num
                        break
                
                if house_at_position:
                    # Generate house cell
                    house_key = f"House_{house_at_position}"
                    house_data = chart.get(house_key, {})
                    
                    house_name = self.tamil_house_names[house_at_position - 1]
                    rasi_name = house_data.get('rasi_name', '')
                    planets = house_data.get('planets', [])
                    
                    # Abbreviate planet names for display
                    planet_abbrevs = []
                    for planet in planets:
                        abbrev = self.planet_abbreviations.get(planet, planet[:2])
                        planet_abbrevs.append(abbrev)
                    
                    planets_text = ' '.join(planet_abbrevs) if planet_abbrevs else ''
                    
                    chart_html += f"""
                    <div class="corrected-tamil-chart-cell corrected-tamil-house-cell house-{house_at_position}" data-house="{house_at_position}">
                        <div class="corrected-tamil-house-number">{house_at_position}</div>
                        <div class="corrected-tamil-house-name">{house_name}</div>
                        <div class="corrected-tamil-rasi-name">{rasi_name}</div>
                        <div class="corrected-tamil-planets">{planets_text}</div>
                    </div>
                    """
                else:
                    # Empty center cells
                    chart_html += '<div class="corrected-tamil-chart-cell corrected-tamil-center-cell"></div>'
            
            chart_html += '</div>'
        
        chart_html += """
            </div>
        </div>
        """
        
        return chart_html
    
    def generate_corrected_planetary_positions_html(self, chart_data: Dict) -> str:
        """
        Generate corrected planetary positions display without errors
        """
        planetary_positions = chart_data.get('planetary_positions', {})
        
        if not planetary_positions:
            return "<p>கிரக நிலைகள் கிடைக்கவில்லை</p>"
        
        positions_html = """
        <div class="corrected-planetary-positions">
            <h4 style="color: #2c3e50; margin-bottom: 1rem; text-align: center;">
                🪐 கிரக நிலைகள் (Swiss Ephemeris கணக்கீடு)
            </h4>
            <div class="corrected-planets-grid">
        """
        
        # Order planets as per traditional Tamil Jathagam
        planet_order = ['Sun', 'Moon', 'Mars', 'Mercury', 'Jupiter', 'Venus', 'Saturn', 'Rahu', 'Ketu']
        
        for planet_name in planet_order:
            if planet_name in planetary_positions:
                planet_data = planetary_positions[planet_name]
                
                tamil_name = planet_data.get('tamil_name', planet_name)
                rasi_name = planet_data.get('rasi_name', 'தெரியவில்லை')
                nakshatra_name = planet_data.get('nakshatra_name', 'தெரியவில்லை')
                pada = planet_data.get('pada', 1)
                degrees = planet_data.get('degrees_in_sign', 0)
                longitude = planet_data.get('longitude', 0)
                
                positions_html += f"""
                <div class="corrected-planet-row">
                    <div class="corrected-planet-name">{tamil_name}</div>
                    <div class="corrected-planet-rasi">{rasi_name}</div>
                    <div class="corrected-planet-nakshatra">{nakshatra_name}</div>
                    <div class="corrected-planet-pada">{pada}ம் பாதம்</div>
                    <div class="corrected-planet-degrees">{degrees:.2f}°</div>
                    <div class="corrected-planet-longitude">{longitude:.4f}°</div>
                </div>
                """
        
        positions_html += """
            </div>
        </div>
        """
        
        return positions_html
    
    def generate_corrected_chart_css(self) -> str:
        """
        Generate corrected CSS for Tamil charts with proper styling
        """
        return """
        <style>
        .corrected-tamil-chart-container {
            margin: 1.5rem 0;
            font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', 'Suratha', sans-serif;
            text-align: center;
        }
        
        .corrected-tamil-chart-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1.2rem;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .corrected-tamil-south-indian-chart {
            display: grid;
            grid-template-rows: repeat(4, 1fr);
            width: 500px;
            height: 500px;
            margin: 0 auto;
            border: 4px solid #2c3e50;
            background: #ffffff;
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
            border-radius: 12px;
            overflow: hidden;
        }
        
        .corrected-tamil-chart-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
        }
        
        .corrected-tamil-chart-cell {
            border: 2px solid #34495e;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0.6rem;
            font-size: 0.9rem;
            position: relative;
            min-height: 120px;
            transition: all 0.3s ease;
        }
        
        .corrected-tamil-house-cell {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            cursor: pointer;
            border: 2px solid #495057;
        }
        
        .corrected-tamil-house-cell:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: scale(1.03);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 10;
        }
        
        .corrected-tamil-center-cell {
            background: linear-gradient(135deg, #f1f3f4 0%, #dadce0 100%);
            border: 1px solid #bdc3c7;
        }
        
        .corrected-tamil-house-number {
            font-size: 0.75rem;
            color: #ffffff;
            position: absolute;
            top: 4px;
            left: 4px;
            font-weight: bold;
            background: #e74c3c;
            padding: 3px 6px;
            border-radius: 4px;
            border: 1px solid #c0392b;
            min-width: 20px;
            text-align: center;
        }
        
        .corrected-tamil-house-name {
            font-size: 0.8rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-weight: 600;
            text-align: center;
            line-height: 1.2;
            background: rgba(255,255,255,0.9);
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .corrected-tamil-rasi-name {
            font-weight: bold;
            color: #8e44ad;
            font-size: 1.1rem;
            margin-bottom: 0.6rem;
            text-align: center;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
            background: rgba(142, 68, 173, 0.1);
            padding: 4px 8px;
            border-radius: 5px;
            border: 1px solid rgba(142, 68, 173, 0.3);
        }
        
        .corrected-tamil-planets {
            color: #e74c3c;
            font-weight: bold;
            font-size: 0.9rem;
            line-height: 1.5;
            text-align: center;
            background: rgba(231, 76, 60, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
            min-height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(231, 76, 60, 0.3);
            flex-wrap: wrap;
        }
        
        /* Corrected planetary positions styling */
        .corrected-planetary-positions {
            margin: 2rem 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-radius: 10px;
            border: 2px solid #dee2e6;
        }
        
        .corrected-planets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 0.8rem;
            margin-top: 1rem;
        }
        
        .corrected-planet-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1.2fr 0.8fr 0.8fr 1fr;
            gap: 0.5rem;
            padding: 0.8rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .corrected-planet-row:hover {
            background: #e3f2fd;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .corrected-planet-name {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1rem;
        }
        
        .corrected-planet-rasi {
            color: #8e44ad;
            font-weight: 600;
        }
        
        .corrected-planet-nakshatra {
            color: #27ae60;
            font-size: 0.9rem;
        }
        
        .corrected-planet-pada {
            color: #f39c12;
            font-size: 0.85rem;
        }
        
        .corrected-planet-degrees {
            color: #e74c3c;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .corrected-planet-longitude {
            color: #7f8c8d;
            font-size: 0.8rem;
        }
        
        /* Rasi chart specific styling */
        .corrected-rasi-chart {
            border-color: #2c3e50;
        }
        
        .corrected-rasi-chart .corrected-tamil-chart-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* Navamsa chart specific styling */
        .corrected-navamsa-chart {
            border-color: #8e44ad;
        }
        
        .corrected-navamsa-chart .corrected-tamil-chart-title {
            background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .corrected-tamil-south-indian-chart {
                width: 400px;
                height: 400px;
            }
            
            .corrected-tamil-chart-cell {
                font-size: 0.8rem;
                min-height: 95px;
                padding: 0.4rem;
            }
            
            .corrected-planets-grid {
                grid-template-columns: 1fr;
            }
            
            .corrected-planet-row {
                grid-template-columns: 1fr 1fr;
                gap: 0.3rem;
            }
        }
        
        @media (max-width: 480px) {
            .corrected-tamil-south-indian-chart {
                width: 350px;
                height: 350px;
            }
            
            .corrected-tamil-chart-cell {
                font-size: 0.7rem;
                min-height: 80px;
                padding: 0.3rem;
            }
        }
        </style>
        """
    
    def generate_complete_corrected_charts_html(self, chart_data: Dict) -> str:
        """
        Generate complete HTML with corrected charts and planetary positions
        """
        if not chart_data:
            return "<p>தமிழ் ஜாதக தகவல் கிடைக்கவில்லை</p>"
        
        personal = chart_data.get('personal_details', {})
        astro = chart_data.get('astrological_details', {})
        
        html = self.generate_corrected_chart_css()
        
        html += f"""
        <div class="corrected-tamil-jathagam-container">
            <div class="corrected-tamil-jathagam-header">
                <h2 style="text-align: center; color: #2c3e50; margin-bottom: 1.5rem; font-size: 1.8rem;">
                    🌟 {personal.get('name', 'தெரியவில்லை')} அவர்களின் சரியான தமிழ் ஜாதகம் 🌟
                </h2>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 1.5rem; border-radius: 10px; color: white;">
                        <h4 style="margin-bottom: 1rem; color: white;">📋 பிறப்பு விவரங்கள்</h4>
                        <div style="display: grid; gap: 0.5rem; font-size: 0.95rem;">
                            <div><strong>பிறந்த தேதி:</strong> {personal.get('birth_date', 'தெரியவில்லை')}</div>
                            <div><strong>பிறந்த நேரம்:</strong> {personal.get('birth_time', 'தெரியவில்லை')}</div>
                            <div><strong>பிறந்த இடம்:</strong> {personal.get('birth_place', 'தெரியவில்லை')}</div>
                            <div><strong>கணக்கீட்டு முறை:</strong> Swiss Ephemeris + Lahiri</div>
                        </div>
                    </div>
                    
                    <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); padding: 1.5rem; border-radius: 10px; color: white;">
                        <h4 style="margin-bottom: 1rem; color: white;">🌙 முக்கிய ஜோதிட தகவல்கள்</h4>
                        <div style="display: grid; gap: 0.5rem; font-size: 0.95rem;">
                            <div><strong>லக்ன ராசி:</strong> {astro.get('lagna_rasi', 'தெரியவில்லை')}</div>
                            <div><strong>சந்திர ராசி:</strong> {astro.get('moon_raasi', 'தெரியவில்லை')}</div>
                            <div><strong>சந்திர நட்சத்திரம்:</strong> {astro.get('moon_nakshatra', 'தெரியவில்லை')}</div>
                            <div><strong>சந்திர பாதம்:</strong> {astro.get('moon_pada', 'தெரியவில்லை')}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            {self.generate_corrected_planetary_positions_html(chart_data)}
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; margin: 2rem 0;">
                <div class="chart-section">
                    {self.generate_corrected_chart_html(chart_data, "rasi")}
                </div>
                <div class="chart-section">
                    {self.generate_corrected_chart_html(chart_data, "navamsa")}
                </div>
            </div>
            
            <div style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); padding: 1rem; border-radius: 8px; text-align: center; font-size: 0.9rem; color: #2c3e50;">
                <strong>கணக்கீட்டு முறை:</strong> {chart_data.get('calculation_method', 'Swiss Ephemeris with Lahiri Ayanamsa')}
                <br><strong>Architecture:</strong> {chart_data.get('architecture_steps', 'STEP 1-5 Implementation')}
            </div>
        </div>
        """
        
        return html

# Test function
if __name__ == "__main__":
    print("🎨 Testing Corrected Chart Visualizer...")
    
    # Sample chart data with corrected structure
    sample_chart_data = {
        'personal_details': {
            'name': 'சரியான சார்ட் சோதனை',
            'birth_date': '1990-08-15',
            'birth_time': '10:30',
            'birth_place': 'சென்னை'
        },
        'astrological_details': {
            'lagna_rasi': 'துலாம்',
            'moon_raasi': 'ரிஷபம்',
            'moon_nakshatra': 'ரோகிணி',
            'moon_pada': 2
        },
        'planetary_positions': {
            'Sun': {'tamil_name': 'சூரியன்', 'rasi_name': 'கடகம்', 'nakshatra_name': 'ஆயில்யம்', 'pada': 4, 'degrees_in_sign': 28.39, 'longitude': 118.39},
            'Moon': {'tamil_name': 'சந்திரன்', 'rasi_name': 'ரிஷபம்', 'nakshatra_name': 'ரோகிணி', 'pada': 3, 'degrees_in_sign': 18.93, 'longitude': 48.93},
            'Mars': {'tamil_name': 'செவ்வாய்', 'rasi_name': 'மேஷம்', 'nakshatra_name': 'கார்த்திகை', 'pada': 1, 'degrees_in_sign': 27.49, 'longitude': 27.49}
        },
        'rasi_chart': {
            'House_1': {'rasi_name': 'துலாம்', 'planets': ['சூரியன்']},
            'House_2': {'rasi_name': 'விருச்சிகம்', 'planets': ['சந்திரன்']},
            'House_3': {'rasi_name': 'தனுசு', 'planets': []},
            'House_4': {'rasi_name': 'மகரம்', 'planets': ['குரு']},
            'House_5': {'rasi_name': 'கும்பம்', 'planets': []},
            'House_6': {'rasi_name': 'மீனம்', 'planets': ['செவ்வாய்']},
            'House_7': {'rasi_name': 'மேஷம்', 'planets': ['சுக்கிரன்']},
            'House_8': {'rasi_name': 'ரிஷபம்', 'planets': []},
            'House_9': {'rasi_name': 'மிதுனம்', 'planets': ['சனி']},
            'House_10': {'rasi_name': 'கடகம்', 'planets': []},
            'House_11': {'rasi_name': 'சிம்மம்', 'planets': ['ராகு']},
            'House_12': {'rasi_name': 'கன்னி', 'planets': ['கேது']}
        },
        'navamsa_chart': {
            'House_1': {'rasi_name': 'மேஷம்', 'planets': ['சூரியன்']},
            'House_2': {'rasi_name': 'ரிஷபம்', 'planets': []},
            'House_3': {'rasi_name': 'மிதுனம்', 'planets': ['சந்திரன்']},
            'House_4': {'rasi_name': 'கடகம்', 'planets': []},
            'House_5': {'rasi_name': 'சிம்மம்', 'planets': ['குரு']},
            'House_6': {'rasi_name': 'கன்னி', 'planets': []},
            'House_7': {'rasi_name': 'துலாம்', 'planets': ['சுக்கிரன்']},
            'House_8': {'rasi_name': 'விருச்சிகம்', 'planets': []},
            'House_9': {'rasi_name': 'தனுசு', 'planets': ['செவ்வாய்']},
            'House_10': {'rasi_name': 'மகரம்', 'planets': ['சனி']},
            'House_11': {'rasi_name': 'கும்பம்', 'planets': []},
            'House_12': {'rasi_name': 'மீனம்', 'planets': ['ராகு', 'கேது']}
        },
        'calculation_method': 'Swiss Ephemeris with Lahiri Ayanamsa - Exact Logical Architecture',
        'architecture_steps': 'STEP 1-5 Implementation: Input→Ephemeris→Rasi→Navamsa→Visualization'
    }
    
    visualizer = CorrectedChartVisualizer()
    
    # Test corrected chart generation
    rasi_html = visualizer.generate_corrected_chart_html(sample_chart_data, "rasi")
    navamsa_html = visualizer.generate_corrected_chart_html(sample_chart_data, "navamsa")
    planetary_html = visualizer.generate_corrected_planetary_positions_html(sample_chart_data)
    complete_html = visualizer.generate_complete_corrected_charts_html(sample_chart_data)
    
    print("✅ Corrected Tamil chart HTML generation successful!")
    print(f"   Rasi chart HTML: {len(rasi_html)} characters")
    print(f"   Navamsa chart HTML: {len(navamsa_html)} characters")
    print(f"   Planetary positions HTML: {len(planetary_html)} characters")
    print(f"   Complete charts HTML: {len(complete_html)} characters")
    print("\n🎉 Corrected Chart Visualizer test completed!")
