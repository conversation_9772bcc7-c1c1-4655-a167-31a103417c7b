#!/usr/bin/env python3
"""
Database Migration Check Script
"""
import os
import sys
from pathlib import Path

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import db, Document, User

def check_migration():
    """Check database migration status and file integrity"""
    with app.app_context():
        print("=== DATABASE MIGRATION CHECK ===")
        
        # Basic counts
        total_users = User.query.count()
        total_documents = Document.query.count()
        uploaded_docs = Document.query.filter_by(document_type='uploaded').all()
        scanned_docs = Document.query.filter_by(document_type='scanned').all()
        
        print(f"📊 Database Statistics:")
        print(f"   • Total Users: {total_users}")
        print(f"   • Total Documents: {total_documents}")
        print(f"   • Uploaded Documents: {len(uploaded_docs)}")
        print(f"   • Scanned Documents: {len(scanned_docs)}")
        
        # Check file paths
        print(f"\n🔍 File Path Analysis:")
        path_issues = []
        absolute_paths = 0
        relative_paths = 0
        
        for doc in uploaded_docs:
            if os.path.isabs(doc.file_path):
                absolute_paths += 1
                exists = os.path.exists(doc.file_path)
                print(f"   ✅ {doc.filename}: Absolute path, Exists: {exists}")
                if not exists:
                    path_issues.append(f"File missing: {doc.file_path}")
            else:
                relative_paths += 1
                exists = os.path.exists(doc.file_path)
                print(f"   ⚠️  {doc.filename}: Relative path, Exists: {exists}")
                path_issues.append(f"Relative path found: {doc.file_path}")
        
        # Check user-document associations
        print(f"\n👥 User-Document Associations:")
        users_with_docs = User.query.filter(User.documents.any()).count()
        docs_without_users = Document.query.filter_by(user_id=None).count()
        
        print(f"   • Users with documents: {users_with_docs}")
        print(f"   • Documents without users: {docs_without_users}")
        
        # Check uploads folder
        uploads_folder = Path("uploads")
        if uploads_folder.exists():
            uploaded_files = list(uploads_folder.glob("*"))
            print(f"\n📁 Uploads Folder:")
            print(f"   • Physical files: {len(uploaded_files)}")
            for file in uploaded_files:
                print(f"     - {file.name} ({file.stat().st_size} bytes)")
        
        # Migration summary
        print(f"\n📋 Migration Summary:")
        if relative_paths > 0:
            print(f"   ❌ {relative_paths} documents have relative paths (needs migration)")
        else:
            print(f"   ✅ All document paths are absolute")
            
        if path_issues:
            print(f"   ❌ {len(path_issues)} file path issues found:")
            for issue in path_issues:
                print(f"     - {issue}")
        else:
            print(f"   ✅ All file paths are valid")
            
        # Test recent upload functionality
        print(f"\n🧪 Recent Upload Test:")
        recent_uploads = Document.query.filter_by(document_type='uploaded').order_by(Document.created_at.desc()).limit(3).all()
        for doc in recent_uploads:
            user_name = doc.user.name if doc.user else "No User"
            exists = os.path.exists(doc.file_path)
            print(f"   • {doc.filename} -> {user_name} (File exists: {exists})")

if __name__ == "__main__":
    check_migration()
