#!/usr/bin/env python3
"""
Precise Chart Visualizer for Rasi and Navamsa Charts
Renders charts using the precise logical architecture
"""
from typing import Dict, List

class PreciseChartVisualizer:
    """Visualize precise Rasi and Navamsa charts with proper South Indian layout"""
    
    def __init__(self):
        # South Indian chart house positions (fixed layout)
        # House positions in 4x4 grid (row, col)
        self.south_indian_positions = {
            1: (1, 3),   # லக்னம் - Right center
            2: (0, 3),   # தன - Top right
            3: (0, 2),   # சகோதர - Top center-right
            4: (0, 1),   # மாதா - Top center-left
            5: (0, 0),   # புத்திர - Top left
            6: (1, 0),   # ரிபு - Left center
            7: (2, 0),   # கலத்திர - Bottom left
            8: (3, 0),   # ஆயுள் - Bottom center-left
            9: (3, 1),   # பாக்கிய - Bottom center-right
            10: (3, 2),  # கர்ம - Bottom right
            11: (2, 3),  # லாப - Right center-bottom
            12: (1, 2)   # வ்யய - Right center-top
        }
        
        # Tamil house names
        self.house_names = [
            'லக்னம்', 'தன', 'சகோதர', 'மாதா', 'புத்திர', 'ரிபு',
            'கலத்திர', 'ஆயுள்', 'பாக்கிய', 'கர்ம', 'லாப', 'வ்யய'
        ]
        
        # Planet abbreviations in Tamil
        self.planet_abbrev = {
            'சூரியன்': 'சூ',
            'சந்திரன்': 'ச',
            'செவ்வாய்': 'செ',
            'புதன்': 'பு',
            'குரு': 'கு',
            'சுக்கிரன்': 'சு',
            'சனி': 'சனி',
            'ராகு': 'ரா',
            'கேது': 'கே'
        }
    
    def generate_precise_chart_html(self, chart_data: Dict, chart_type: str = "rasi") -> str:
        """Generate HTML for precise Rasi or Navamsa chart"""
        
        if chart_type == "rasi":
            chart = chart_data.get('rasi_chart', {})
            title = "ராசி சக்கரம் (D1)"
        else:
            chart = chart_data.get('navamsa_chart', {})
            title = "நவாம்ச சக்கரம் (D9)"
        
        chart_html = f"""
        <div class="precise-chart-container">
            <h3 class="precise-chart-title">{title}</h3>
            <div class="precise-south-indian-chart">
        """
        
        # Create 4x4 grid layout
        for row in range(4):
            chart_html += '<div class="precise-chart-row">'
            for col in range(4):
                # Find which house is in this position
                house_in_position = None
                for house_num in range(1, 13):
                    if self.south_indian_positions[house_num] == (row, col):
                        house_in_position = house_num
                        break
                
                if house_in_position:
                    # Generate house cell
                    house_key = f"House_{house_in_position}"
                    house_data = chart.get(house_key, {})
                    
                    house_name = self.house_names[house_in_position - 1]
                    rasi_name = house_data.get('rasi_name', '')
                    planets = house_data.get('planets', [])
                    
                    # Abbreviate planet names
                    planet_abbrevs = []
                    for planet in planets:
                        abbrev = self.planet_abbrev.get(planet, planet[:2])
                        planet_abbrevs.append(abbrev)
                    
                    planets_text = ' '.join(planet_abbrevs) if planet_abbrevs else ''
                    
                    chart_html += f"""
                    <div class="precise-chart-cell precise-house-cell house-{house_in_position}">
                        <div class="precise-house-number">{house_in_position}</div>
                        <div class="precise-house-name">{house_name}</div>
                        <div class="precise-rasi-name">{rasi_name}</div>
                        <div class="precise-planets">{planets_text}</div>
                    </div>
                    """
                else:
                    # Empty center cells
                    chart_html += '<div class="precise-chart-cell precise-center-cell"></div>'
            
            chart_html += '</div>'
        
        chart_html += """
            </div>
        </div>
        """
        
        return chart_html
    
    def generate_precise_chart_css(self) -> str:
        """Generate CSS for precise charts"""
        return """
        <style>
        .precise-chart-container {
            margin: 1rem 0;
            font-family: 'Latha', 'Tamil Sangam MN', 'Noto Sans Tamil', sans-serif;
        }
        
        .precise-chart-title {
            text-align: center;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #2c3e50;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        
        .precise-south-indian-chart {
            display: grid;
            grid-template-rows: repeat(4, 1fr);
            width: 450px;
            height: 450px;
            margin: 0 auto;
            border: 3px solid #2c3e50;
            background: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .precise-chart-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
        }
        
        .precise-chart-cell {
            border: 1px solid #34495e;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0.4rem;
            font-size: 0.8rem;
            position: relative;
            min-height: 110px;
        }
        
        .precise-house-cell {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transition: all 0.3s ease;
        }
        
        .precise-house-cell:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: scale(1.02);
        }
        
        .precise-center-cell {
            background: linear-gradient(135deg, #f1f3f4 0%, #dadce0 100%);
            border: none;
        }
        
        .precise-house-number {
            font-size: 0.7rem;
            color: #6c757d;
            position: absolute;
            top: 3px;
            left: 3px;
            font-weight: bold;
            background: rgba(255,255,255,0.8);
            padding: 1px 3px;
            border-radius: 3px;
        }
        
        .precise-house-name {
            font-size: 0.7rem;
            color: #495057;
            margin-bottom: 0.3rem;
            font-weight: 600;
            text-align: center;
        }
        
        .precise-rasi-name {
            font-weight: bold;
            color: #2c3e50;
            font-size: 0.9rem;
            margin-bottom: 0.4rem;
            text-align: center;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
        }
        
        .precise-planets {
            color: #dc3545;
            font-weight: bold;
            font-size: 0.8rem;
            line-height: 1.3;
            text-align: center;
            background: rgba(220, 53, 69, 0.1);
            padding: 2px 4px;
            border-radius: 4px;
            min-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* House-specific colors */
        .house-1 { border-left: 4px solid #ff6b6b; } /* லக்னம் */
        .house-2 { border-left: 4px solid #4ecdc4; } /* தன */
        .house-3 { border-left: 4px solid #45b7d1; } /* சகோதர */
        .house-4 { border-left: 4px solid #96ceb4; } /* மாதா */
        .house-5 { border-left: 4px solid #ffeaa7; } /* புத்திர */
        .house-6 { border-left: 4px solid #dda0dd; } /* ரிபு */
        .house-7 { border-left: 4px solid #98d8c8; } /* கலத்திர */
        .house-8 { border-left: 4px solid #f7dc6f; } /* ஆயுள் */
        .house-9 { border-left: 4px solid #bb8fce; } /* பாக்கிய */
        .house-10 { border-left: 4px solid #85c1e9; } /* கர்ம */
        .house-11 { border-left: 4px solid #82e0aa; } /* லாப */
        .house-12 { border-left: 4px solid #f8c471; } /* வ்யய */
        
        @media (max-width: 768px) {
            .precise-south-indian-chart {
                width: 350px;
                height: 350px;
            }
            
            .precise-chart-cell {
                font-size: 0.7rem;
                min-height: 85px;
                padding: 0.3rem;
            }
            
            .precise-house-name {
                font-size: 0.6rem;
            }
            
            .precise-rasi-name {
                font-size: 0.8rem;
            }
            
            .precise-planets {
                font-size: 0.7rem;
            }
        }
        
        @media (max-width: 480px) {
            .precise-south-indian-chart {
                width: 300px;
                height: 300px;
            }
            
            .precise-chart-cell {
                font-size: 0.6rem;
                min-height: 70px;
                padding: 0.2rem;
            }
            
            .precise-house-name {
                font-size: 0.5rem;
            }
            
            .precise-rasi-name {
                font-size: 0.7rem;
            }
            
            .precise-planets {
                font-size: 0.6rem;
            }
        }
        </style>
        """
    
    def generate_complete_precise_charts_html(self, chart_data: Dict) -> str:
        """Generate complete HTML with both precise Rasi and Navamsa charts"""
        if not chart_data:
            return "<p>துல்லியமான ஜாதக தகவல் கிடைக்கவில்லை</p>"
        
        personal = chart_data.get('personal_details', {})
        astro = chart_data.get('astrological_details', {})
        
        html = self.generate_precise_chart_css()
        
        html += f"""
        <div class="precise-jathagam-container">
            <div class="precise-jathagam-header">
                <h2 style="text-align: center; color: #2c3e50; margin-bottom: 1rem;">
                    🌟 {personal.get('name', 'தெரியவில்லை')} அவர்களின் துல்லியமான ஜாதகம் 🌟
                </h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 1.5rem; border-radius: 10px; color: white;">
                        <h4 style="margin-bottom: 1rem; color: white;">📋 பிறப்பு விவரங்கள்</h4>
                        <div style="display: grid; gap: 0.5rem; font-size: 0.9rem;">
                            <div><strong>பிறந்த தேதி:</strong> {personal.get('birth_date', 'தெரியவில்லை')}</div>
                            <div><strong>பிறந்த நேரம்:</strong> {personal.get('birth_time', 'தெரியவில்லை')}</div>
                            <div><strong>பிறந்த இடம்:</strong> {personal.get('birth_place', 'தெரியவில்லை')}</div>
                        </div>
                    </div>
                    
                    <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); padding: 1.5rem; border-radius: 10px; color: white;">
                        <h4 style="margin-bottom: 1rem; color: white;">🌙 முக்கிய ஜோதிட தகவல்கள்</h4>
                        <div style="display: grid; gap: 0.5rem; font-size: 0.9rem;">
                            <div><strong>ராசி:</strong> {astro.get('moon_raasi', 'தெரியவில்லை')}</div>
                            <div><strong>நட்சத்திரம்:</strong> {astro.get('moon_nakshatra', 'தெரியவில்லை')}</div>
                            <div><strong>லக்னம்:</strong> {astro.get('lagna_raasi', 'தெரியவில்லை')}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; margin-bottom: 2rem;">
                <div class="chart-section">
                    {self.generate_precise_chart_html(chart_data, "rasi")}
                </div>
                <div class="chart-section">
                    {self.generate_precise_chart_html(chart_data, "navamsa")}
                </div>
            </div>
            
            <div style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); padding: 1rem; border-radius: 8px; text-align: center; font-size: 0.9rem; color: #2c3e50;">
                <strong>கணக்கீட்டு முறை:</strong> {chart_data.get('calculation_method', 'Swiss Ephemeris with Precise Logic')}
            </div>
        </div>
        """
        
        return html

# Test function
if __name__ == "__main__":
    # Test chart visualization
    visualizer = PreciseChartVisualizer()
    
    # Sample data
    sample_chart_data = {
        'personal_details': {
            'name': 'துல்லிய சோதனை',
            'birth_date': '1990-08-15',
            'birth_time': '10:30',
            'birth_place': 'சென்னை'
        },
        'astrological_details': {
            'moon_raasi': 'ரிஷபம்',
            'moon_nakshatra': 'ரோகிணி',
            'lagna_raasi': 'துலாம்'
        },
        'rasi_chart': {
            'House_1': {'rasi_name': 'துலாம்', 'planets': ['சூரியன்']},
            'House_2': {'rasi_name': 'விருச்சிகம்', 'planets': ['சந்திரன்']},
            'House_3': {'rasi_name': 'தனுசு', 'planets': []},
            'House_4': {'rasi_name': 'மகரம்', 'planets': ['குரு']},
            'House_5': {'rasi_name': 'கும்பம்', 'planets': []},
            'House_6': {'rasi_name': 'மீனம்', 'planets': ['செவ்வாய்']},
            'House_7': {'rasi_name': 'மேஷம்', 'planets': ['சுக்கிரன்']},
            'House_8': {'rasi_name': 'ரிஷபம்', 'planets': []},
            'House_9': {'rasi_name': 'மிதுனம்', 'planets': ['சனி']},
            'House_10': {'rasi_name': 'கடகம்', 'planets': []},
            'House_11': {'rasi_name': 'சிம்மம்', 'planets': ['ராகு']},
            'House_12': {'rasi_name': 'கன்னி', 'planets': ['கேது']}
        },
        'navamsa_chart': {
            'House_1': {'rasi_name': 'மேஷம்', 'planets': ['சூரியன்']},
            'House_2': {'rasi_name': 'ரிஷபம்', 'planets': []},
            'House_3': {'rasi_name': 'மிதுனம்', 'planets': ['சந்திரன்']},
            'House_4': {'rasi_name': 'கடகம்', 'planets': []},
            'House_5': {'rasi_name': 'சிம்மம்', 'planets': ['குரு']},
            'House_6': {'rasi_name': 'கன்னி', 'planets': []},
            'House_7': {'rasi_name': 'துலாம்', 'planets': ['சுக்கிரன்']},
            'House_8': {'rasi_name': 'விருச்சிகம்', 'planets': []},
            'House_9': {'rasi_name': 'தனுசு', 'planets': ['செவ்வாய்']},
            'House_10': {'rasi_name': 'மகரம்', 'planets': ['சனி']},
            'House_11': {'rasi_name': 'கும்பம்', 'planets': []},
            'House_12': {'rasi_name': 'மீனம்', 'planets': ['ராகு', 'கேது']}
        }
    }
    
    html_output = visualizer.generate_complete_precise_charts_html(sample_chart_data)
    print("✅ Precise chart HTML generated successfully!")
    print("Charts use exact logical architecture with proper house positioning.")
