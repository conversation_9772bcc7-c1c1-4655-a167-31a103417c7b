#!/usr/bin/env python3
"""
Test the accurate Jathagam generation system
"""
import os
import sys
import requests
import json
from datetime import date, time

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_accurate_jathagam_generation():
    """Test accurate Jathagam generation with Swiss Ephemeris"""
    print("=== Testing Accurate Jathagam Generation ===")
    
    try:
        from accurate_jathagam_generator import AccurateJathagamGenerator, format_jathagam_for_display
        
        # Test data
        test_name = "அக்குரேட் சோதனை"
        test_date = date(1990, 8, 15)
        test_time = time(10, 30)
        test_place = "சென்னை"
        test_gender = "Male"
        
        # Generate accurate Jathagam
        generator = AccurateJathagamGenerator()
        jathagam = generator.generate_accurate_jathagam(
            test_name, test_date, test_time, test_place, test_gender
        )
        
        if jathagam:
            print("✅ Accurate Jathagam generated successfully!")
            
            # Check personal details
            personal = jathagam.get('personal_details', {})
            print(f"   Name: {personal.get('name')}")
            print(f"   Birth Place: {personal.get('birth_place')}")
            print(f"   Coordinates: {personal.get('latitude')}, {personal.get('longitude')}")
            print(f"   Timezone: {personal.get('timezone')}")
            
            # Check astrological details
            astro = jathagam.get('astrological_details', {})
            print(f"   Moon Raasi: {astro.get('moon_raasi')}")
            print(f"   Moon Nakshatra: {astro.get('moon_nakshatra')}")
            print(f"   Lagna Raasi: {astro.get('lagna_raasi')}")
            
            # Check planetary positions
            planets = jathagam.get('planetary_positions', {})
            print(f"   Planets calculated: {len(planets)}")
            
            # Check charts
            rasi_chart = jathagam.get('rasi_chart', {})
            navamsa_chart = jathagam.get('navamsa_chart', {})
            print(f"   Rasi chart houses: {len(rasi_chart)}")
            print(f"   Navamsa chart houses: {len(navamsa_chart)}")
            
            return True
        else:
            print("❌ Accurate Jathagam generation failed")
            return False
            
    except ImportError as e:
        print(f"❌ Required libraries not available: {e}")
        print("Note: Ensure pyswisseph, geopy, and timezonefinder are installed")
        return False
    except Exception as e:
        print(f"❌ Accurate Jathagam test error: {e}")
        return False

def test_tamil_chart_generation():
    """Test Tamil chart generation"""
    print("\n=== Testing Tamil Chart Generation ===")
    
    try:
        from tamil_chart_generator import TamilChartGenerator
        
        # Sample chart data
        sample_rasi_chart = {
            'லக்னம்': {'raasi': 'மேஷம்', 'planets': ['சூரியன்']},
            'தன': {'raasi': 'ரிஷபம்', 'planets': ['சந்திரன்', 'புதன்']},
            'சகோதர': {'raasi': 'மிதுனம்', 'planets': []},
            'மாதா': {'raasi': 'கடகம்', 'planets': ['குரு']},
            'புத்திர': {'raasi': 'சிம்மம்', 'planets': []},
            'ரிபு': {'raasi': 'கன்னி', 'planets': ['செவ்வாய்']},
            'கலத்திர': {'raasi': 'துலாம்', 'planets': ['சுக்கிரன்']},
            'ஆயுள்': {'raasi': 'விருச்சிகம்', 'planets': []},
            'பாக்கிய': {'raasi': 'தனுசு', 'planets': ['சனி']},
            'கர்ம': {'raasi': 'மகரம்', 'planets': []},
            'லாப': {'raasi': 'கும்பம்', 'planets': ['ராகு']},
            'வ்யய': {'raasi': 'மீனம்', 'planets': ['கேது']}
        }
        
        chart_gen = TamilChartGenerator()
        
        # Test Rasi chart HTML generation
        rasi_html = chart_gen.generate_rasi_chart_html(sample_rasi_chart)
        if rasi_html and len(rasi_html) > 100:
            print("✅ Tamil Rasi chart HTML generated successfully!")
        else:
            print("❌ Tamil Rasi chart HTML generation failed")
            return False
        
        # Test CSS generation
        css = chart_gen.generate_chart_css()
        if css and 'south-indian-chart' in css:
            print("✅ Tamil chart CSS generated successfully!")
        else:
            print("❌ Tamil chart CSS generation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Tamil chart test error: {e}")
        return False

def test_web_jathagam_generation():
    """Test web-based Jathagam generation"""
    print("\n=== Testing Web Jathagam Generation ===")
    
    try:
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        login_response = session.post('http://localhost:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ Login failed")
            return False
        
        # Generate accurate Jathagam via web
        data = {
            'name': 'வெப் சோதனை',
            'gender': 'Female',
            'birth_date': '1992-12-25',
            'birth_time': '15:45',
            'birth_place': 'மதுரை'
        }
        
        response = session.post('http://localhost:5000/generate_standalone_jathagam', data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Web Jathagam generation successful!")
                
                jathagam = result.get('jathagam', {})
                personal = jathagam.get('personal_details', {})
                astro = jathagam.get('astrological_details', {})
                
                print(f"   Name: {personal.get('name')}")
                print(f"   Birth Place: {personal.get('birth_place')}")
                print(f"   Moon Raasi: {astro.get('moon_raasi')}")
                print(f"   Lagna Raasi: {astro.get('lagna_raasi')}")
                
                # Check if chart HTML is generated
                chart_html = result.get('chart_html', '')
                if chart_html and len(chart_html) > 100:
                    print("✅ Chart HTML generated successfully!")
                else:
                    print("⚠️ Chart HTML not generated or empty")
                
                return True
            else:
                print(f"❌ Web Jathagam generation failed: {result.get('message')}")
                return False
        else:
            print(f"❌ Web request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web Jathagam test error: {e}")
        return False

def test_coordinate_accuracy():
    """Test coordinate and timezone accuracy"""
    print("\n=== Testing Location Services ===")
    
    try:
        from accurate_jathagam_generator import LocationService
        
        location_service = LocationService()
        
        # Test Chennai coordinates
        lat, lng = location_service.get_coordinates("சென்னை")
        print(f"Chennai coordinates: {lat}, {lng}")
        
        if abs(lat - 13.0827) < 1 and abs(lng - 80.2707) < 1:
            print("✅ Chennai coordinates accurate!")
        else:
            print("⚠️ Chennai coordinates may be inaccurate")
        
        # Test timezone
        timezone = location_service.get_timezone(lat, lng)
        print(f"Timezone: {timezone}")
        
        if timezone == 'Asia/Kolkata':
            print("✅ Timezone detection accurate!")
            return True
        else:
            print("⚠️ Timezone may be inaccurate")
            return True  # Still pass as it's functional
            
    except Exception as e:
        print(f"❌ Location services test error: {e}")
        return False

def main():
    """Run all accurate Jathagam tests"""
    print("🧪 Testing Accurate Jathagam System with Swiss Ephemeris...")
    
    tests = [
        test_coordinate_accuracy,
        test_accurate_jathagam_generation,
        test_tamil_chart_generation,
        test_web_jathagam_generation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All accurate Jathagam tests passed!")
        print("\n✅ Accurate System Features:")
        print("  🌌 Swiss Ephemeris: Professional astronomical calculations")
        print("  📍 Location Services: Accurate coordinates and timezones")
        print("  🎯 Precise Calculations: Correct Rasi, Nakshatra, Lagna")
        print("  📊 Tamil Charts: South Indian style with Unicode support")
        print("  🌐 Web Integration: Complete web-based generation")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        print("\nNote: Ensure all required libraries are installed:")
        print("  pip install pyswisseph geopy timezonefinder")

if __name__ == "__main__":
    main()
