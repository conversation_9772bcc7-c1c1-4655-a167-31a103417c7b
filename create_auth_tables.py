#!/usr/bin/env python3
"""
Create authentication tables and add default admin user
"""
import os
import sys

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import db, <PERSON>th<PERSON><PERSON>

def create_auth_tables():
    """Create authentication tables and add default admin"""
    with app.app_context():
        print("Creating authentication tables...")
        
        # Create all tables
        db.create_all()
        
        # Check if admin user already exists
        admin_user = AuthUser.query.filter_by(username='admin').first()
        
        if not admin_user:
            print("Creating default admin user...")
            # Create default admin user
            admin_user = AuthUser(
                username='admin',
                email='<EMAIL>',
                full_name='நிர்வாகி',  # Admin in Tamil
                city='Chennai',
                role='admin'
            )
            admin_user.set_password('admin123')  # Default password
            
            db.session.add(admin_user)
            db.session.commit()
            
            print("✅ Default admin user created!")
            print("   Username: admin")
            print("   Password: admin123")
            print("   Role: admin")
        else:
            print("✅ Admin user already exists")
        
        # Create a test regular user
        test_user = AuthUser.query.filter_by(username='user').first()
        if not test_user:
            print("Creating test user...")
            test_user = AuthUser(
                username='user',
                email='<EMAIL>',
                full_name='சோதனை பயனர்',  # Test User in Tamil
                city='Mumbai',
                role='user'
            )
            test_user.set_password('user123')  # Default password
            
            db.session.add(test_user)
            db.session.commit()
            
            print("✅ Test user created!")
            print("   Username: user")
            print("   Password: user123")
            print("   Role: user")
        else:
            print("✅ Test user already exists")
        
        print("\n🎉 Authentication system setup complete!")
        print("\nYou can now login with:")
        print("Admin: username=admin, password=admin123")
        print("User:  username=user, password=user123")

if __name__ == "__main__":
    create_auth_tables()
