#!/usr/bin/env python3
"""
Test the new manual entry with required file upload functionality
"""
import os
import sys
from werkzeug.datastructures import FileStorage
from io import BytesIO
from PIL import Image

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import db, User, Document

def test_manual_entry_with_file():
    """Test the new manual entry that requires file upload"""
    with app.app_context():
        print("=== Testing Manual Entry with Required File Upload ===")
        
        # Count before
        users_before = User.query.count()
        docs_before = Document.query.count()
        print(f"Before: {users_before} users, {docs_before} documents")
        
        # Test 1: Try manual entry without file (should fail)
        print("\n1. Testing manual entry without file (should fail)...")
        with app.test_client() as client:
            response = client.post('/', 
                                 data={'name': 'John Test', 'city': 'Test City'},
                                 content_type='application/x-www-form-urlencoded')
            print(f"Response without file: {response.status_code}")
        
        # Test 2: Manual entry with file (should succeed)
        print("\n2. Testing manual entry with file (should succeed)...")
        
        # Create a test image
        img = Image.new('RGB', (100, 100), color='blue')
        img_buffer = BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        file_storage = FileStorage(
            stream=img_buffer,
            filename='test_manual_doc.png',
            content_type='image/png'
        )
        
        with app.test_client() as client:
            response = client.post('/', 
                                 data={
                                     'name': 'Jane Manual',
                                     'city': 'Manual City',
                                     'user_file': file_storage
                                 },
                                 content_type='multipart/form-data')
            print(f"Response with file: {response.status_code}")
        
        # Count after
        users_after = User.query.count()
        docs_after = Document.query.count()
        print(f"\nAfter: {users_after} users, {docs_after} documents")
        print(f"Added: {users_after - users_before} users, {docs_after - docs_before} documents")
        
        # Check the new user and document
        if users_after > users_before:
            new_user = User.query.filter_by(name='Jane Manual', city='Manual City').first()
            if new_user:
                print(f"\n✅ New user created: {new_user.name} from {new_user.city} (source: {new_user.source})")
                user_docs = Document.query.filter_by(user_id=new_user.id).all()
                print(f"   Documents associated: {len(user_docs)}")
                for doc in user_docs:
                    exists = os.path.exists(doc.file_path)
                    print(f"   - {doc.filename} (exists: {exists})")
        
        # Test 3: Test with a CSV file 
        print("\n3. Testing manual entry with CSV file...")
        csv_content = "Sample,Data\nfor,testing"
        csv_buffer = BytesIO(csv_content.encode('utf-8'))
        
        csv_storage = FileStorage(
            stream=csv_buffer,
            filename='test_manual.csv',
            content_type='text/csv'
        )
        
        with app.test_client() as client:
            response = client.post('/', 
                                 data={
                                     'name': 'Bob CSV',
                                     'city': 'CSV City',
                                     'user_file': csv_storage
                                 },
                                 content_type='multipart/form-data')
            print(f"Response with CSV: {response.status_code}")
        
        # Final count
        users_final = User.query.count()
        docs_final = Document.query.count()
        print(f"\nFinal: {users_final} users, {docs_final} documents")
        print(f"Total added: {users_final - users_before} users, {docs_final - docs_before} documents")
        
        print("\n✅ Manual entry with required file upload test completed!")

if __name__ == "__main__":
    test_manual_entry_with_file()
