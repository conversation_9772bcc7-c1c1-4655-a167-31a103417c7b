/**
 * Scanner Printer System - Client-Side Architecture
 * Following the exact architecture:
 * Document Scanning via Printer/Scanner on Client Side
 * 
 * High-Level Workflow:
 * User Device (Client Side) → Connect to Scanner/Printer → Start Scan → 
 * Capture Image → Upload/Save/OCR/PDF → Send to Server
 */

class ScannerPrinterSystem {
    constructor() {
        this.scanners = [];
        this.selectedScanner = null;
        this.scanSettings = {
            dpi: 300,
            format: 'JPEG',
            mode: 'Color',
            area: 'A4'
        };
        this.scannedDocument = null;
        this.isInitialized = false;
        console.log('🖨️ Scanner Printer System initialized - Client-Side Architecture');
    }

    /**
     * Initialize scanner detection following the workflow
     * User Device (Client Side) → Connect to Scanner/Printer
     */
    async initialize() {
        console.log('🔄 Starting Client-Side Scanner Detection Workflow...');
        
        try {
            // Step 1: Connect to Scanner/Printer using native interface
            await this.connectToScannerPrinter();
            
            // Step 2: Update UI with detected scanners
            this.updateScannerUI();
            
            this.isInitialized = true;
            console.log('✅ Scanner Printer System initialized successfully');
            
        } catch (error) {
            console.error('❌ Scanner initialization error:', error);
            this.showError('ஸ்கேனர் துவக்கம் பிழை: ' + error.message);
        }
    }

    /**
     * Connect to Scanner/Printer using native interface
     * Architecture: Local USB/Wi-Fi → TWAIN/WIA/SANE/ICA
     */
    async connectToScannerPrinter() {
        console.log('🔌 Connecting to Scanner/Printer using native interface...');
        
        // Option 1: Windows - WIA/TWAIN
        await this.detectWindowsScanners();
        
        // Option 2: macOS - ICA
        await this.detectMacOSScanners();
        
        // Option 3: Linux - SANE
        await this.detectLinuxScanners();
        
        // Option 4: Cross-platform - pyinsane2
        await this.detectCrossPlatformScanners();
        
        // Option 5: Web-based with agent
        await this.detectWebBasedScanners();
        
        console.log(`✅ Scanner detection completed: ${this.scanners.length} scanners found`);
    }

    /**
     * Windows Scanner Detection - WIA/TWAIN
     */
    async detectWindowsScanners() {
        console.log('🪟 Detecting Windows scanners (WIA/TWAIN)...');
        
        try {
            // Check for Windows WIA interface
            if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.getWIAScanners) {
                const wiaScanners = await window.electronAPI.getWIAScanners();
                wiaScanners.forEach(scanner => {
                    this.scanners.push({
                        name: scanner.name,
                        type: 'WIA Scanner',
                        interface: 'WIA',
                        os: 'Windows',
                        connection: scanner.connection || 'USB',
                        capabilities: {
                            dpi: [150, 300, 600, 1200],
                            formats: ['JPEG', 'PNG', 'PDF'],
                            modes: ['Color', 'Grayscale', 'BlackWhite'],
                            areas: ['A4', 'Letter', 'Custom']
                        },
                        available: true,
                        method: 'wia_scan',
                        device: scanner
                    });
                });
                console.log(`✅ Found ${wiaScanners.length} WIA scanners`);
            }
            
            // Check for TWAIN interface
            if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.getTWAINScanners) {
                const twainScanners = await window.electronAPI.getTWAINScanners();
                twainScanners.forEach(scanner => {
                    this.scanners.push({
                        name: scanner.name,
                        type: 'TWAIN Scanner',
                        interface: 'TWAIN',
                        os: 'Windows',
                        connection: scanner.connection || 'USB',
                        capabilities: {
                            dpi: [150, 300, 600, 1200],
                            formats: ['JPEG', 'PNG', 'PDF', 'TIFF'],
                            modes: ['Color', 'Grayscale', 'BlackWhite'],
                            areas: ['A4', 'Letter', 'Custom']
                        },
                        available: true,
                        method: 'twain_scan',
                        device: scanner
                    });
                });
                console.log(`✅ Found ${twainScanners.length} TWAIN scanners`);
            }
            
        } catch (error) {
            console.log('⚠️ Windows scanner detection failed:', error.message);
        }
    }

    /**
     * macOS Scanner Detection - ICA
     */
    async detectMacOSScanners() {
        console.log('🍎 Detecting macOS scanners (ICA)...');
        
        try {
            // Check for macOS ICA interface
            if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.getICAScanners) {
                const icaScanners = await window.electronAPI.getICAScanners();
                icaScanners.forEach(scanner => {
                    this.scanners.push({
                        name: scanner.name,
                        type: 'ICA Scanner',
                        interface: 'ICA',
                        os: 'macOS',
                        connection: scanner.connection || 'USB',
                        capabilities: {
                            dpi: [150, 300, 600],
                            formats: ['JPEG', 'PNG', 'PDF'],
                            modes: ['Color', 'Grayscale'],
                            areas: ['A4', 'Letter', 'Custom']
                        },
                        available: true,
                        method: 'ica_scan',
                        device: scanner
                    });
                });
                console.log(`✅ Found ${icaScanners.length} ICA scanners`);
            }
            
        } catch (error) {
            console.log('⚠️ macOS scanner detection failed:', error.message);
        }
    }

    /**
     * Linux Scanner Detection - SANE
     */
    async detectLinuxScanners() {
        console.log('🐧 Detecting Linux scanners (SANE)...');
        
        try {
            // Check for Linux SANE interface
            if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.getSANEScanners) {
                const saneScanners = await window.electronAPI.getSANEScanners();
                saneScanners.forEach(scanner => {
                    this.scanners.push({
                        name: scanner.name,
                        type: 'SANE Scanner',
                        interface: 'SANE',
                        os: 'Linux',
                        connection: scanner.connection || 'USB',
                        capabilities: {
                            dpi: [150, 300, 600, 1200],
                            formats: ['JPEG', 'PNG', 'PDF', 'TIFF'],
                            modes: ['Color', 'Grayscale', 'BlackWhite'],
                            areas: ['A4', 'Letter', 'Custom']
                        },
                        available: true,
                        method: 'sane_scan',
                        device: scanner
                    });
                });
                console.log(`✅ Found ${saneScanners.length} SANE scanners`);
            }
            
        } catch (error) {
            console.log('⚠️ Linux scanner detection failed:', error.message);
        }
    }

    /**
     * Cross-platform Scanner Detection - pyinsane2
     */
    async detectCrossPlatformScanners() {
        console.log('🌐 Detecting cross-platform scanners (pyinsane2)...');
        
        try {
            // Check for pyinsane2 interface
            if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.getPyinsaneScanners) {
                const pyinsaneScanners = await window.electronAPI.getPyinsaneScanners();
                pyinsaneScanners.forEach(scanner => {
                    this.scanners.push({
                        name: scanner.name,
                        type: 'Pyinsane2 Scanner',
                        interface: 'pyinsane2',
                        os: 'Cross-platform',
                        connection: scanner.connection || 'USB',
                        capabilities: {
                            dpi: [150, 300, 600, 1200],
                            formats: ['JPEG', 'PNG', 'PDF', 'TIFF'],
                            modes: ['Color', 'Grayscale', 'BlackWhite'],
                            areas: ['A4', 'Letter', 'Custom']
                        },
                        available: true,
                        method: 'pyinsane_scan',
                        device: scanner
                    });
                });
                console.log(`✅ Found ${pyinsaneScanners.length} pyinsane2 scanners`);
            }
            
        } catch (error) {
            console.log('⚠️ Cross-platform scanner detection failed:', error.message);
        }
    }

    /**
     * Web-based Scanner Detection with Agent
     */
    async detectWebBasedScanners() {
        console.log('🌐 Detecting web-based scanners with agent...');
        
        try {
            // Check for Dynamsoft Web TWAIN
            if (typeof Dynamsoft !== 'undefined' && Dynamsoft.DWT) {
                console.log('✅ Dynamsoft Web TWAIN agent detected');
                
                const DWObject = await Dynamsoft.DWT.GetWebTwain('dwtcontrolContainer');
                if (DWObject) {
                    const sourceCount = DWObject.SourceCount;
                    
                    for (let i = 0; i < sourceCount; i++) {
                        const sourceName = DWObject.GetSourceNameItems(i);
                        this.scanners.push({
                            name: sourceName,
                            type: 'Web TWAIN Scanner',
                            interface: 'Web TWAIN',
                            os: 'Web-based',
                            connection: 'Agent',
                            capabilities: {
                                dpi: [150, 300, 600, 1200],
                                formats: ['JPEG', 'PNG', 'PDF', 'TIFF'],
                                modes: ['Color', 'Grayscale', 'BlackWhite'],
                                areas: ['A4', 'Letter', 'Custom']
                            },
                            available: true,
                            method: 'web_twain_scan',
                            dwObject: DWObject,
                            sourceIndex: i
                        });
                    }
                }
            }
            
            // Check for custom WIA Agent
            if (typeof window !== 'undefined' && window.wiaAgent) {
                console.log('✅ Custom WIA Agent detected');
                const agentScanners = await window.wiaAgent.getScanners();
                agentScanners.forEach(scanner => {
                    this.scanners.push({
                        name: scanner.name,
                        type: 'WIA Agent Scanner',
                        interface: 'WIA Agent',
                        os: 'Web-based',
                        connection: 'Local Agent',
                        capabilities: scanner.capabilities,
                        available: true,
                        method: 'wia_agent_scan',
                        device: scanner
                    });
                });
            }
            
        } catch (error) {
            console.log('⚠️ Web-based scanner detection failed:', error.message);
        }
    }

    /**
     * Update scanner UI
     */
    updateScannerUI() {
        const statusDiv = document.getElementById('scanner-printer-status');
        const listDiv = document.getElementById('scanner-printer-list');
        const controlsDiv = document.getElementById('scanner-printer-controls');
        
        if (this.scanners.length > 0) {
            statusDiv.innerHTML = `
                <div style="color: #155724; background: #d4edda; padding: 1rem; border-radius: 5px; border-left: 4px solid #28a745;">
                    ✅ ${this.scanners.length} ஸ்கேனர்/பிரிண்டர் கண்டறியப்பட்டது
                    <div style="font-size: 0.9rem; margin-top: 0.5rem;">
                        🔌 Native Interface: TWAIN/WIA/SANE/ICA
                    </div>
                </div>
            `;
            
            listDiv.innerHTML = this.generateScannerListHTML();
            listDiv.style.display = 'block';
            
            controlsDiv.style.display = 'block';
            
            // Select first scanner by default
            this.selectedScanner = this.scanners[0];
            
            // Show scan button
            document.getElementById('start-scanner-printer-btn').style.display = 'inline-block';
        } else {
            statusDiv.innerHTML = `
                <div style="color: #721c24; background: #f8d7da; padding: 1rem; border-radius: 5px; border-left: 4px solid #dc3545;">
                    ❌ ஸ்கேனர்/பிரிண்டர் கண்டறியப்படவில்லை
                    <div style="font-size: 0.9rem; margin-top: 0.5rem;">
                        💡 Desktop app அல்லது Web TWAIN agent தேவை
                    </div>
                </div>
            `;
            
            this.showInstallationOptions();
        }
    }

    /**
     * Generate scanner list HTML
     */
    generateScannerListHTML() {
        return this.scanners.map((scanner, index) => `
            <div style="border: 1px solid #ddd; padding: 1rem; border-radius: 5px; margin-bottom: 0.5rem; ${index === 0 ? 'background: #e8f5e8; border-color: #28a745;' : ''}">
                <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="radio" name="selected-scanner-printer" value="${index}" ${index === 0 ? 'checked' : ''} onchange="scannerPrinter.selectScanner(${index})">
                    <div style="margin-left: 0.5rem; flex: 1;">
                        <div style="font-weight: bold; color: #333;">
                            🖨️ ${scanner.name}
                        </div>
                        <div style="font-size: 0.9em; color: #666;">
                            Interface: ${scanner.interface} | OS: ${scanner.os} | Connection: ${scanner.connection}
                        </div>
                        <div style="font-size: 0.8em; color: #007bff; margin-top: 0.2rem;">
                            📋 Formats: ${scanner.capabilities.formats.join(', ')}
                        </div>
                        <div style="font-size: 0.8em; color: #28a745; margin-top: 0.2rem;">
                            🔧 DPI: ${scanner.capabilities.dpi.join(', ')} | Modes: ${scanner.capabilities.modes.join(', ')}
                        </div>
                    </div>
                </label>
            </div>
        `).join('');
    }

    /**
     * Select scanner
     */
    selectScanner(index) {
        this.selectedScanner = this.scanners[index];
        console.log('Selected scanner:', this.selectedScanner.name);
        this.updateScannerUI();
    }

    /**
     * Start scan process following the workflow
     * Start Scan (user clicks) → Set resolution, format
     */
    async startScan() {
        if (!this.selectedScanner) {
            alert('❌ ஸ்கேனர் தேர்ந்தெடுக்கப்படவில்லை');
            return;
        }

        console.log('🔄 Starting scan workflow with:', this.selectedScanner.name);
        
        try {
            // Update scan settings from UI
            this.updateScanSettings();
            
            // Start scan based on interface type
            switch (this.selectedScanner.method) {
                case 'wia_scan':
                    await this.scanWithWIA();
                    break;
                case 'twain_scan':
                    await this.scanWithTWAIN();
                    break;
                case 'ica_scan':
                    await this.scanWithICA();
                    break;
                case 'sane_scan':
                    await this.scanWithSANE();
                    break;
                case 'pyinsane_scan':
                    await this.scanWithPyinsane();
                    break;
                case 'web_twain_scan':
                    await this.scanWithWebTWAIN();
                    break;
                case 'wia_agent_scan':
                    await this.scanWithWIAAgent();
                    break;
                default:
                    throw new Error('Unsupported scan method: ' + this.selectedScanner.method);
            }
        } catch (error) {
            console.error('Scan error:', error);
            alert('❌ ஸ்கேன் பிழை: ' + error.message);
        }
    }

    /**
     * Update scan settings from UI
     */
    updateScanSettings() {
        this.scanSettings.dpi = parseInt(document.getElementById('scanner-printer-dpi').value);
        this.scanSettings.format = document.getElementById('scanner-printer-format').value;
        this.scanSettings.mode = document.getElementById('scanner-printer-mode').value;
        this.scanSettings.area = document.getElementById('scanner-printer-area').value;
        
        console.log('📋 Scan settings updated:', this.scanSettings);
    }

    /**
     * Scan with Windows WIA
     */
    async scanWithWIA() {
        console.log('🪟 Scanning with WIA interface...');
        
        try {
            const scanResult = await window.electronAPI.scanWithWIA({
                device: this.selectedScanner.device,
                settings: this.scanSettings
            });
            
            if (scanResult.success) {
                this.processScanResult(scanResult);
            } else {
                throw new Error(scanResult.error);
            }
        } catch (error) {
            throw new Error('WIA scan failed: ' + error.message);
        }
    }

    /**
     * Scan with TWAIN
     */
    async scanWithTWAIN() {
        console.log('🔌 Scanning with TWAIN interface...');
        
        try {
            const scanResult = await window.electronAPI.scanWithTWAIN({
                device: this.selectedScanner.device,
                settings: this.scanSettings
            });
            
            if (scanResult.success) {
                this.processScanResult(scanResult);
            } else {
                throw new Error(scanResult.error);
            }
        } catch (error) {
            throw new Error('TWAIN scan failed: ' + error.message);
        }
    }

    /**
     * Scan with Web TWAIN
     */
    async scanWithWebTWAIN() {
        console.log('🌐 Scanning with Web TWAIN...');
        
        try {
            const scanner = this.selectedScanner;
            const DWObject = scanner.dwObject;
            
            // Select scanner source
            DWObject.SelectSourceByIndex(scanner.sourceIndex);
            
            // Configure scan settings
            DWObject.Resolution = this.scanSettings.dpi;
            DWObject.PixelType = this.getPixelType(this.scanSettings.mode);
            
            // Acquire image
            DWObject.AcquireImage();
            
            // Wait for scan completion
            DWObject.RegisterEvent('OnPostTransfer', () => {
                console.log('✅ Web TWAIN scan completed');
                this.processWebTWAINScan(DWObject);
            });
            
        } catch (error) {
            throw new Error('Web TWAIN scan failed: ' + error.message);
        }
    }

    /**
     * Process scan result
     * Capture image (JPG/PNG/PDF) → Show preview to user
     */
    processScanResult(scanResult) {
        console.log('📄 Processing scan result...');
        
        try {
            this.scannedDocument = {
                file: scanResult.file,
                path: scanResult.path,
                format: scanResult.format,
                size: scanResult.size,
                settings: this.scanSettings,
                scanner: this.selectedScanner.name
            };
            
            this.showScanPreview(scanResult);
            
        } catch (error) {
            console.error('Scan processing error:', error);
        }
    }

    /**
     * Show scan preview
     */
    showScanPreview(scanResult) {
        const previewDiv = document.getElementById('scanner-printer-preview');
        previewDiv.innerHTML = `
            <h4 style="color: #333; margin-bottom: 1rem;">👁️ Scan Preview</h4>
            <div style="text-align: center; padding: 1rem; border: 1px solid #ddd; border-radius: 5px;">
                ${scanResult.format.toLowerCase().includes('image') ? 
                    `<img src="${scanResult.dataUrl || scanResult.path}" style="max-width: 100%; max-height: 300px; border: 1px solid #ddd;">` :
                    `<div style="font-size: 3rem; margin-bottom: 1rem;">📄</div><div>${scanResult.filename}</div>`
                }
                <div style="margin-top: 1rem; font-size: 0.9rem; color: #666;">
                    <strong>File:</strong> ${scanResult.filename}<br>
                    <strong>Format:</strong> ${scanResult.format}<br>
                    <strong>Size:</strong> ${(scanResult.size / 1024 / 1024).toFixed(2)} MB<br>
                    <strong>DPI:</strong> ${this.scanSettings.dpi}<br>
                    <strong>Mode:</strong> ${this.scanSettings.mode}<br>
                    <strong>Scanner:</strong> ${this.selectedScanner.name}
                </div>
            </div>
        `;
        previewDiv.style.display = 'block';
        
        // Show save/upload buttons
        document.getElementById('save-scanner-printer-btn').style.display = 'inline-block';
        document.getElementById('upload-scanner-printer-btn').style.display = 'inline-block';
    }

    /**
     * Save scanned document
     * Upload / Save / OCR / PDF → Send to Server if needed
     */
    async saveScannedDocument() {
        if (!this.scannedDocument) {
            alert('❌ ஸ்கேன் செய்யப்பட்ட ஆவணம் இல்லை');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('file', this.scannedDocument.file);
            formData.append('document_type', 'scanner_printer_document');
            formData.append('scanner_interface', this.selectedScanner.interface);
            formData.append('scanner_name', this.selectedScanner.name);
            formData.append('scan_settings', JSON.stringify(this.scanSettings));

            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                alert('✅ ஆவணம் வெற்றிகரமாக சேமிக்கப்பட்டது!');
                this.closeModal();
            } else {
                throw new Error(result.message || 'Upload failed');
            }
        } catch (error) {
            console.error('Save error:', error);
            alert('❌ சேமிப்பு பிழை: ' + error.message);
        }
    }

    /**
     * Show installation options
     */
    showInstallationOptions() {
        const optionsDiv = document.getElementById('scanner-printer-options');
        optionsDiv.innerHTML = `
            <div style="background: #fff3cd; padding: 1rem; border-radius: 5px; margin: 1rem 0; border-left: 4px solid #ffc107;">
                <h4 style="color: #856404;">🛠️ Installation Options</h4>
                <div style="margin: 1rem 0;">
                    <h5 style="color: #856404;">Option 1: Desktop App (Recommended)</h5>
                    <p style="color: #856404; margin: 0.5rem 0;">
                        Install our desktop application for full scanner access:
                    </p>
                    <ul style="color: #856404; margin: 0.5rem 0;">
                        <li>✅ Windows: WIA/TWAIN support</li>
                        <li>✅ macOS: ICA support</li>
                        <li>✅ Linux: SANE support</li>
                        <li>✅ Cross-platform: pyinsane2</li>
                    </ul>
                </div>
                <div style="margin: 1rem 0;">
                    <h5 style="color: #856404;">Option 2: Web TWAIN Agent</h5>
                    <p style="color: #856404; margin: 0.5rem 0;">
                        Install Dynamsoft Web TWAIN for browser-based scanning:
                    </p>
                    <a href="https://www.dynamsoft.com/web-twain/downloads" target="_blank" 
                       style="background: #007bff; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 5px; display: inline-block;">
                        📥 Download Web TWAIN SDK
                    </a>
                </div>
            </div>
        `;
        optionsDiv.style.display = 'block';
    }

    /**
     * Get pixel type for TWAIN
     */
    getPixelType(mode) {
        switch (mode) {
            case 'Color': return 2; // RGB
            case 'Grayscale': return 1; // Grayscale
            case 'BlackWhite': return 0; // Black & White
            default: return 2;
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        const statusDiv = document.getElementById('scanner-printer-status');
        if (statusDiv) {
            statusDiv.innerHTML = `
                <div style="color: #721c24; background: #f8d7da; padding: 1rem; border-radius: 5px; border-left: 4px solid #dc3545;">
                    ❌ ${message}
                </div>
            `;
        }
    }

    /**
     * Close modal
     */
    closeModal() {
        const modal = document.getElementById('scanner-printer-modal');
        if (modal) {
            modal.remove();
        }
    }
}

// Global instance
let scannerPrinter = null;
